<template>
  <div v-if="subscription" class="d-flex cursor-pointer">
    <div class="">
    <v-img min-height="100" min-width="100" max-height="100" max-width="100" :src="subscription.premiumServiceUrl">
    </v-img>
    </div>
    <div class="ms-7">
      <div class="d-flex align-items-center">
        <v-chip v-if="subscription.isTrailing" class="text-white" color="green" small label>
          体験中
        </v-chip>
        <v-chip v-if="subscription.isStoped" class="text-white" color="red" small label>
          キャンセル済み
        </v-chip>
        <v-chip v-if="subscription.isDone" class="text-white" color="green" small label>
          アクティブ
        </v-chip>
        <v-chip v-if="subscription.isPending" class="text-white" color="red" small label>
          未払い
        </v-chip>
        <div class="text-blue ms-2 font-bold">{{ subscription.premiumServiceName }}</div>
      </div>
      <div class="mt-2 text-gray" v-html="subscription.premiumServiceDescription" />
      <div class="mt-2 font-bold">
        <!-- Monthly subscription -->
        <template v-if="subscription.isEachMonth">
          定期購入
          {{ (subscription.premiumServicePrice || subscription.price || 0).toLocaleString() }}円/月
          <span v-if="nextPaymentDate">次回 {{ nextPaymentDate }}</span>
        </template>

        <!-- Life time with installment -->
        <template v-else-if="subscription.isLifeTime && subscription.hasInstallment">
          分割払い（{{ subscription.installmentCountCurrent}} / {{subscription.installmentCountTotal }}）
          {{ (subscription.purchasePrice || 0).toLocaleString() }}円
          <span v-if="nextPaymentDate">次回 {{ nextPaymentDate }}</span>
        </template>

        <!-- Life time without installment -->
        <template v-else-if="subscription.isLifeTime">
          一括購入
          <span class="price-spacer"></span>
          {{ subscription.pricePlan }}
          <span class="price-spacer"></span>
          {{ (subscription.purchasePrice || 0).toLocaleString() }}円
        </template>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
  import {
    format,
  } from 'date-fns'
  import Vue from 'vue'
  import {
    Component,
    Prop
  } from 'vue-property-decorator'
  import Subscription, { StripeUpcomingInvoice } from '../../../api/v2/models/subscription'

  @Component({})
  export default class SubscriptionComponent extends Vue {
    @Prop({
      required: true
    })
    subscription: Subscription

    @Prop({
      required: false,
      default: null
    })
    stripeUpcomingInvoice: StripeUpcomingInvoice | null

    get nextPaymentDate() {
      if (this.stripeUpcomingInvoice && this.stripeUpcomingInvoice.nextPaymentAttempt) {
        return this.dateFormat(this.stripeUpcomingInvoice.nextPaymentAttempt)
      }
      return null
    }

    dateFormat(date: Date) {
      return format(date, 'YYYY/MM/DD')
    }

    mounted() {
      console.log('Subscription type:', {
        type: this.subscription.purchaseType,
        isLifeTime: this.subscription.isLifeTime,
        isEachMonth: this.subscription.isEachMonth
      });
    }
  }
</script>

<style lang="scss" scoped>
  .price-spacer {
    padding-right: 25px;
  }
</style>
