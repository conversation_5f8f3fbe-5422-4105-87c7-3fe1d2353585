<template>
  <div v-if="coursePurchase" class="d-flex cursor-pointer">
    <div class="">
    <v-img min-height="100" min-width="100" max-height="100" max-width="100" :src="coursePurchase.courseUrl">
    </v-img>
    </div>
    <div class="ms-7">
      <div class="d-flex align-items-center">
        <v-chip v-if="coursePurchase.isStoped" class="text-white" color="red" small label>
          終了
        </v-chip>
        <v-chip v-if="coursePurchase.isDone" class="text-white" color="green" small label>
          アクティブ
        </v-chip>
        <v-chip v-if="coursePurchase.isPending" class="text-white" color="red" small label>
          未払い
        </v-chip>
        <div class="text-blue ms-2 font-bold">{{ coursePurchase.courseName }}</div>
      </div>
      <div class="mt-2 text-gray white-space-pre-line" v-html="coursePurchase.courseDescription" />
      <div class="mt-2 font-bold">
        <template v-if="coursePurchase.hasInstallment">
          分割払い（{{ coursePurchase.installmentCountCurrent}} / {{coursePurchase.installmentCountTotal }}）
          {{ coursePurchase.paiedPrice ? coursePurchase.paiedPrice.toLocaleString() : 0 }}円 <span v-if="coursePurchase.nextPay">次回 {{ dateFormat(coursePurchase.nextPay) }}</span>
        </template>
        <template v-if="!coursePurchase.hasInstallment">
          一括購入
          {{ coursePurchase.paiedPrice ? coursePurchase.paiedPrice.toLocaleString() : 0 }}円
        </template>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
  import {
    format,
  } from 'date-fns'
  import Vue from 'vue'
  import {
    Component,
    Prop
  } from 'vue-property-decorator'
  import CoursePurchase from '../../../api/v2/models/course_purchase'

  @Component({})
  export default class CoursePurchaseComponent extends Vue {
    @Prop({
      required: true
    })
    coursePurchase: CoursePurchase

    dateFormat(date: Date) {
      return format(date, 'YYYY/MM/DD')
    }
  }
</script>

<style lang="scss" scoped></style>