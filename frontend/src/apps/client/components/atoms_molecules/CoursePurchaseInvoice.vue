<template>
  <div class="d-flex px-4">
    <div class="w-25 p-1"> 
      {{ date }}
    </div>
    <div class="w-25 p-1">{{ coursePurchaseInvoice.amountPaid.toLocaleString() }}円</div>
    <div  class="w-25 p-1" :style="{ minWidth: '100px' }">
      <v-chip class="me-2 text-white" color="blue" small label>
        支払い済み
      </v-chip>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Component, Prop } from 'vue-property-decorator'
import { CoursePurchaseInvoice } from '../../../api/v2/models/course_purchase'
import { format } from 'date-fns'

@Component({})
export default class CoursePurchaseInvoiceComponent extends Vue {
  @Prop({ required: true })
  coursePurchaseInvoice: CoursePurchaseInvoice

  get date() {
    return format(this.coursePurchaseInvoice.paiedAt, 'YYYY/MM/DD')
  }
}
</script>

<style lang="scss" scoped></style>
