<template>
  <div fluid class="container-fix-padding">
    <v-col lg="12">
      <v-breadcrumbs :items="breadcrumbs" class="p-0"></v-breadcrumbs>
    </v-col>
    <div class="d-flex">
      <v-col lg="9" class="p-4 bg-white" >
      <p v-if="errorMessage" class="text-danger">{{ errorMessage }}</p>
      <CoursePurchaseComponent :coursePurchase="coursePurchase" />

      <div v-if="coursePurchase && coursePurchase.needUpdateCard" class="m-2 d-flex justify-end">
        <StripeCardPayComponent
          :cardComplete="onPay"
          :title="user.currentSchoolName"
          :description="'分割払いの更新'"
          :mail="user.email"
          :purchasePrice="lastCoursePurchaseInvoice ? lastCoursePurchaseInvoice.amountPaid : 0"
        />
      </div>

      <div class="bg-white border-default py-1 mt-2">
        <div class="d-flex px-4">
          <div class="w-25 p-1"> 
            お支払い日時
          </div>
          <div class="w-25 p-1">
            お支払い金額
          </div>
          <div class="w-25 p-1">
            ステータス
          </div>
          <div class="w-25 p-1">
          </div>
        </div>
        <v-divider class="my-2"/>
        <CoursePurchaseInvoiceComponent
          class="mt-1"
          v-for="(coursePurchaseInvoice, index) in coursePurchaseInvoices"
          :key="index"
          :coursePurchaseInvoice="coursePurchaseInvoice"
        />
      </div>
      </v-col>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Component, Watch } from 'vue-property-decorator'
import { checkMobile } from '../../utils/utils'
import CoursePurchase, {
  CoursePurchaseInvoice
} from '../../../api/v2/models/course_purchase'
import CoursePurchaseForm from '../../../api/v2/forms/course_purchase'
import { coursePurchaseState } from '../../stores/course_purchase'
import { userState } from '../../stores/user'
import { headerState } from '../../stores/header'
import { footerState } from '../../stores/footer'
import CoursePurchaseComponent from '../atoms_molecules/CoursePurchase.vue'
import CoursePurchaseInvoiceComponent from '../atoms_molecules/CoursePurchaseInvoice.vue'
import StripeCardPayComponent from '../atoms_molecules/StripeCardPay.vue'
import Enrollment from '../../../api/v2/models/enrollment'
import CourseMy from '../atoms_molecules/CourseMy.vue'

@Component({
  components: {
    CoursePurchaseComponent,
    CoursePurchaseInvoiceComponent,
    StripeCardPayComponent,
    CourseMy,
  }
})
export default class CoursePurchaseDetail extends Vue {
  mobile: boolean = false

  tab: 1 | 0 = 0

  get user() {
    return userState.user
  }
  get isLoading() {
    return coursePurchaseState.isLoading
  }
  get errorMessage() {
    return coursePurchaseState.errorMessage
  }

  breadcrumbs = [{
      text: 'クラスルーム',
      disabled: false,
      exact: true,
      to: '/classroom/'
    },
    {
      text: '購入済み',
      disabled: false,
      exact: true,
      to: '/classroom/subscription'
    }
  ]

  coursePurchase: CoursePurchase | null = null

  coursePurchaseInvoices: CoursePurchaseInvoice[] = []

  created() {
    this.mobile = checkMobile()
    headerState.setTitle('購入済み')
    footerState.setFooterDefault()
    this.getCoursePurchase()
  }

  getCoursePurchase() {
    coursePurchaseState.getCoursePurchase(this.$route.params.coursePurchaseId as any).then((response) => {
      if (response) {
        this.coursePurchase = response.coursePurchase
        this.coursePurchaseInvoices = response.coursePurchaseInvoices
      }
    })
  }

  get lastCoursePurchaseInvoice() {
    return this.coursePurchaseInvoices[0]
  }

  async onPay(tokenId: string) {
    const coursePurchaseForm = new CoursePurchaseForm()
    coursePurchaseForm.coursePurchaseId = this.coursePurchase.id
    coursePurchaseForm.token = tokenId
    coursePurchaseForm.email = this.user.email
    await coursePurchaseState.updateCoursePurchase({
      coursePurchaseForm
    })
    this.getCoursePurchase()
    userState.getUser()
  }
}
</script>
