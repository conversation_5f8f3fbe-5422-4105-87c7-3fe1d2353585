<template>
  <div fluid class="container-fix-padding">
    <v-col lg="12">
      <v-breadcrumbs :items="breadcrumbs" class="p-0"></v-breadcrumbs>
    </v-col>
    <v-col lg="9" class="pt-3 bg-white" v-if="infor">
      <div class="d-flex justify-space-between align-center">
        <div class="d-flex align-center">
          <ButtonBack @click.native="back" />
          <div class="ms-2"> 
            <div class="text-large">{{ infor.title }}</div>
            <div class="text-small">{{ dateFormat(infor.createdAt) }}</div>
          </div>
        </div>
        <div>
          <div class="text-small ms-2 border-default p-1 text-blue cursor-pointer" @click="unread">
            未読に戻す
          </div>
        </div>
      </div>
      <v-divider />
      <div class="info-content">
        <p class="my-3 html-body" v-html="infor.content"></p>
      </div>
    </v-col>
  </div>
</template>

<style lang="scss">
</style>

<script lang="ts">
import Vue from 'vue'
import { Component } from 'vue-property-decorator'
import Infor from '../../../api/v2/models/information'
import api from '../../../api/v2/api'
import { headerState } from '../../stores/header'
import { footerState } from '../../stores/footer'
import ButtonBack from '../atoms_molecules/ButtonBack.vue'
import { format } from 'date-fns'
import { ROUTER_NAME } from '../../router'
import { snackBarState } from '../../stores/snackbar'
import {SnackBar} from "../../models/other_models";

@Component({
  components: {
    ButtonBack,
  }
})
export default class InforList extends Vue {
  infor: Infor
  breadcrumbs = [
    {
      text: 'クラスルーム',
      disabled: false,
      exact: true,
      to: '/classroom/'
    },
    {
      text: 'お知らせ一覧',
      disabled: false,
      exact: true,
      to: '/classroom/infors'
    }
  ]

  created() {
    headerState.setTitle('お知らせ')
    footerState.setFooterDefault()
    api.infor.show(this.$route.params.inforId).then((res) => {
      this.infor = res
      this.breadcrumbs.push({
        text: this.infor.title,
        disabled: true,
        exact: true,
        to: ""
      })
    }).catch((err) => {
      snackBarState.snackbar = new SnackBar(true, "このページは非公開です", err.apiStatus)
      this.$router.push({
        name: ROUTER_NAME.INFOR_LIST
      })
    })
  }

  dateFormat(date: Date) {
    return format(date, 'YYYY/MM/DD HH:mm')
  }

  async unread(){
    await api.infor.unread(this.infor.id.toString())
    this.$router.push({
      name: ROUTER_NAME.INFOR_LIST
    })
  }

  back(){
    this.$router.push({
      name: ROUTER_NAME.INFOR_LIST
    })
  }
}
</script>
