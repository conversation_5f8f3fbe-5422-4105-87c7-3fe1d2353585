<template>
  <div fluid class="container-fix-padding">
    <v-col lg="12">
      <v-breadcrumbs :items="breadcrumbs" class="p-0"></v-breadcrumbs>
    </v-col>
    <div class="d-flex">
      <v-col lg="12" class="p-4 bg-white" >
      <p v-if="errorMessage" class="text-danger">{{ errorMessage }}</p>
      <SubscriptionComponent :subscription="subscription" :stripeUpcomingInvoice="stripeUpcomingInvoice" />

      <v-tabs v-model="tab">
        <v-tab :key="'HISTORY'">支払い</v-tab>
        <v-tab :key="'CHANGE_PLAN'" v-if="stripeUpcomingInvoice">プラン変更</v-tab>
      </v-tabs>
      <v-divider class="mt-2"/>
      <v-tabs-items v-model="tab">
        <v-tab-item
          :key="'HISTORY'"
        >
          <v-alert
            v-if="stripeInvoices.some((stripeInvoice) => stripeInvoice.status === 'open')"
            dense
            border="left"
            type="warning"
          >
            あなたには未完了の取引があります。
            <v-btn
              color="primary"
              dark
              @click="openNewTab(stripeInvoices.find((stripeInvoice) => stripeInvoice.status === 'open').hostedInvoiceUrl)"
            >
              支払いを確定する
            </v-btn>
          </v-alert>

          <div v-show="!subscription.isStoped">
            <div class="d-flex justify-end">
              <div class="mr-4">
                <p class="fw-bold mb-0">{{ cardNumber() }}</p>
                <p class="text-secondary small mb-0">有効期限: {{ cardDate() }}</p>
              </div>
              <StripeElementCardPayComponent
                v-show="user"
                :title="user && user.currentSchoolName"
                :description="'カードを変更する'"
                :mail="user && user.email"
                :user_id="user.id"
                :premiumServiceId="subscription.premiumServiceId"
                :changeFromSubscriptionId="subscription.id"
                :type_change="'not_default'"
                @card-details="updateCardDetails"
              />
            </div>
          </div>

            <div class="bg-white border-default py-1 mt-2">
              <div class="d-flex px-4">
                <div class="w-25 p-1">
                  お支払い日時
                </div>
                <div class="w-25 p-1">
                  お支払い金額
                </div>
                <div class="w-25 p-1">
                  ステータス
                </div>
                <div class="w-25 p-1"></div>
              </div>
              <v-divider class="my-2" />
              <div class="d-flex px-4" v-if="subscription && subscription.endDate">
                <div class="w-25 p-1">
                  {{ dateFormat(beforeOneDay(subscription.endDate)) }}
                </div>
                <div class="w-25 p-1">0円</div>
                <div class="w-25 p-1" :style="{ minWidth: '100px' }">
                  <v-chip class="me-2 text-white" color="red" small label>
                    キャンセル済み
                  </v-chip>
                </div>
                <div class="w-25 p-1">
                  {{ dateFormat(subscription.endDate) }}
                  まで有効
                </div>
              </div>

              <div v-if="subscription && subscription.isEachMonth">
                <StripeUpcomingInvoiceComponent
                  v-if="stripeUpcomingInvoice"
                  :stripeUpcomingInvoice="stripeUpcomingInvoice"
                  @cancel="onCancelPay"
                />
                <v-divider class="my-2" />
                <StripeInvoiceComponent
                  class="mt-1"
                  v-for="(stripeInvoice, index) in stripeInvoices"
                  :key="index"
                  :stripeInvoice="stripeInvoice"
                  :show-membership-fee="subscription.needMembershipFee"
                  :membership-fee="subscription.membershipFee"
                  :membership-fee-paid-at="subscription.membershipFeePaidAt"
                  :membership-fee-charge-at="subscription.membershipFeeChargeAt"
                  :membership-fee-charge-failed="subscription.membershipFeeChargeFailed"
                  :membership-fee-charge-failed-message="subscription.membershipFeeChargeFailedMessage"
                  :is-last-invoice="index === stripeInvoices.length - 1"
                  :need-membership-fee="subscription.needMembershipFee"
                  :stripeUpcomingInvoice="stripeUpcomingInvoice"
                  :membership-status="membershipStatus"
                />
              </div>
              <div v-else>
                <SubscriptionInvoiceComponent
                  class="mt-1"
                  v-for="(subscriptionInvoice, index) in subscriptionInvoices"
                  :key="index"
                  :subscriptionInvoice="subscriptionInvoice"
                />
              </div>
            </div>
          </v-tab-item>
          <v-tab-item :key="'CHANGE_PLAN'">
            <ChangePlan :subscription="subscription" />
          </v-tab-item>
        </v-tabs-items>
      </v-col>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Component, Watch } from 'vue-property-decorator'
import { checkMobile } from '../../utils/utils'
import Subscription, {
  StripeInvoice,
  StripeUpcomingInvoice,
  SubscriptionInvoice
} from '../../../api/v2/models/subscription'
import SubscriptionForm from '../../../api/v2/forms/subscription'
import { subscriptionState } from '../../stores/subscription'
import { userState } from '../../stores/user'
import { headerState } from '../../stores/header'
import { footerState } from '../../stores/footer'
import SubscriptionComponent from '../atoms_molecules/Subscription.vue'
import StripeInvoiceComponent from '../atoms_molecules/StripeInvoice.vue'
import StripeUpcomingInvoiceComponent from '../atoms_molecules/StripeUpcomingInvoice.vue'
import SubscriptionInvoiceComponent from '../atoms_molecules/SubscriptionInvoice.vue'
import StripeCardPayComponent from '../atoms_molecules/StripeCardPay.vue'
import StripeElementCardPayComponent from '../atoms_molecules/StripeElementCardPay.vue'
import CourseMy from '../atoms_molecules/CourseMy.vue'
import DialogDeleteSubscriptionConfirm from '../atoms_molecules/DialogDeleteSubscriptionConfirm.vue'
import { format } from 'date-fns'
import ChangePlan from '../pages/subscription/ChangePlan.vue'
import StripeToken from '../../../api/v2/models/stripe_token'

@Component({
  components: {
    DialogDeleteSubscriptionConfirm,
    SubscriptionComponent,
    StripeInvoiceComponent,
    StripeUpcomingInvoiceComponent,
    SubscriptionInvoiceComponent,
    StripeCardPayComponent,
    StripeElementCardPayComponent,
    CourseMy,
    ChangePlan,
  }
})
export default class SubscriptionDetail extends Vue {
  mobile: boolean = false

  tab: 1 | 0 = 0

  cardLast4: string = '';
  cardExpMonth: number | null = null;
  cardExpYear: number | null = null;
  stripeToken: StripeToken | null = null

  get user() {
    return userState.user
  }
  get isLoading() {
    return subscriptionState.isLoading
  }
  get errorMessage() {
    return subscriptionState.errorMessage
  }

  breadcrumbs = [
    {
      text: 'クラスルーム',
      disabled: false,
      exact: true,
      to: '/classroom/'
    },
    {
      text: '購入済み',
      disabled: false,
      exact: true,
      to: '/classroom/subscription'
    }
  ]

  subscription: Subscription | null = null

  stripeUpcomingInvoice: StripeUpcomingInvoice | null = null
  stripeInvoices: StripeInvoice[] = []
  subscriptionInvoices: SubscriptionInvoice[] = []
  membershipStatus: string = ''

  @Watch('$router', { immediate: true })
  watchRouter() {
    if (this.$route.query['tab']) {
      var value = 'COURSE'
      if (Array.isArray(this.$route.query['tab'])) {
        value = this.$route.query['tab'][0] as any
      } else {
        value = this.$route.query['tab'] as any
      }

      this.tab = value == 'HISTORY' ? 0 : 1
    }
  }

  @Watch('tab')
  watchTab() {
    this.$router.push({
      query: {
        tab: this.tab == 0 ? 'HISTORY' : 'CHANGE_PLAN'
      }
    })
  }

  @Watch('subscription')
  onSubscriptionChanged() {
    if (this.subscription) {
      // this.userEmail = this.user.email || '';
      // this.userName = this.user.name || 'User Name';
      // this.totalAmount = this.subscription.price || 0;
      // this.premiumServiceId = this.subscription.premiumServiceId?.toString() || '';
      // Update other fields as needed
    }
  }

  async created() {
    this.mobile = checkMobile()
    headerState.setTitle('購入済み')
    footerState.setFooterDefault()
    await this.getSubscription()
    this.membershipStatus = this.getMembershipStatus(this.stripeInvoices)
  }

  async getSubscription() {
    const response = await subscriptionState.getSubscription(this.$route.params.subscriptionId as any)
    if (response) {
      this.subscription = response.subscription
      this.stripeUpcomingInvoice = response.stripeUpcomingInvoice
      this.stripeInvoices = response.stripeInvoices
      this.subscriptionInvoices = response.subscriptionInvoices
    }
  }

  get lastSubscriptionInvoice() {
    return this.subscriptionInvoices[0]
  }

  async onPay(tokenId: string) {
    const subscriptionForm = new SubscriptionForm()
    subscriptionForm.subscriptionId = this.subscription.id
    subscriptionForm.token = tokenId
    subscriptionForm.email = this.user.email
    await subscriptionState.updateSubscription({
      subscriptionForm
    })
    this.getSubscription()
    userState.getUser()
  }
  async onCancelPay(message: string = '') {
    await subscriptionState.cancelSubscription({
      subscriptionId: this.subscription.id,
      message
    })
    this.getSubscription()
  }

  dateFormat(date: Date) {
    return format(date, 'YYYY/MM/DD')
  }

  beforeOneDay(date: Date) {
    return new Date(date.getTime() + 24 * 60 * 60 * 1000)
  }

  openNewTab(url): void {
    window.open(url, '_blank')
  }

  getMembershipStatus(stripeInvoices: StripeInvoice[]) {
    let flagMembershipFeePaid = false
    stripeInvoices.forEach((stripeInvoice) => {
      if (stripeInvoice.amountPaid > 0) {
        let membershipFeeLine: any = stripeInvoice.lines.data.find((line: any) => line.description?.includes('Membership Fee'))
        if (membershipFeeLine) {
          flagMembershipFeePaid = true
        }
      }
    })

    if (flagMembershipFeePaid) {
      return 'paid'
    } else {
      return ''
    }
  }

  handlePaymentComplete(paymentIntentId: string) {
    console.log('Payment successful with payment intent:', paymentIntentId);

    // Use the existing onPay method with the payment intent ID
    // this.onPay(paymentIntentId);
  }

  cardNumber() {
    if (this.cardLast4) {
      return `**** **** **** ${this.cardLast4}`;
    } else if (this.stripeToken) {
      return `**** **** **** ${this.stripeToken.last4}`
    }
    return `**** **** **** ${this.subscription && this.subscription.stripeCustomerDefaultCardNumber}`
  }

  cardDate() {
    if (this.cardExpMonth && this.cardExpYear) {
      const month = String(this.cardExpMonth).padStart(2, '0');
      const year = String(this.cardExpYear)
      return `${month}/${year}`;
    } else if (this.stripeToken) {
      return `${this.stripeToken.expMonth}/${this.stripeToken.expYear}`
    }
    return this.subscription && this.subscription.stripeCustomerDefaultCardExpiredAt
  }

  updateCardDetails(cardDetails) {
    this.cardLast4 = cardDetails.last4;
    this.cardExpMonth = cardDetails.exp_month;
    this.cardExpYear = cardDetails.exp_year;
  }
}
</script>
