<template>
  <div>
    <div class="p-2" v-if="user && userOnboard">
      <div class="row">
        <div class="col-md-8 white-back">
          <div v-if="state === 'welcome'">
            <div v-html="userOnboard.welcomeBody" />
            <div class="w-100 d-flex justify-content-center my-5">
              <button type="button" class="btn btn-dark rounded-pill btn-lg" @click="onGotoCourse">次へ</button>
            </div>
          </div>
          <div v-if="state === 'course'">
            <div class="cursor-pointer font-bold" @click="onGotoWelcome">＜ 戻る</div>
            <h3 class="my-2">
              コースを選択しましょう
            </h3>
            <div>
              <div v-for="(item, index) in userOnboard.courses" class="form-check d-flex align-items-center">
                <input :style="{
                  width: '30px',
                  height: '30px'
                }" v-model="item.selected" class="form-check-input flex-shrink-0" type="checkbox" :id="`checkbox_id_${item.id}`"
                  :value="item.id">
                <label class="form-check-label d-flex p-1" :for="`checkbox_id_${item.id}`">
                  <div class="p-1 flex-shrink-0">
                    <v-img v-if="item.image" min-height="100" min-width="100" max-height="100" max-width="100"
                      :src="item.image.url"></v-img>
                  </div>
                  <div>{{ item.name }}</div>
                </label>
              </div>
            </div>
            <div class="w-100 d-flex justify-content-center my-5">
              <button type="button" class="btn btn-dark rounded-pill btn-lg" @click="onGotoForm">次へ</button>
            </div>
          </div>
          <div v-if="state === 'contact_form'">
            <div class="cursor-pointer font-bold" @click="onGotoCourse">＜ 戻る</div>
            <div class="text-center" v-html="userOnboard.bodyHtml" />
          </div>
        </div>
      </div>
    </div>
    <portal to="header-left-drawer">
      <v-navigation-drawer dark fixed v-model="drawer" app>
        <v-toolbar class="transparent">
          <v-list class="p-0">
            <v-list-item @click.native="goToHome" class="home-header">
              <v-list-item-avatar>
                <img :src="schoolSetting.imageUrl" v-if="schoolSetting && schoolSetting.imageUrl" />
              </v-list-item-avatar>
              <v-list-item-content>
                <v-list-item-title v-if="schoolSetting && schoolSetting.name">{{
                  schoolSetting.name
                }}</v-list-item-title>
              </v-list-item-content>
            </v-list-item>
          </v-list>
        </v-toolbar>
        <v-list class="pt-0 custom-links-v-list" dense>
          <v-list-item to="/classroom/on-boarding">
            <v-list-item-action>
              <v-icon>home</v-icon>
            </v-list-item-action>
            <v-list-item-content>
              <v-list-item-title>
                オンボーディング
              </v-list-item-title>
            </v-list-item-content>
          </v-list-item>
        </v-list>
      </v-navigation-drawer>
    </portal>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Component, Watch } from 'vue-property-decorator'
import { headerState } from '../../stores/header'
import { userState } from '../../stores/user'
import { ROUTER_NAME } from '../../router'
import api from '../../../api/v2/api'
import { snackBarState } from '../../stores/snackbar'
import { LETTER_FORMAT } from '../../utils/constants'
import { checkMobile } from '../../utils/utils'
import { UserOnboardType } from '../../../api/v2/models/user'

@Component({
  components: {
  }
})
export default class OnBoarding extends Vue {
  LETTER_FORMAT = LETTER_FORMAT
  mobile: boolean = checkMobile()

  drawer = true

  get schoolSetting() {
    return headerState.schoolSetting
  }
  get user() {
    return userState.user
  }
  get userOnboard() {
    return this.previewUserOnboard ?? this.user.userOnboard
  }
  state: 'welcome' | 'course' | 'contact_form' = 'welcome'
  async created() {
    this.drawer = !this.mobile
    headerState.setTitle('マイプロファイル')
    headerState.getSchoolSetting()

    if (this.previewCode) {
      this.previewUserOnboard = await api.userOnboard.index({ previewCode: this.previewCode })
    }

    this.state = this.$route.hash ?  this.$route.hash.replace("#", "") : 'welcome' as any
  }

  previewUserOnboard: UserOnboardType | null = null

  @Watch('user')
  watchUser() {
    if (!this.userOnboard && !this.previewCode) {
      this.$router.push({
        name: ROUTER_NAME.HOME
      })
    }
  }

  get previewCode() {
    return this.$route.query['preview_code'] as string ?? ''
  }

  onGotoWelcome() {
    this.state = 'welcome'
    this.goToState()
  }

  onGotoCourse() {
    this.state = 'course'
    this.goToState()
  }

  onGotoForm() {
    this.userOnboard.courses.forEach(item => {
      if (item.selected) {
        api.enrollment.create({
          user_id: this.user.id,
          course_id: item.id
        })
      }
    });

    (window as any).onContactFormCheckInputSuccess = () => {
      // Wait submit complete
      userState.isLoading = true
      setTimeout(async () => {
        if (this.previewUserOnboard) {
          this.$router.push({
            name: ROUTER_NAME.HOME
          })
          snackBarState.setPushSnackbar('オンボーディングプレビューが完了しました。');
        } else {
          await userState.getUser()
          this.previewUserOnboard = null
          this.$router.push({
            query: {}
          })
          if (!this.userOnboard) {
            snackBarState.setPushSnackbar('ありがとございました');
          }
        }
      }, 2000);
    }

    this.state = 'contact_form'
    this.goToState()
  }

  goToState(){
    this.$router.push({
      hash: this.state,
      query: this.$route.query
    })
  }

  goToHome() {
    this.$router.push({
      name: ROUTER_NAME.HOME
    })
  }
}
</script>
