<template>
  <div class="header-main">
    <v-app-bar :color="headerNavColor" dark fixed app clipped-right :class="{ 'header-mobile': mobile }">
      <portal-target name="header-left">
        <div class="d-flex align-center minus-left">
          <template v-if="user && user != null && user.isGuestExamOnly">
            <v-btn class="mk-0" icon @click="toExamShow">
              <v-icon>keyboard_arrow_left</v-icon>
            </v-btn>
            <v-toolbar-title :class="{ 'ps-0': mobile }" :style="{'color': headerNavTextColor}">
              {{ headerTitle }}
            </v-toolbar-title>
          </template>
          <template v-else>
            <v-app-bar-nav-icon @click.stop="drawer = !drawer" :style="hamburgerColor"></v-app-bar-nav-icon>
            <template v-if="mobile"><span :style="{ 'color': headerNavTextColor }">{{ pageTitle }}</span></template>
            <v-toolbar-title :class="{ 'm-0': true, 'ps-0': mobile }" class="header-title">
              <div class="d-flex align-center ">
                <div v-if="!mobile" class="d-flex ms-4">
                  <template v-for="menu in menus">
                    <template v-if="menu.kbn == 'MENU_KBN_MENU'">
                      <v-list-group :key="menu.url" no-action>
                        <template v-slot:activator>
                          <v-list-item-content :style="{ 'color': headerNavTextColor }">
                            <v-list-item-title class="menu-view-name">
                              {{ menu.viewName }}
                            </v-list-item-title>
                          </v-list-item-content>
                        </template>
                        <div class="sub-menu p-2">
                          <div class="cursor-pointer mt-2" v-for="menuItem in menu.menuItems" :key="menuItem.url"
                            @click="toMenu(menuItem)">
                            <v-list-item-title class="menu-view-name">
                              {{ menuItem.viewName }}
                            </v-list-item-title>
                          </div>
                        </div>
                      </v-list-group>
                    </template>

                    <template v-else>
                      <v-list-item :key="menu.url" @click="toMenu(menu)">
                        <v-list-item-content :style="{ 'color': headerNavTextColor }">
                          <v-list-item-title class="menu-view-name">
                            {{ menu.viewName }}
                          </v-list-item-title>
                        </v-list-item-content>
                      </v-list-item>
                    </template>
                  </template>
                </div>
              </div>
            </v-toolbar-title>
          </template>
        </div>
      </portal-target>
      <v-spacer></v-spacer>
      <portal-target name="header-center">
        <div v-if="mobile" class="d-flex align-center">
          <!-- <ButtonReload @click="reload" /> -->
          <HeaderNotification :colorNotiIcon="headerNavTextColor" />
          <v-btn v-if="cartItemCount > 0" icon @click="goToCart" class="position-relative mx-2" >
            <v-icon :color="headerNavTextColor">mdi-cart</v-icon>
            <span class="cart-badge">{{ cartItemCount }}</span>
          </v-btn>
          <HeaderUser />
        </div>
        <div v-else class="d-flex align-center">
          <HeaderNotification :colorNotiIcon="headerNavTextColor" />
          <v-btn v-if="cartItemCount > 0" icon @click="goToCart" class="position-relative mx-2" >
            <v-icon :color="headerNavTextColor">mdi-cart</v-icon>
            <span class="cart-badge">{{ cartItemCount }}</span>
          </v-btn>
          <HeaderUser />
        </div>
      </portal-target>
      <portal-target name="header-right" />
    </v-app-bar>
    <portal-target name="header-left-drawer">
      <HeaderLeftMenuMobile v-if="mobile" v-model="drawer" />
      <HeaderLeftMenuWeb v-else v-model="drawer" />
    </portal-target>
    <portal-target name="header-right-drawer" />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Component } from 'vue-property-decorator'
import { checkMobile } from '../../utils/utils'
import theme from '../../utils/theme'
import HeaderUser from './HeaderUser.vue'
import HeaderNotification from './HeaderNotification.vue'
import HeaderLeftMenuMobile from './HeaderLeftMenuMobile.vue'
import HeaderLeftMenuWeb from './HeaderLeftMenuWeb.vue'
import ButtonReload from '../atoms_molecules/ButtonReload.vue'
import { userState } from '../../stores/user'
import { headerState } from '../../stores/header'
import { menuState } from '../../stores/menu'
import Menu from '../../../api/v2/models/menu'
import { ROUTER_NAME } from '../../router'
import CartApi from '../../../api/v2/api/cart'

const cartApiInstance = new CartApi()

@Component({
  components: {
    HeaderNotification,
    HeaderUser,
    HeaderLeftMenuMobile,
    HeaderLeftMenuWeb,
    ButtonReload
  }
})
export default class HeaderPageDefault extends Vue {
  ROUTER_NAME = ROUTER_NAME
  mobile: boolean = checkMobile()

  theme = theme
  drawer = true
  right = null
  left = null

  cartItemCount: number = 0
  cartUpdateInterval: number | null = null

  get user() {
    return userState.user
  }

  get schoolSetting() {
    return headerState.schoolSetting
  }

  get headerTitle() {
    return headerState.headerTitle
  }

  get headerNavTextColor() {
    if (this.schoolSetting) {
      return this.schoolSetting.navTextColor
    }
  }

  get hamburgerColor() {
    if (this.schoolSetting) {
      return `color: ${this.schoolSetting.hamburgerColor}`
    } else {
      return "color: white"
    }
  }

  get headerNavColor() {
    if (this.schoolSetting) {
      return this.schoolSetting.navColor
    } else {
      return this.theme.headerBackgroundColor
    }
  }

  async created() {
    this.drawer = !this.mobile
    await headerState.getSchoolSetting()
    this.fetchCartData()

    this.cartUpdateInterval = window.setInterval(() => {
      this.fetchCartData()
    }, 30000) // Check every 30 seconds
  }

  beforeDestroy() {
    if (this.cartUpdateInterval) {
      clearInterval(this.cartUpdateInterval)
    }
  }

  async fetchCartData() {
    this.cartItemCount = await cartApiInstance.getCartItemCount()
  }

  goToCart() {
    window.location.href = '/purchase/cart'
  }

  redirect(router) {
    this.$router.push(router)
  }

  reload() {
    this.$router.go(this.$router.currentRoute as any)
  }

  get menus() {
    return menuState.filteredMenus
  }

  get pageTitle() {
    return this.$route.meta ? this.$route.meta.title : null
  }

  toMenu(menu: Menu) {
    if ((!menu.kbn.includes("LINK") && (menu.url != "")) || menu.isTargetBlank) {
      window.open(menu.url, '_blank')
    } else {
      window.location.href = menu.url
    }
  }

  toExamShow(){
    if (this.user && this.user != null && this.user.isGuestExamOnly) {
      location.href = `/exams/${this.$route.params.examId}`
      return
    }
  }
}
</script>

<style lang="scss" scoped>
.sub-menu {
  position: absolute;
  background-color: rgb(125, 212, 32);
  border-radius: 5px;

  color: white;
}

.menu-view-name {
  font-size: 0.9rem;
}

// Add cart badge styles
.cart-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #FF5252;
  color: white;
  border-radius: 50%;
  min-width: 18px;
  height: 18px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translate(50%, -50%);
  font-weight: bold;
  padding: 2px;
}

.position-relative {
  position: relative;
}
</style>
