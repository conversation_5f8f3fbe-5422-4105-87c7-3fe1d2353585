import api from '../../api/v2/api';
import { ApiError } from '../../api/v2/api/base/response';
import SubscriptionForm from '../../api/v2/forms/subscription';
import Enrollment from '../../api/v2/models/enrollment';
import Subscription, { StripeInvoice, StripeUpcomingInvoice, SubscriptionInvoice } from '../../api/v2/models/subscription';
import PremiumService from '../../api/v2/models/pre_service';

class SubscriptionState {
  isLoading = false
  errorMessage = ""

  async updateSubscription({ subscriptionForm }: { subscriptionForm: SubscriptionForm }){
    try {
      this.isLoading = true
      this.errorMessage = ""
      await api.subscription.patch(subscriptionForm.subscriptionId, subscriptionForm)
      this.isLoading = false
    } catch (error) {
      this.isLoading = false
      this.errorMessage = error instanceof ApiError ? error.messages : "Unknow Error"
    }
  }

  async cancelSubscription({ subscriptionId, message }: { subscriptionId: number, message: string }){
    try {
      this.isLoading = true
      this.errorMessage = ""
      await api.subscription.delete(subscriptionId, message)
      this.isLoading = false
    } catch (error) {
      this.isLoading = false
      this.errorMessage = error instanceof ApiError ? error.messages : "Unknow Error"
    }
  }

  async getSubscription(subscriptionId: number): Promise<{
    subscription: Subscription;
    stripeUpcomingInvoice: StripeUpcomingInvoice;
    stripeInvoices:  StripeInvoice[];
    subscriptionInvoices:  SubscriptionInvoice[];
    enrollments:  Enrollment[];
  } | null>{
    let response = null
    try {
      this.isLoading = true
      this.errorMessage = ""
      response = await api.subscription.show(subscriptionId)
      if (response) {
        this.isLoading = false
      }
    } catch (error) {
      this.isLoading = false
      this.errorMessage = error instanceof ApiError ? error.messages : "Unknow Error"
    }

    return response
  }

  async checkSubscriptionStatus(subscriptionId: number, subscriptionChangePlanId: string) {
    let response = null
    try {
      response = await api.subscription.checkSubscriptionStatus(subscriptionId, subscriptionChangePlanId)
    } catch (error) {
      this.errorMessage = error instanceof ApiError ? error.messages : "Unknow Error"
    }

    return response
  }

  async getTargetPremiumService(subscriptionId: number, targetPremiumServiceId: number): Promise<PremiumService | null>{
    let response = null
    try {
      response = await api.subscription.getTargetPremiumService(subscriptionId, targetPremiumServiceId)
    } catch (error) {
      this.errorMessage = error instanceof ApiError ? error.messages : "Unknow Error"
    }

    return response
  }

  async getPremiumServicesToChangePlan(subscriptionId: number): Promise<PremiumService[] | null>{
    let response = null

    try {
      this.isLoading = true
      this.errorMessage = ""
      response = await api.subscription.getPremiumServicesToChangePlan(subscriptionId)
      if (response) {
        this.isLoading = false
      }
    } catch (error) {
      this.isLoading = false
      this.errorMessage = error instanceof ApiError ? error.messages : "Unknow Error"
    }

    return response
  }
}

export const subscriptionState = new SubscriptionState()
