import api from '../../api/v2/api';
import { ApiError } from '../../api/v2/api/base/response';
import CoursePurchaseForm from '../../api/v2/forms/course_purchase';
import Enrollment from '../../api/v2/models/enrollment';
import CoursePurchase, { CoursePurchaseInvoice } from '../../api/v2/models/course_purchase';

class CoursePurchaseState {
  isLoading = false
  errorMessage = ""

  async updateCoursePurchase({ coursePurchaseForm }: { coursePurchaseForm: CoursePurchaseForm }){
    try {
      this.isLoading = true
      this.errorMessage = ""
      await api.coursePurchase.patch(coursePurchaseForm.coursePurchaseId, coursePurchaseForm)
      this.isLoading = false
    } catch (error) {
      this.isLoading = false
      this.errorMessage = error instanceof ApiError ? error.messages : "Unknow Error"
    }
  }

  async getCoursePurchase(coursePurchaseId: number): Promise<{
    coursePurchase: CoursePurchase;
    coursePurchaseInvoices:  CoursePurchaseInvoice[];
} | null>{
    let response = null
    try {
      this.isLoading = true
      this.errorMessage = ""
      response = await api.coursePurchase.show(coursePurchaseId)
      this.isLoading = false
    } catch (error) {
      this.isLoading = false
      this.errorMessage = error instanceof ApiError ? error.messages : "Unknow Error"
    }

    return response
  }
}

export const coursePurchaseState = new CoursePurchaseState()
