import Image from './image'
export default class Information {
  readonly id: number
  readonly title: string
  readonly content: string
  readonly createdAt: Date
  readonly image: Image
  readonly description: string
  readonly updatedAt: Date
  readonly readed: boolean = false
  readonly showDashboard: boolean = false

  constructor(data: { [key: string]: any }) {
    this.id = data.id
    this.title = data.title
    this.content = data.content
    this.readed = data.readed
    this.description = data.description
    this.createdAt = new Date(data.createdAt)
    this.updatedAt = new Date(data.updatedAt)
    this.showDashboard = data.showDashboard
    if (data.image) {
      this.image = new Image(data.image)
    }
  }
}
