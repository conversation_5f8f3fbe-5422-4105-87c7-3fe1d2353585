import ClassroomMenu from "./classroom_menu"

export default class SchoolSetting {
  readonly name: string
  readonly imageUrl: string
  readonly allowUserEditGroup: boolean = false
  readonly usePremiumService: boolean = false
  readonly useCourseLesson: boolean = false
  readonly useUserQuestion: boolean = false
  readonly useUserDashboard: boolean = false
  readonly useGoal: boolean = false
  readonly useExamStock: boolean = false
  readonly useAiChatLesson: boolean = false
  readonly useAiExamGeneration: boolean = false
  readonly useAiChatGoal: boolean = false
  readonly useAiChatExam: boolean = false
  readonly navColor: string
  readonly navTextColor: string
  readonly hamburgerColor: string
  readonly useExamRecommendation: boolean = false
  readonly useExam: string
  readonly useDashboard: string
  readonly useMail: string
  readonly useSkill: string
  readonly useUserGraph: string
  readonly useBanner: string
  readonly useMeeting: string
  readonly usingDeepSeek: boolean = false
  readonly classroomMenus: ClassroomMenu[] = []
  readonly useEvaluateLesson: boolean = false
  constructor(data: { [key: string]: any }) {
    this.name = data.name
    this.imageUrl = data.imageUrl
    this.allowUserEditGroup = data.allowUserEditGroup
    this.usePremiumService = data.usePremiumService
    this.useCourseLesson = data.useCourseLesson
    this.useUserQuestion = data.useUserQuestion
    this.useUserDashboard = data.useUserDashboard
    this.useGoal = data.useGoal
    this.useExamStock = data.useExamStock
    this.useExamRecommendation = data.useExamRecommendation
    this.navColor = data.navColor
    this.navTextColor = data.navTextColor
    this.hamburgerColor = data.hamburgerColor
    this.useExam = data.useExam
    this.useDashboard = data.useDashboard
    this.useMail = data.useMail
    this.useSkill = data.useSkill
    this.useUserGraph = data.useUserGraph
    this.useBanner = data.useBanner
    this.useMeeting = data.useMeeting
    this.useAiChatLesson = data.useAiChatLesson
    this.useAiExamGeneration = data.useAiExamGeneration
    this.useAiChatGoal = data.useAiChatGoal
    this.useAiChatExam = data.useAiChatExam
    this.usingDeepSeek = data.usingDeepSeek
    this.classroomMenus = data.classroomMenus.map((element: any) => new ClassroomMenu(element))
    // this.classroomMenus = data.classroomMenus
    this.useEvaluateLesson = data.useEvaluateLesson
  }
}
