import { addMonths } from "date-fns"

export default class CoursePurchase {
  static STATUS_PENDING = 1
  static STATUS_DONE = 2
  static STATUS_STOPED = 3
  static STATUS_TRIALING = 4

  static METHOD_CARD = 1
  static METHOD_MANUAL = 2


  static FORCE_STATUS_NONE = 0
  static FORCE_STATUS_START = 1
  static FORCE_STATUS_STOP = 2

  readonly id: number
  readonly purchasePrice: number
  readonly paiedPrice: number
  readonly status: number
  readonly createdAt: Date
  readonly paiedAt: Date | null
  readonly expiredAt: Date | null
  readonly courseId: number
  readonly courseName: string
  readonly courseDescription: string
  readonly courseUrl: string
  readonly installmentCountCurrent: number
  readonly installmentCountTotal: number

  constructor(data: { [key: string]: any }) {
    this.id = data.id
    this.purchasePrice = data.purchasePrice
    this.paiedPrice = data.paiedPrice
    this.status = data.status
    this.createdAt = new Date(data.createdAt)
    this.paiedAt = data.paiedAt ? new Date(data.paiedAt) : null
    this.expiredAt = data.expiredAt ? new Date(data.expiredAt) : null
    this.courseId = data.courseId
    this.courseName = data.courseName
    this.courseDescription = data.courseDescription
    this.courseUrl = data.courseUrl
    this.installmentCountCurrent = data.installmentCountCurrent
    this.installmentCountTotal = data.installmentCountTotal
  }

  public get isPending(): boolean {
    return this.status === CoursePurchase.STATUS_PENDING
  }

  public get isDone(): boolean {
    return this.status === CoursePurchase.STATUS_DONE
  }

  public get isStoped(): boolean {
    return this.status === CoursePurchase.STATUS_STOPED
  }

  public get isTrailing(): boolean {
    return this.status === CoursePurchase.STATUS_TRIALING
  }

  public get hasInstallment(): boolean {
    return this.installmentCountTotal > 1
  }

  public get needUpdateCard(){
    return this.hasInstallment && this.isPending
  }

  
  public get nextPay() : Date | null {
    if ((this.installmentCountCurrent < this.installmentCountTotal)) {
      return addMonths(this.paiedAt, 1)
    }
    return null
  }

  public get lifeTimeInvoice(){
    return new CoursePurchaseInvoice({
      id: '',
      amountPaid: this.purchasePrice,
      date: this.paiedAt.getTime() / 1000
    })
  }
}

export class CoursePurchaseInvoice {
  readonly id: string
  readonly amountPaid: number
  readonly paiedAt: Date
  constructor(data: { [key: string]: any }) {
    this.id = data.id
    this.amountPaid = data.amountPaid
    this.paiedAt = new Date(data.paiedAt)
  }
}
