import Image from './image'
import Subscription from './subscription'
import Information from './information'
import CoursePurchase from './course_purchase'

export interface UserOnboardType {
  welcomeBody: string,
  courses: {
    id: number,
    name: string,
    image?: Image,
    selected: boolean
  }[],
  bodyHtml: string
}

export default class User {
  static SUBSCRIPTION_NO = 1
  static SUBSCRIPTION_OK = 1

  readonly id: number
  readonly name: string
  readonly profile: string
  readonly email: string
  readonly currentSchoolId: number
  readonly currentSchoolName: string
  readonly userBlockId: number
  readonly image: Image | undefined
  readonly unconfirmedEmail: string
  readonly subscriptionType: number
  readonly subscriptions: Subscription[] = []
  readonly coursePurchases: CoursePurchase[] = []
  readonly lastExamAt: string
  readonly guestType: string
  readonly isShowGraph: boolean
  readonly premiumCourseIds: number[] = [] 
  readonly hasExamSubscription: boolean
  readonly learnTimeGoalValue: number

  readonly unreadInformations: Information[] = []
  readonly unreadInformationsCount: number
  readonly showDashboardInformation: Information = null
  readonly userOnboard?: UserOnboardType


  constructor(data: {[key: string]: any}) {
    this.id = data.id
    this.name = data.name
    this.profile = data.body
    this.email = data.email
    this.unconfirmedEmail = data.unconfirmedEmail
    this.subscriptionType = data.subscriptionType
    this.currentSchoolId = data.currentSchoolId
    this.currentSchoolName = data.currentSchoolName
    this.userBlockId = data.userBlockId
    this.lastExamAt = data.lastExamAt
    this.isShowGraph = data.isShowGraph
    this.hasExamSubscription = data.hasExamSubscription
    this.guestType = data.guestType
    
    this.premiumCourseIds = data.premiumCourseIds
    this.learnTimeGoalValue = data.learnTimeGoalValue

    this.unreadInformationsCount = data.unreadInformationsCount
    if (data.unreadInformations) {
      this.unreadInformations = data.unreadInformations.map((element) => new Information(element))
    }
    if (data.showDashboardInformation) {
      this.showDashboardInformation = new Information(data.showDashboardInformation)
    }

    if (data.image) {
      this.image = new Image(data.image)
    }
    if (data.subscriptions) {
      this.subscriptions = data.subscriptions.map((element) => new Subscription(element))
    }
    if (data.coursePurchases) {
      this.coursePurchases = data.coursePurchases.map((element) => new CoursePurchase(element))
    }
    this.userOnboard = data.userOnboard ?? undefined
    if (this.userOnboard) {
      this.userOnboard.courses.forEach(item => item.selected = false)
    }
  }

  public get hasActiveSubscription(): boolean {
    return this.latestSubscription && this.latestSubscription.isActive
  }

  public get latestSubscription(): Subscription | undefined {
    return this.subscriptions[0]
  }

  public get isUseSubscription(): boolean {
    return this.subscriptionType == User.SUBSCRIPTION_OK
  }

  public get learnTimeGoalMinutes() : number {
    return this.learnTimeGoalValue / 60
  }
  
  public get isGuest() {
    return this.email?.includes("guest_") || this.name?.includes("guest_")
  }
  
  public get isGuestExamOnly() {
    return this.guestType == 'exam_only'
  }
}
