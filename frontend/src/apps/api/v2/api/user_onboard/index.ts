import BaseApi from '../base/base'
import { UserOnboardType } from '../../models/user'

export default class UserOnboardApi extends BaseApi {
  constructor() {
    super()
    this.path = '/user_onboards'
  }

  async index(params: {previewCode?: string} = {}): Promise<UserOnboardType | null> {
    this.path = '/user_onboards'
    const response = await this.getRequest(params)
    return response.data
  }
}
