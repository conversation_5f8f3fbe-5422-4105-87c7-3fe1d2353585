import CoursePurchaseForm from '../../forms/course_purchase'
import CoursePurchase, { CoursePurchaseInvoice } from '../../models/course_purchase'
import BaseApi from '../base/base'

export default class CoursePurchase<PERSON><PERSON> extends BaseApi {
  constructor() {
    super()
    this.path = '/course_purchases'
  }

  async show(id, params={}): Promise<{
    coursePurchase: CoursePurchase;
    coursePurchaseInvoices: CoursePurchaseInvoice[];
}> {
    this.path = `/course_purchases/${id}`
    const response = await this.getRequest(params)
    return {
      coursePurchase: new CoursePurchase(response.data.coursePurchase),
      coursePurchaseInvoices: response.data.coursePurchaseInvoices.map(item => new CoursePurchaseInvoice(item)),
    }
  }

  async patch(coursePurchaseId, coursePurchaseForm: CoursePurchaseForm) {
    this.path = `/course_purchases/${coursePurchaseId}`
    const response = await this.patchRequest(coursePurchaseForm)
    return new CoursePurchase(response.data)
  }

  async show_by_course(courseId, params={}) {
    this.path = `/courses/${courseId}/course_purchases/show_by_course`
    const response = await this.getRequest(params)
    return response.data
  }

  async create(courseId, params) {
    this.path = `/courses/${courseId}/course_purchases/`
    const response = await this.postRequest(params)
    return response.data
  }

  async getCoupon(courseId, params) {
    this.path = `/courses/${courseId}/course_purchases/get_coupon`
    const response = await this.getRequest(params)
    return response.data
  }
}
