import config from '../../../../client/config'
import BaseApi from '../base/base'
import SubscriptionForm from '../../forms/subscription'
import Subscription, { StripeInvoice, StripeUpcomingInvoice, SubscriptionInvoice } from '../../models/subscription'
import PremiumService from '../../models/pre_service'

export default class SubscriptionApi extends BaseApi {
  constructor() {
    super()
    this.path = '/subscriptions'
  }

  async show(
    subscriptionId
  ): Promise<{
    subscription: Subscription
    stripeUpcomingInvoice: StripeUpcomingInvoice
    stripeInvoices: StripeInvoice[]
    subscriptionInvoices: SubscriptionInvoice[]
  }> {
    this.basePath = config.apiPath
    this.version = config.apiVersion
    this.path = `/subscriptions/${subscriptionId}`
    const response = await this.getRequest()
    if (response.data.subscription) {
      return {
        subscription: new Subscription(response.data.subscription),
        stripeUpcomingInvoice: response.data.stripeUpcomingInvoice
          ? new StripeUpcomingInvoice(response.data.stripeUpcomingInvoice)
          : null,
        stripeInvoices: response.data.stripeInvoices.map((item) => new StripeInvoice(item)),
        subscriptionInvoices: response.data.subscriptionInvoices.map((item) => new SubscriptionInvoice(item))
      }
    }
    return null
  }

  async getPremiumServicesToChangePlan(subscriptionId: number): Promise<PremiumService[] | null> {
    this.basePath = config.apiPath
    this.version = config.apiVersion
    this.path = `/subscriptions/${subscriptionId}/change_plan_list`
    const response = await this.getRequest()
    return response.data.map((item) => new PremiumService(item))
  }

  async patch(subscriptionId, subscriptionForm: SubscriptionForm) {
    this.basePath = config.apiPath
    this.version = config.apiVersion
    this.path = `/subscriptions/${subscriptionId}`
    const response = await this.patchRequest(subscriptionForm)
    return new Subscription(response.data)
  }

  async delete(subscriptionId, stopReason = '') {
    this.basePath = config.apiPath
    this.version = config.apiVersion
    this.path = `/subscriptions/${subscriptionId}`
    const response = await this.deleteRequest({
      stopReason
    })
    return new Subscription(response.data)
  }

  async checkSubscriptionStatus(subscriptionId, subscriptionChangePlanId) {
    this.basePath = config.apiPath
    this.version = config.apiVersion
    this.path = `/subscriptions/${subscriptionId}/check_subscription_status?subscription_id=${subscriptionChangePlanId}`
    const response = await this.getRequest()
    return true
  }

  async getTargetPremiumService(
    subscriptionId: number,
    targetPremiumServiceId: number
  ): Promise<PremiumService | null> {
    this.basePath = config.apiPath
    this.version = config.apiVersion
    this.path = `/subscriptions/${subscriptionId}/get_target_premium_service?target_premium_service_id=${targetPremiumServiceId}`
    const response = await this.getRequest()
    return new PremiumService(response.data)
  }

  async create(premiumServiceId: number, payment_method_id: string = null, changeFromSubscriptionId: number = null) {
    this.basePath = ''
    this.version = ''
    this.path = `/premium_services/${premiumServiceId}/subscriptions.json`

    return await this.postRequest({changeFromSubscriptionId, payment_method_id, subscription: { name: "Change plan"} })
  }
}
