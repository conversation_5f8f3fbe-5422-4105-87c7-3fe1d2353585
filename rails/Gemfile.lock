GIT
  remote: https://github.com/BuiDuySon/rails-footnotes.git
  revision: 4a49fe7eeb8bd344a537a68e3aad7a252531b8c4
  specs:
    rails-footnotes (4.1.8)
      rails (>= 3.2)

GIT
  remote: https://github.com/bo-oz/vimeo_me2.git
  revision: 4a6e0fc8d1bb8b8d793d2ff21809358e59f789f9
  specs:
    vimeo_me2 (1.2.1)
      httparty (>= 0.14.0)

GIT
  remote: https://github.com/dat-n/ruby_llm.git
  revision: 046ecf6c02f5a6eb321dc3ae8945eadd395eeb36
  ref: 046ecf6c02f5a6eb321dc3ae8945eadd395eeb36
  specs:
    ruby_llm (1.3.1)
      base64
      event_stream_parser (~> 1)
      faraday (>= 1.10.0)
      faraday-multipart (>= 1)
      faraday-net_http (>= 1)
      faraday-retry (>= 1)
      marcel (~> 1.0)
      zeitwerk (~> 2)

GIT
  remote: https://github.com/randym/axlsx.git
  revision: 776037c0fc799bb09da8c9ea47980bd3bf296874
  ref: 776037c0fc799bb09da8c9ea47980bd3bf296874
  specs:
    axlsx (2.1.0.pre)
      htmlentities (~> 4.3.4)
      mimemagic (~> 0.3)
      nokogiri (>= 1.6.6)
      rubyzip (>= 1.2.1)

GIT
  remote: https://github.com/smartinez87/exception_notification.git
  revision: 11c1e919ea50a1dccc3b2db515152e859c6dbebc
  specs:
    exception_notification (4.5.0)
      actionmailer (>= 5.2, < 8)
      activesupport (>= 5.2, < 8)

GEM
  remote: https://rubygems.org/
  specs:
    Ascii85 (2.0.1)
    RedCloth (4.3.2)
    aasm (5.3.0)
      concurrent-ruby (~> 1.0)
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.0)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      rack (~> 2.0, >= 2.2.0)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actionpack-page_caching (1.2.4)
      actionpack (>= 4.0.0)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    active_model_serializers (0.10.13)
      actionpack (>= 4.1, < 7.1)
      activemodel (>= 4.1, < 7.1)
      case_transform (>= 0.2)
      jsonapi-renderer (>= 0.1.1.beta1, < 0.3)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activemodel-serializers-xml (1.0.2)
      activemodel (> 5.x)
      activesupport (> 5.x)
      builder (~> 3.1)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (*******)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
    acts-as-taggable-on (9.0.1)
      activerecord (>= 6.0, < 7.1)
    addressable (2.8.1)
      public_suffix (>= 2.0.2, < 6.0)
    afm (0.2.2)
    ahoy_matey (4.1.0)
      activesupport (>= 5.2)
      device_detector
      safely_block (>= 0.2.1)
    airbrussh (1.4.1)
      sshkit (>= 1.6.1, != 1.7.0)
    ancestry (4.2.0)
      activerecord (>= 5.2.6)
    anycable (1.2.3)
      anycable-core (= 1.2.3)
      grpc (~> 1.37)
    anycable-core (1.2.3)
      anyway_config (>= 2.1.0)
      google-protobuf (>= 3.13)
    anycable-rails (1.3.4)
      actioncable (>= 6.0)
      anycable (~> 1.2.0)
      globalid
    anyway_config (2.3.0)
      ruby-next-core (>= 0.14.0)
    apexcharts (0.2.0)
    aws-eventstream (1.2.0)
    aws-partitions (1.619.0)
    aws-sdk (3.1.0)
      aws-sdk-resources (~> 3)
    aws-sdk-accessanalyzer (1.29.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-account (1.7.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-acm (1.51.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-acmpca (1.49.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-alexaforbusiness (1.56.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-amplify (1.41.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-amplifybackend (1.17.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-amplifyuibuilder (1.5.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-apigateway (1.78.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-apigatewaymanagementapi (1.30.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-apigatewayv2 (1.42.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-appconfig (1.26.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-appconfigdata (1.5.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-appflow (1.27.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-appintegrationsservice (1.13.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-applicationautoscaling (1.62.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-applicationcostprofiler (1.9.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-applicationdiscoveryservice (1.46.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-applicationinsights (1.31.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-appmesh (1.47.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-appregistry (1.16.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-apprunner (1.14.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-appstream (1.66.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-appsync (1.53.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-athena (1.56.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-auditmanager (1.26.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-augmentedairuntime (1.23.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-autoscaling (1.80.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-autoscalingplans (1.40.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-backup (1.45.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-backupgateway (1.4.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-backupstorage (1.0.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-batch (1.62.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-billingconductor (1.0.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-braket (1.19.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-budgets (1.50.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-chime (1.68.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-chimesdkidentity (1.9.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-chimesdkmediapipelines (1.1.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-chimesdkmeetings (1.14.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-chimesdkmessaging (1.12.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cloud9 (1.45.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cloudcontrolapi (1.8.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-clouddirectory (1.41.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cloudformation (1.70.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cloudfront (1.66.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cloudhsm (1.39.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cloudhsmv2 (1.42.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cloudsearch (1.40.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cloudsearchdomain (1.33.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cloudtrail (1.49.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cloudwatch (1.67.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cloudwatchevents (1.57.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cloudwatchevidently (1.7.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cloudwatchlogs (1.53.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cloudwatchrum (1.4.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-codeartifact (1.21.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-codebuild (1.88.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-codecommit (1.51.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-codedeploy (1.49.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-codeguruprofiler (1.24.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-codegurureviewer (1.31.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-codepipeline (1.53.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-codestar (1.38.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-codestarconnections (1.24.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-codestarnotifications (1.19.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cognitoidentity (1.40.1)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cognitoidentityprovider (1.69.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-cognitosync (1.36.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-comprehend (1.61.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-comprehendmedical (1.36.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-computeoptimizer (1.33.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-configservice (1.82.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-connect (1.74.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-connectcampaignservice (1.1.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-connectcontactlens (1.11.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-connectparticipant (1.22.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-connectwisdomservice (1.8.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-core (3.132.0)
      aws-eventstream (~> 1, >= 1.0.2)
      aws-partitions (~> 1, >= 1.525.0)
      aws-sigv4 (~> 1.1)
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-costandusagereportservice (1.40.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-costexplorer (1.77.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-customerprofiles (1.21.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-databasemigrationservice (1.72.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-dataexchange (1.26.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-datapipeline (1.36.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-datasync (1.49.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-dax (1.39.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-detective (1.29.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-devicefarm (1.51.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-devopsguru (1.24.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-directconnect (1.54.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-directoryservice (1.50.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-dlm (1.51.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-docdb (1.43.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-drs (1.6.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-dynamodb (1.76.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-dynamodbstreams (1.39.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ebs (1.26.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ec2 (1.328.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ec2instanceconnect (1.25.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ecr (1.56.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ecrpublic (1.12.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ecs (1.100.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-efs (1.54.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-eks (1.75.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-elasticache (1.79.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-elasticbeanstalk (1.51.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-elasticinference (1.21.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-elasticloadbalancing (1.40.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-elasticloadbalancingv2 (1.78.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-elasticsearchservice (1.66.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-elastictranscoder (1.38.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-emr (1.62.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-emrcontainers (1.14.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-emrserverless (1.0.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-eventbridge (1.38.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-finspace (1.11.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-finspacedata (1.17.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-firehose (1.48.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-fis (1.13.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-fms (1.50.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-forecastqueryservice (1.21.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-forecastservice (1.36.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-frauddetector (1.34.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-fsx (1.57.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-gamelift (1.57.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-gamesparks (1.1.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-glacier (1.46.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-globalaccelerator (1.40.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-glue (1.118.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-gluedatabrew (1.22.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-greengrass (1.49.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-greengrassv2 (1.18.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-groundstation (1.27.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-guardduty (1.59.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-health (1.47.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-healthlake (1.13.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-honeycode (1.17.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-iam (1.69.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-identitystore (1.17.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-imagebuilder (1.40.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-importexport (1.35.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv2 (~> 1.0)
    aws-sdk-inspector (1.43.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-inspector2 (1.5.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-iot (1.94.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-iot1clickdevicesservice (1.37.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-iot1clickprojects (1.37.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-iotanalytics (1.49.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-iotdataplane (1.39.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-iotdeviceadvisor (1.15.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-iotevents (1.33.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ioteventsdata (1.27.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-iotfleethub (1.11.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-iotjobsdataplane (1.36.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-iotsecuretunneling (1.21.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-iotsitewise (1.43.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-iotthingsgraph (1.23.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-iottwinmaker (1.5.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-iotwireless (1.25.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ivs (1.21.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ivschat (1.2.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-kafka (1.50.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-kafkaconnect (1.7.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-kendra (1.56.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-keyspaces (1.2.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-kinesis (1.41.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-kinesisanalytics (1.40.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-kinesisanalyticsv2 (1.40.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-kinesisvideo (1.42.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-kinesisvideoarchivedmedia (1.44.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-kinesisvideomedia (1.37.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-kinesisvideosignalingchannels (1.19.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-kms (1.58.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-lakeformation (1.27.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-lambda (1.85.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-lambdapreview (1.35.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-lex (1.45.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-lexmodelbuildingservice (1.57.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-lexmodelsv2 (1.25.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-lexruntimev2 (1.15.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-licensemanager (1.40.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-licensemanagerusersubscriptions (1.0.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-lightsail (1.68.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-locationservice (1.23.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-lookoutequipment (1.12.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-lookoutforvision (1.16.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-lookoutmetrics (1.20.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-machinelearning (1.37.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-macie (1.38.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-macie2 (1.46.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-mainframemodernization (1.0.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-managedblockchain (1.32.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-managedgrafana (1.8.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-marketplacecatalog (1.22.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-marketplacecommerceanalytics (1.41.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-marketplaceentitlementservice (1.35.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-marketplacemetering (1.44.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-mediaconnect (1.44.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-mediaconvert (1.92.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-medialive (1.88.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-mediapackage (1.54.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-mediapackagevod (1.36.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-mediastore (1.41.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-mediastoredata (1.38.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-mediatailor (1.55.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-memorydb (1.8.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-mgn (1.14.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-migrationhub (1.40.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-migrationhubconfig (1.20.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-migrationhubrefactorspaces (1.7.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-migrationhubstrategyrecommendations (1.4.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-mobile (1.35.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-mq (1.47.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-mturk (1.40.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-mwaa (1.16.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-neptune (1.46.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-networkfirewall (1.18.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-networkmanager (1.25.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-nimblestudio (1.14.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-opensearchservice (1.11.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-opsworks (1.41.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-opsworkscm (1.52.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-organizations (1.70.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-outposts (1.35.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-panorama (1.7.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-personalize (1.43.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-personalizeevents (1.27.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-personalizeruntime (1.33.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-pi (1.39.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-pinpoint (1.68.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-pinpointemail (1.35.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-pinpointsmsvoice (1.32.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-pinpointsmsvoicev2 (1.0.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-polly (1.57.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-pricing (1.40.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-privatenetworks (1.0.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-prometheusservice (1.15.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-proton (1.16.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-qldb (1.25.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-qldbsession (1.22.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-quicksight (1.67.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ram (1.39.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-rds (1.153.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-rdsdataservice (1.36.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-recyclebin (1.5.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-redshift (1.84.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-redshiftdataapiservice (1.21.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-redshiftserverless (1.3.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-rekognition (1.70.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-resiliencehub (1.5.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-resourcegroups (1.45.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-resourcegroupstaggingapi (1.47.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-resources (3.138.0)
      aws-sdk-accessanalyzer (~> 1)
      aws-sdk-account (~> 1)
      aws-sdk-acm (~> 1)
      aws-sdk-acmpca (~> 1)
      aws-sdk-alexaforbusiness (~> 1)
      aws-sdk-amplify (~> 1)
      aws-sdk-amplifybackend (~> 1)
      aws-sdk-amplifyuibuilder (~> 1)
      aws-sdk-apigateway (~> 1)
      aws-sdk-apigatewaymanagementapi (~> 1)
      aws-sdk-apigatewayv2 (~> 1)
      aws-sdk-appconfig (~> 1)
      aws-sdk-appconfigdata (~> 1)
      aws-sdk-appflow (~> 1)
      aws-sdk-appintegrationsservice (~> 1)
      aws-sdk-applicationautoscaling (~> 1)
      aws-sdk-applicationcostprofiler (~> 1)
      aws-sdk-applicationdiscoveryservice (~> 1)
      aws-sdk-applicationinsights (~> 1)
      aws-sdk-appmesh (~> 1)
      aws-sdk-appregistry (~> 1)
      aws-sdk-apprunner (~> 1)
      aws-sdk-appstream (~> 1)
      aws-sdk-appsync (~> 1)
      aws-sdk-athena (~> 1)
      aws-sdk-auditmanager (~> 1)
      aws-sdk-augmentedairuntime (~> 1)
      aws-sdk-autoscaling (~> 1)
      aws-sdk-autoscalingplans (~> 1)
      aws-sdk-backup (~> 1)
      aws-sdk-backupgateway (~> 1)
      aws-sdk-backupstorage (~> 1)
      aws-sdk-batch (~> 1)
      aws-sdk-billingconductor (~> 1)
      aws-sdk-braket (~> 1)
      aws-sdk-budgets (~> 1)
      aws-sdk-chime (~> 1)
      aws-sdk-chimesdkidentity (~> 1)
      aws-sdk-chimesdkmediapipelines (~> 1)
      aws-sdk-chimesdkmeetings (~> 1)
      aws-sdk-chimesdkmessaging (~> 1)
      aws-sdk-cloud9 (~> 1)
      aws-sdk-cloudcontrolapi (~> 1)
      aws-sdk-clouddirectory (~> 1)
      aws-sdk-cloudformation (~> 1)
      aws-sdk-cloudfront (~> 1)
      aws-sdk-cloudhsm (~> 1)
      aws-sdk-cloudhsmv2 (~> 1)
      aws-sdk-cloudsearch (~> 1)
      aws-sdk-cloudsearchdomain (~> 1)
      aws-sdk-cloudtrail (~> 1)
      aws-sdk-cloudwatch (~> 1)
      aws-sdk-cloudwatchevents (~> 1)
      aws-sdk-cloudwatchevidently (~> 1)
      aws-sdk-cloudwatchlogs (~> 1)
      aws-sdk-cloudwatchrum (~> 1)
      aws-sdk-codeartifact (~> 1)
      aws-sdk-codebuild (~> 1)
      aws-sdk-codecommit (~> 1)
      aws-sdk-codedeploy (~> 1)
      aws-sdk-codeguruprofiler (~> 1)
      aws-sdk-codegurureviewer (~> 1)
      aws-sdk-codepipeline (~> 1)
      aws-sdk-codestar (~> 1)
      aws-sdk-codestarconnections (~> 1)
      aws-sdk-codestarnotifications (~> 1)
      aws-sdk-cognitoidentity (~> 1)
      aws-sdk-cognitoidentityprovider (~> 1)
      aws-sdk-cognitosync (~> 1)
      aws-sdk-comprehend (~> 1)
      aws-sdk-comprehendmedical (~> 1)
      aws-sdk-computeoptimizer (~> 1)
      aws-sdk-configservice (~> 1)
      aws-sdk-connect (~> 1)
      aws-sdk-connectcampaignservice (~> 1)
      aws-sdk-connectcontactlens (~> 1)
      aws-sdk-connectparticipant (~> 1)
      aws-sdk-connectwisdomservice (~> 1)
      aws-sdk-costandusagereportservice (~> 1)
      aws-sdk-costexplorer (~> 1)
      aws-sdk-customerprofiles (~> 1)
      aws-sdk-databasemigrationservice (~> 1)
      aws-sdk-dataexchange (~> 1)
      aws-sdk-datapipeline (~> 1)
      aws-sdk-datasync (~> 1)
      aws-sdk-dax (~> 1)
      aws-sdk-detective (~> 1)
      aws-sdk-devicefarm (~> 1)
      aws-sdk-devopsguru (~> 1)
      aws-sdk-directconnect (~> 1)
      aws-sdk-directoryservice (~> 1)
      aws-sdk-dlm (~> 1)
      aws-sdk-docdb (~> 1)
      aws-sdk-drs (~> 1)
      aws-sdk-dynamodb (~> 1)
      aws-sdk-dynamodbstreams (~> 1)
      aws-sdk-ebs (~> 1)
      aws-sdk-ec2 (~> 1)
      aws-sdk-ec2instanceconnect (~> 1)
      aws-sdk-ecr (~> 1)
      aws-sdk-ecrpublic (~> 1)
      aws-sdk-ecs (~> 1)
      aws-sdk-efs (~> 1)
      aws-sdk-eks (~> 1)
      aws-sdk-elasticache (~> 1)
      aws-sdk-elasticbeanstalk (~> 1)
      aws-sdk-elasticinference (~> 1)
      aws-sdk-elasticloadbalancing (~> 1)
      aws-sdk-elasticloadbalancingv2 (~> 1)
      aws-sdk-elasticsearchservice (~> 1)
      aws-sdk-elastictranscoder (~> 1)
      aws-sdk-emr (~> 1)
      aws-sdk-emrcontainers (~> 1)
      aws-sdk-emrserverless (~> 1)
      aws-sdk-eventbridge (~> 1)
      aws-sdk-finspace (~> 1)
      aws-sdk-finspacedata (~> 1)
      aws-sdk-firehose (~> 1)
      aws-sdk-fis (~> 1)
      aws-sdk-fms (~> 1)
      aws-sdk-forecastqueryservice (~> 1)
      aws-sdk-forecastservice (~> 1)
      aws-sdk-frauddetector (~> 1)
      aws-sdk-fsx (~> 1)
      aws-sdk-gamelift (~> 1)
      aws-sdk-gamesparks (~> 1)
      aws-sdk-glacier (~> 1)
      aws-sdk-globalaccelerator (~> 1)
      aws-sdk-glue (~> 1)
      aws-sdk-gluedatabrew (~> 1)
      aws-sdk-greengrass (~> 1)
      aws-sdk-greengrassv2 (~> 1)
      aws-sdk-groundstation (~> 1)
      aws-sdk-guardduty (~> 1)
      aws-sdk-health (~> 1)
      aws-sdk-healthlake (~> 1)
      aws-sdk-honeycode (~> 1)
      aws-sdk-iam (~> 1)
      aws-sdk-identitystore (~> 1)
      aws-sdk-imagebuilder (~> 1)
      aws-sdk-importexport (~> 1)
      aws-sdk-inspector (~> 1)
      aws-sdk-inspector2 (~> 1)
      aws-sdk-iot (~> 1)
      aws-sdk-iot1clickdevicesservice (~> 1)
      aws-sdk-iot1clickprojects (~> 1)
      aws-sdk-iotanalytics (~> 1)
      aws-sdk-iotdataplane (~> 1)
      aws-sdk-iotdeviceadvisor (~> 1)
      aws-sdk-iotevents (~> 1)
      aws-sdk-ioteventsdata (~> 1)
      aws-sdk-iotfleethub (~> 1)
      aws-sdk-iotjobsdataplane (~> 1)
      aws-sdk-iotsecuretunneling (~> 1)
      aws-sdk-iotsitewise (~> 1)
      aws-sdk-iotthingsgraph (~> 1)
      aws-sdk-iottwinmaker (~> 1)
      aws-sdk-iotwireless (~> 1)
      aws-sdk-ivs (~> 1)
      aws-sdk-ivschat (~> 1)
      aws-sdk-kafka (~> 1)
      aws-sdk-kafkaconnect (~> 1)
      aws-sdk-kendra (~> 1)
      aws-sdk-keyspaces (~> 1)
      aws-sdk-kinesis (~> 1)
      aws-sdk-kinesisanalytics (~> 1)
      aws-sdk-kinesisanalyticsv2 (~> 1)
      aws-sdk-kinesisvideo (~> 1)
      aws-sdk-kinesisvideoarchivedmedia (~> 1)
      aws-sdk-kinesisvideomedia (~> 1)
      aws-sdk-kinesisvideosignalingchannels (~> 1)
      aws-sdk-kms (~> 1)
      aws-sdk-lakeformation (~> 1)
      aws-sdk-lambda (~> 1)
      aws-sdk-lambdapreview (~> 1)
      aws-sdk-lex (~> 1)
      aws-sdk-lexmodelbuildingservice (~> 1)
      aws-sdk-lexmodelsv2 (~> 1)
      aws-sdk-lexruntimev2 (~> 1)
      aws-sdk-licensemanager (~> 1)
      aws-sdk-licensemanagerusersubscriptions (~> 1)
      aws-sdk-lightsail (~> 1)
      aws-sdk-locationservice (~> 1)
      aws-sdk-lookoutequipment (~> 1)
      aws-sdk-lookoutforvision (~> 1)
      aws-sdk-lookoutmetrics (~> 1)
      aws-sdk-machinelearning (~> 1)
      aws-sdk-macie (~> 1)
      aws-sdk-macie2 (~> 1)
      aws-sdk-mainframemodernization (~> 1)
      aws-sdk-managedblockchain (~> 1)
      aws-sdk-managedgrafana (~> 1)
      aws-sdk-marketplacecatalog (~> 1)
      aws-sdk-marketplacecommerceanalytics (~> 1)
      aws-sdk-marketplaceentitlementservice (~> 1)
      aws-sdk-marketplacemetering (~> 1)
      aws-sdk-mediaconnect (~> 1)
      aws-sdk-mediaconvert (~> 1)
      aws-sdk-medialive (~> 1)
      aws-sdk-mediapackage (~> 1)
      aws-sdk-mediapackagevod (~> 1)
      aws-sdk-mediastore (~> 1)
      aws-sdk-mediastoredata (~> 1)
      aws-sdk-mediatailor (~> 1)
      aws-sdk-memorydb (~> 1)
      aws-sdk-mgn (~> 1)
      aws-sdk-migrationhub (~> 1)
      aws-sdk-migrationhubconfig (~> 1)
      aws-sdk-migrationhubrefactorspaces (~> 1)
      aws-sdk-migrationhubstrategyrecommendations (~> 1)
      aws-sdk-mobile (~> 1)
      aws-sdk-mq (~> 1)
      aws-sdk-mturk (~> 1)
      aws-sdk-mwaa (~> 1)
      aws-sdk-neptune (~> 1)
      aws-sdk-networkfirewall (~> 1)
      aws-sdk-networkmanager (~> 1)
      aws-sdk-nimblestudio (~> 1)
      aws-sdk-opensearchservice (~> 1)
      aws-sdk-opsworks (~> 1)
      aws-sdk-opsworkscm (~> 1)
      aws-sdk-organizations (~> 1)
      aws-sdk-outposts (~> 1)
      aws-sdk-panorama (~> 1)
      aws-sdk-personalize (~> 1)
      aws-sdk-personalizeevents (~> 1)
      aws-sdk-personalizeruntime (~> 1)
      aws-sdk-pi (~> 1)
      aws-sdk-pinpoint (~> 1)
      aws-sdk-pinpointemail (~> 1)
      aws-sdk-pinpointsmsvoice (~> 1)
      aws-sdk-pinpointsmsvoicev2 (~> 1)
      aws-sdk-polly (~> 1)
      aws-sdk-pricing (~> 1)
      aws-sdk-privatenetworks (~> 1)
      aws-sdk-prometheusservice (~> 1)
      aws-sdk-proton (~> 1)
      aws-sdk-qldb (~> 1)
      aws-sdk-qldbsession (~> 1)
      aws-sdk-quicksight (~> 1)
      aws-sdk-ram (~> 1)
      aws-sdk-rds (~> 1)
      aws-sdk-rdsdataservice (~> 1)
      aws-sdk-recyclebin (~> 1)
      aws-sdk-redshift (~> 1)
      aws-sdk-redshiftdataapiservice (~> 1)
      aws-sdk-redshiftserverless (~> 1)
      aws-sdk-rekognition (~> 1)
      aws-sdk-resiliencehub (~> 1)
      aws-sdk-resourcegroups (~> 1)
      aws-sdk-resourcegroupstaggingapi (~> 1)
      aws-sdk-robomaker (~> 1)
      aws-sdk-rolesanywhere (~> 1)
      aws-sdk-route53 (~> 1)
      aws-sdk-route53domains (~> 1)
      aws-sdk-route53recoverycluster (~> 1)
      aws-sdk-route53recoverycontrolconfig (~> 1)
      aws-sdk-route53recoveryreadiness (~> 1)
      aws-sdk-route53resolver (~> 1)
      aws-sdk-s3 (~> 1)
      aws-sdk-s3control (~> 1)
      aws-sdk-s3outposts (~> 1)
      aws-sdk-sagemaker (~> 1)
      aws-sdk-sagemakeredgemanager (~> 1)
      aws-sdk-sagemakerfeaturestoreruntime (~> 1)
      aws-sdk-sagemakerruntime (~> 1)
      aws-sdk-savingsplans (~> 1)
      aws-sdk-schemas (~> 1)
      aws-sdk-secretsmanager (~> 1)
      aws-sdk-securityhub (~> 1)
      aws-sdk-serverlessapplicationrepository (~> 1)
      aws-sdk-servicecatalog (~> 1)
      aws-sdk-servicediscovery (~> 1)
      aws-sdk-servicequotas (~> 1)
      aws-sdk-ses (~> 1)
      aws-sdk-sesv2 (~> 1)
      aws-sdk-shield (~> 1)
      aws-sdk-signer (~> 1)
      aws-sdk-simpledb (~> 1)
      aws-sdk-sms (~> 1)
      aws-sdk-snowball (~> 1)
      aws-sdk-snowdevicemanagement (~> 1)
      aws-sdk-sns (~> 1)
      aws-sdk-sqs (~> 1)
      aws-sdk-ssm (~> 1)
      aws-sdk-ssmcontacts (~> 1)
      aws-sdk-ssmincidents (~> 1)
      aws-sdk-ssoadmin (~> 1)
      aws-sdk-ssooidc (~> 1)
      aws-sdk-states (~> 1)
      aws-sdk-storagegateway (~> 1)
      aws-sdk-support (~> 1)
      aws-sdk-swf (~> 1)
      aws-sdk-synthetics (~> 1)
      aws-sdk-textract (~> 1)
      aws-sdk-timestreamquery (~> 1)
      aws-sdk-timestreamwrite (~> 1)
      aws-sdk-transcribeservice (~> 1)
      aws-sdk-transcribestreamingservice (~> 1)
      aws-sdk-transfer (~> 1)
      aws-sdk-translate (~> 1)
      aws-sdk-voiceid (~> 1)
      aws-sdk-waf (~> 1)
      aws-sdk-wafregional (~> 1)
      aws-sdk-wafv2 (~> 1)
      aws-sdk-wellarchitected (~> 1)
      aws-sdk-workdocs (~> 1)
      aws-sdk-worklink (~> 1)
      aws-sdk-workmail (~> 1)
      aws-sdk-workmailmessageflow (~> 1)
      aws-sdk-workspaces (~> 1)
      aws-sdk-workspacesweb (~> 1)
      aws-sdk-xray (~> 1)
    aws-sdk-robomaker (1.51.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-rolesanywhere (1.0.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-route53 (1.63.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-route53domains (1.40.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-route53recoverycluster (1.11.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-route53recoverycontrolconfig (1.10.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-route53recoveryreadiness (1.10.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-route53resolver (1.37.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-s3 (1.114.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.4)
    aws-sdk-s3control (1.50.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-s3outposts (1.13.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-sagemaker (1.134.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-sagemakeredgemanager (1.12.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-sagemakerfeaturestoreruntime (1.12.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-sagemakerruntime (1.42.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-savingsplans (1.26.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-schemas (1.23.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-secretsmanager (1.65.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-securityhub (1.68.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-serverlessapplicationrepository (1.43.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-servicecatalog (1.72.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-servicediscovery (1.46.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-servicequotas (1.23.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ses (1.47.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-sesv2 (1.27.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-shield (1.49.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-signer (1.38.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-simpledb (1.35.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv2 (~> 1.0)
    aws-sdk-sms (1.40.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-snowball (1.49.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-snowdevicemanagement (1.7.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-sns (1.53.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-sqs (1.51.1)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ssm (1.138.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ssmcontacts (1.14.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ssmincidents (1.15.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ssoadmin (1.18.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-ssooidc (1.20.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-states (1.48.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-storagegateway (1.68.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-support (1.41.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-swf (1.36.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-synthetics (1.28.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-textract (1.38.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-timestreamquery (1.16.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-timestreamwrite (1.14.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-transcribeservice (1.76.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-transcribestreamingservice (1.42.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-transfer (1.57.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-translate (1.45.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-voiceid (1.8.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-waf (1.47.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-wafregional (1.48.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-wafv2 (1.41.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-wellarchitected (1.17.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-workdocs (1.39.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-worklink (1.33.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-workmail (1.50.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-workmailmessageflow (1.21.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-workspaces (1.72.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-workspacesweb (1.4.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-xray (1.47.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-ses (0.7.1)
      builder
      mail (> 2.2.5)
      mime-types
      xml-simple
    aws-sigv2 (1.1.0)
    aws-sigv4 (1.5.1)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.3.0)
    bcrypt (3.1.18)
    better_errors (2.9.1)
      coderay (>= 1.0.0)
      erubi (>= 1.0.0)
      rack (>= 0.9.0)
    bigdecimal (3.1.8)
    bindex (0.8.1)
    binding_of_caller (1.0.0)
      debug_inspector (>= 0.0.1)
    bootsnap (1.13.0)
      msgpack (~> 1.2)
    bootstrap-daterangepicker-rails (3.0.4)
      railties (>= 4.0)
    browser (5.3.1)
    builder (3.2.4)
    byebug (11.1.3)
    cancancan (3.4.0)
    capistrano (3.5.0)
      airbrussh (>= 1.0.0)
      capistrano-harrow
      i18n
      rake (>= 10.0.0)
      sshkit (>= 1.9.0)
    capistrano-bundler (2.1.0)
      capistrano (~> 3.1)
    capistrano-database-yml (1.0.1)
      capistrano (>= 3.1)
      sshkit (>= 1.2.0)
    capistrano-env-config (0.3.0)
      capistrano (~> 3.0)
      dotenv (~> 2.0)
    capistrano-harrow (0.5.3)
    capistrano-rails (1.6.2)
      capistrano (~> 3.1)
      capistrano-bundler (>= 1.1, < 3)
    capistrano-rbenv (2.2.0)
      capistrano (~> 3.1)
      sshkit (~> 1.3)
    capistrano-rbenv-install (1.2.0)
      capistrano (>= 3.0)
      capistrano-rbenv (>= 2.0)
    capistrano-safe-deploy-to (1.1.1)
      capistrano (>= 3.0)
    capistrano-sidekiq (0.10.0)
      capistrano
      sidekiq (>= 3.4)
    capistrano3-unicorn (0.2.1)
      capistrano (~> 3.1, >= 3.1.0)
    carrierwave (2.2.2)
      activemodel (>= 5.0.0)
      activesupport (>= 5.0.0)
      addressable (~> 2.6)
      image_processing (~> 1.1)
      marcel (~> 1.0.0)
      mini_mime (>= 0.1.3)
      ssrf_filter (~> 1.0)
    case_transform (0.2)
      activesupport
    caxlsx (3.2.0)
      htmlentities (~> 4.3, >= 4.3.4)
      marcel (~> 1.0)
      nokogiri (~> 1.10, >= 1.10.4)
      rubyzip (>= 1.3.0, < 3)
    caxlsx_rails (0.6.3)
      actionpack (>= 3.1)
      caxlsx (>= 3.0)
    childprocess (4.1.0)
    chronic (0.10.2)
    chronic_duration (0.10.6)
      numerizer (~> 0.1.1)
    ckeditor (5.1.1)
      orm_adapter (~> 0.5.0)
    coderay (1.1.3)
    coffee-rails (5.0.0)
      coffee-script (>= 2.2.0)
      railties (>= 5.2.0)
    coffee-script (2.4.1)
      coffee-script-source
      execjs
    coffee-script-source (1.12.2)
    concurrent-ruby (1.1.10)
    config (5.6.1)
      deep_merge (~> 1.2, >= 1.2.1)
      ostruct
    connection_pool (2.2.5)
    crass (1.0.6)
    csv (3.3.5)
    database_cleaner (2.0.1)
      database_cleaner-active_record (~> 2.0.0)
    database_cleaner-active_record (2.0.1)
      activerecord (>= 5.a)
      database_cleaner-core (~> 2.0.0)
    database_cleaner-core (2.0.1)
    debase (0.2.5.beta2)
      debase-ruby_core_source (>= 0.10.12)
    debase-ruby_core_source (0.10.17)
    debug_inspector (1.1.0)
    deep_merge (1.2.2)
    device_detector (1.0.7)
    devise (4.8.1)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    diff-lcs (1.5.0)
    digest (3.1.0)
    docile (1.4.0)
    docx (0.9.1)
      nokogiri (~> 1.13, >= 1.13.0)
      rubyzip (~> 2.0)
    domain_name (0.5.20190701)
      unf (>= 0.0.5, < 1.0.0)
    doorkeeper (5.5.4)
      railties (>= 5)
    dotenv (2.8.1)
    dotenv-rails (2.8.1)
      dotenv (= 2.8.1)
      railties (>= 3.2)
    draper (4.0.2)
      actionpack (>= 5.0)
      activemodel (>= 5.0)
      activemodel-serializers-xml (>= 1.0)
      activesupport (>= 5.0)
      request_store (>= 1.0)
      ruby2_keywords
    draper-cancancan (1.1.1)
    dry-configurable (1.3.0)
      dry-core (~> 1.1)
      zeitwerk (~> 2.6)
    dry-core (1.1.0)
      concurrent-ruby (~> 1.0)
      logger
      zeitwerk (~> 2.6)
    dry-inflector (1.2.0)
    dry-initializer (3.2.0)
    dry-logic (1.6.0)
      bigdecimal
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.1)
      zeitwerk (~> 2.6)
    dry-schema (1.13.4)
      concurrent-ruby (~> 1.0)
      dry-configurable (~> 1.0, >= 1.0.1)
      dry-core (~> 1.0, < 2)
      dry-initializer (~> 3.0)
      dry-logic (>= 1.4, < 2)
      dry-types (>= 1.7, < 2)
      zeitwerk (~> 2.6)
    dry-struct (1.8.0)
      dry-core (~> 1.1)
      dry-types (~> 1.8, >= 1.8.2)
      ice_nine (~> 0.11)
      zeitwerk (~> 2.6)
    dry-types (1.8.3)
      bigdecimal (~> 3.0)
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.0)
      dry-inflector (~> 1.0)
      dry-logic (~> 1.4)
      zeitwerk (~> 2.6)
    dry-validation (1.10.0)
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.0, < 2)
      dry-initializer (~> 3.0)
      dry-schema (>= 1.12, < 2)
      zeitwerk (~> 2.6)
    enum_help (0.0.19)
      activesupport (>= 3.0.0)
    errbase (0.2.2)
    erubi (1.11.0)
    et-orbi (1.2.7)
      tzinfo
    event_stream_parser (1.0.0)
    excon (0.92.4)
    execjs (2.8.1)
    faraday (1.10.1)
      faraday-em_http (~> 1.0)
      faraday-em_synchrony (~> 1.0)
      faraday-excon (~> 1.1)
      faraday-httpclient (~> 1.0)
      faraday-multipart (~> 1.0)
      faraday-net_http (~> 1.0)
      faraday-net_http_persistent (~> 1.0)
      faraday-patron (~> 1.0)
      faraday-rack (~> 1.0)
      faraday-retry (~> 1.0)
      ruby2_keywords (>= 0.0.4)
    faraday-em_http (1.0.0)
    faraday-em_synchrony (1.0.0)
    faraday-excon (1.1.0)
    faraday-httpclient (1.0.1)
    faraday-multipart (1.0.4)
      multipart-post (~> 2)
    faraday-net_http (1.0.1)
    faraday-net_http_persistent (1.2.0)
    faraday-patron (1.0.0)
    faraday-rack (1.0.0)
    faraday-retry (1.0.3)
    ffaker (2.21.0)
    ffi (1.15.5)
    fog-aws (3.14.0)
      fog-core (~> 2.1)
      fog-json (~> 1.1)
      fog-xml (~> 0.1)
    fog-core (2.3.0)
      builder
      excon (~> 0.71)
      formatador (>= 0.2, < 2.0)
      mime-types
    fog-json (1.2.0)
      fog-core
      multi_json (~> 1.10)
    fog-xml (0.1.4)
      fog-core
      nokogiri (>= 1.5.11, < 2.0.0)
    font-awesome-sass (6.1.2)
      sassc (~> 2.0)
    formatador (1.1.0)
    fugit (1.5.3)
      et-orbi (~> 1, >= 1.2.7)
      raabro (~> 1.4)
    fuubar (2.5.1)
      rspec-core (~> 3.0)
      ruby-progressbar (~> 1.4)
    gemoji (3.0.1)
    get_process_mem (0.2.7)
      ffi (~> 1.0)
    globalid (1.0.0)
      activesupport (>= 5.0)
    gon (6.4.0)
      actionpack (>= 3.0.20)
      i18n (>= 0.7)
      multi_json
      request_store (>= 1.0)
    google-protobuf (3.21.5)
    google-protobuf (3.21.5-x86_64-darwin)
    google-protobuf (3.21.5-x86_64-linux)
    googleapis-common-protos-types (1.4.0)
      google-protobuf (~> 3.14)
    grape-swagger-rails (0.3.1)
      railties (>= 3.2.12)
    groupdate (6.1.0)
      activesupport (>= 5.2)
    grpc (1.48.0)
      google-protobuf (~> 3.19)
      googleapis-common-protos-types (~> 1.0)
    grpc (1.48.0-x86_64-darwin)
      google-protobuf (~> 3.19)
      googleapis-common-protos-types (~> 1.0)
    grpc (1.48.0-x86_64-linux)
      google-protobuf (~> 3.19)
      googleapis-common-protos-types (~> 1.0)
    hashery (2.1.2)
    hashie (5.0.0)
    highcharts-rails (6.0.3)
      railties (>= 3.1)
    htmlbeautifier (1.4.2)
    htmlentities (4.3.4)
    http-cookie (1.0.5)
      domain_name (~> 0.5)
    httparty (0.22.0)
      csv
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    i18n (1.8.5)
      concurrent-ruby (~> 1.0)
    ice_nine (0.11.2)
    image_processing (1.12.2)
      mini_magick (>= 4.9.5, < 5)
      ruby-vips (>= 2.0.17, < 3)
    importmap-rails (1.1.5)
      actionpack (>= 6.0.0)
      railties (>= 6.0.0)
    impressionist (2.0.0)
      nokogiri (~> 1)
    jbuilder (2.11.5)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jmespath (1.6.1)
    jquery-datetimepicker-rails (*******)
    json (2.6.2)
    jsonapi-renderer (0.2.2)
    jwt (2.4.1)
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    kgio (2.11.4)
    koala (3.2.0)
      addressable
      faraday (< 2)
      json (>= 1.8)
      rexml
    launchy (2.5.0)
      addressable (~> 2.7)
    letter_opener (1.10.0)
      launchy (>= 2.2, < 4)
    letter_opener_web (2.0.0)
      actionmailer (>= 5.2)
      letter_opener (~> 1.7)
      railties (>= 5.2)
      rexml
    liquid (5.5.1)
    liquid-c (4.2.0)
      liquid (>= 5.0.1)
    logger (1.7.0)
    lograge (0.12.0)
      actionpack (>= 4)
      activesupport (>= 4)
      railties (>= 4)
      request_store (~> 1.0)
    loofah (2.18.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.5.9)
    mail (2.7.1)
      mini_mime (>= 0.1.1)
    marcel (1.0.2)
    mechanize (2.8.5)
      addressable (~> 2.8)
      domain_name (~> 0.5, >= 0.5.20190701)
      http-cookie (~> 1.0, >= 1.0.3)
      mime-types (~> 3.0)
      net-http-digest_auth (~> 1.4, >= 1.4.1)
      net-http-persistent (>= 2.5.2, < 5.0.dev)
      nokogiri (~> 1.11, >= 1.11.2)
      rubyntlm (~> 0.6, >= 0.6.3)
      webrick (~> 1.7)
      webrobots (~> 0.1.2)
    method_source (1.0.0)
    mime-types (3.4.1)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2022.0105)
    mimemagic (0.4.3)
      nokogiri (~> 1)
      rake
    mini_magick (4.11.0)
    mini_mime (1.1.2)
    minitest (5.16.3)
    moji (1.6)
    mojinizer (0.2.2)
      moji (~> 1.6)
    momentjs-rails (********)
      railties (>= 3.1)
    msgpack (1.5.4)
    multi_json (1.15.0)
    multi_xml (0.7.1)
      bigdecimal (~> 3.1)
    multipart-post (2.2.3)
    mysql2 (0.5.6)
    natto (1.2.0)
      ffi (>= 1.9.0)
    net-http-digest_auth (1.4.1)
    net-http-persistent (4.0.1)
      connection_pool (~> 2.2)
    net-imap (0.2.3)
      digest
      net-protocol
      strscan
    net-pop (0.1.1)
      digest
      net-protocol
      timeout
    net-protocol (0.1.3)
      timeout
    net-scp (1.2.1)
      net-ssh (>= 2.6.5)
    net-smtp (0.3.1)
      digest
      net-protocol
      timeout
    net-ssh (7.0.1)
    nio4r (2.5.8)
    nkf (0.1.1)
    nokogiri (1.13.8-aarch64-linux)
      racc (~> 1.4)
    nokogiri (1.13.8-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.13.8-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.13.8-x86_64-linux)
      racc (~> 1.4)
    numerizer (0.1.1)
    nyan-cat-formatter (0.12.0)
      rspec (>= 2.99, >= 2.14.2, < 4)
    oauth (0.5.10)
    oauth2 (2.0.6)
      faraday (>= 0.17.3, < 3.0)
      jwt (>= 1.0, < 3.0)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 3)
      rash_alt (>= 0.4, < 1)
      version_gem (~> 1.1)
    omniauth (1.9.2)
      hashie (>= 3.4.6)
      rack (>= 1.6.2, < 3)
    omniauth-facebook (9.0.0)
      omniauth-oauth2 (~> 1.2)
    omniauth-oauth (1.2.0)
      oauth
      omniauth (>= 1.0, < 3)
    omniauth-oauth2 (1.7.3)
      oauth2 (>= 1.4, < 3)
      omniauth (>= 1.9, < 3)
    omniauth-stripe-connect (2.10.1)
      omniauth (~> 1.3)
      omniauth-oauth2 (~> 1.4)
    omniauth-twitter (1.4.0)
      omniauth-oauth (~> 1.1)
      rack
    orm_adapter (0.5.0)
    ostruct (0.6.3)
    paranoia (2.6.0)
      activerecord (>= 5.1, < 7.1)
    pdf-reader (2.14.1)
      Ascii85 (>= 1.0, < 3.0, != 2.0.0)
      afm (~> 0.2.1)
      hashery (~> 2.0)
      ruby-rc4
      ttfunk
    pinecone (1.2.1)
      dry-struct (~> 1.6)
      dry-validation (~> 1.10)
      httparty (~> 0.22.0)
    pretender (0.5.0)
      actionpack (>= 6.1)
    pry (0.14.1)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-byebug (3.10.1)
      byebug (~> 11.0)
      pry (>= 0.13, < 0.15)
    pry-doc (1.3.0)
      pry (~> 0.11)
      yard (~> 0.9.11)
    pry-rails (0.3.9)
      pry (>= 0.10.4)
    pry-stack_explorer (0.6.1)
      binding_of_caller (~> 1.0)
      pry (~> 0.13)
    public_suffix (5.0.0)
    puma (5.6.4)
      nio4r (~> 2.0)
    puma_worker_killer (1.0.0)
      bigdecimal (>= 2.0)
      get_process_mem (>= 0.2)
      puma (>= 2.7)
    raabro (1.4.0)
    racc (1.6.0)
    rack (2.2.4)
    rack-cors (1.1.1)
      rack (>= 2.0.0)
    rack-protection (2.2.2)
      rack
    rack-rewrite (1.5.1)
    rack-test (2.0.2)
      rack (>= 1.3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-controller-testing (1.0.5)
      actionpack (>= 5.0.1.rc1)
      actionview (>= 5.0.1.rc1)
      activesupport (>= 5.0.1.rc1)
    rails-dom-testing (2.0.3)
      activesupport (>= 4.2.0)
      nokogiri (>= 1.6)
    rails-file-icons (0.0.1)
    rails-html-sanitizer (1.4.3)
      loofah (~> 2.3)
    rails_autolink (1.1.6)
      rails (> 3.1)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
      zeitwerk (~> 2.5)
    raindrops (0.20.0)
    rake (13.0.6)
    ranked-model (0.4.8)
      activerecord (>= 4.2)
    ransack (3.2.1)
      activerecord (>= 6.1.5)
      activesupport (>= 6.1.5)
      i18n
    rash_alt (0.4.12)
      hashie (>= 3.4)
    recaptcha (5.10.0)
      json
    redcarpet (3.5.1)
    redis (4.5.1)
    redis-actionpack (5.3.0)
      actionpack (>= 5, < 8)
      redis-rack (>= 2.1.0, < 3)
      redis-store (>= 1.1.0, < 2)
    redis-activesupport (5.3.0)
      activesupport (>= 3, < 8)
      redis-store (>= 1.3, < 2)
    redis-rack (2.1.4)
      rack (>= 2.0.8, < 3)
      redis-store (>= 1.2, < 2)
    redis-rails (5.0.2)
      redis-actionpack (>= 5.0, < 6)
      redis-activesupport (>= 5.0, < 6)
      redis-store (>= 1.2, < 2)
    redis-store (1.9.1)
      redis (>= 4, < 5)
    remotipart (1.4.4)
    request_store (1.5.1)
      rack (>= 1.4)
    responders (3.0.1)
      actionpack (>= 5.0)
      railties (>= 5.0)
    rexml (3.2.5)
    roo (2.9.0)
      nokogiri (~> 1)
      rubyzip (>= 1.3.0, < 3.0.0)
    roo-xls (1.2.0)
      nokogiri
      roo (>= 2.0.0, < 3)
      spreadsheet (> 0.9.0)
    rouge (3.30.0)
    rouge-rails (0.2.1)
      actionview
      railties
      rouge
    rspec (3.11.0)
      rspec-core (~> 3.11.0)
      rspec-expectations (~> 3.11.0)
      rspec-mocks (~> 3.11.0)
    rspec-core (3.11.0)
      rspec-support (~> 3.11.0)
    rspec-expectations (3.11.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.11.0)
    rspec-mocks (3.11.1)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.11.0)
    rspec-rails (5.1.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
      railties (>= 5.2)
      rspec-core (~> 3.10)
      rspec-expectations (~> 3.10)
      rspec-mocks (~> 3.10)
      rspec-support (~> 3.10)
    rspec-retry (0.6.2)
      rspec-core (> 3.3)
    rspec-support (3.11.0)
    rspec_junit_formatter (0.5.1)
      rspec-core (>= 2, < 4, != 2.12.0)
    ruby-next-core (0.15.2)
    ruby-ole (********)
    ruby-openai (7.1.0)
      event_stream_parser (>= 0.3.0, < 2.0.0)
      faraday (>= 1)
      faraday-multipart (>= 1)
    ruby-progressbar (1.11.0)
    ruby-rc4 (0.1.5)
    ruby-vips (2.1.4)
      ffi (~> 1.12)
    ruby2_keywords (0.0.5)
    rubyntlm (0.6.3)
    rubyzip (2.3.2)
    safely_block (0.3.0)
      errbase (>= 0.1.1)
    sassc (2.4.0)
      ffi (~> 1.9)
    select2-rails (4.0.13)
    selenium-webdriver (4.4.0)
      childprocess (>= 0.5, < 5.0)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    sidekiq (5.2.10)
      connection_pool (~> 2.2, >= 2.2.2)
      rack (~> 2.0)
      rack-protection (>= 1.5.0)
      redis (~> 4.5, < 4.6.0)
    sidekiq-status (2.1.3)
      chronic_duration
      sidekiq (>= 5.0)
    simple_form (5.1.0)
      actionpack (>= 5.2)
      activemodel (>= 5.2)
    simplecov (0.21.2)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.12.3)
    simplecov_json_formatter (0.1.4)
    simplemde-rails (1.11.2)
    slack-notifier (2.4.0)
    slim (4.1.0)
      temple (>= 0.7.6, < 0.9)
      tilt (>= 2.0.6, < 2.1)
    slim-rails (3.5.1)
      actionpack (>= 3.1)
      railties (>= 3.1)
      slim (>= 3.0, < 5.0)
    spreadsheet (1.3.0)
      ruby-ole
    spring (4.0.0)
    sprockets (4.1.1)
      concurrent-ruby (~> 1.0)
      rack (> 1, < 3)
    sprockets-rails (3.4.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
      sprockets (>= 3.0.0)
    sshkit (1.21.2)
      net-scp (>= 1.1.2)
      net-ssh (>= 2.8.0)
    ssrf_filter (1.0.8)
    stimulus-rails (1.1.0)
      railties (>= 6.0.0)
    stripe (7.0.0)
    strscan (3.0.4)
    swagger-docs (0.2.9)
      activesupport (>= 3)
      rails (>= 3)
    systemu (2.6.5)
    temple (0.8.2)
    thor (1.2.1)
    tiktoken_ruby (0.0.6-aarch64-linux)
    tiktoken_ruby (0.0.6-arm64-darwin)
    tiktoken_ruby (0.0.6-x86_64-darwin)
    tiktoken_ruby (0.0.6-x86_64-linux)
    tilt (2.0.11)
    timeliness (0.4.4)
    timeout (0.3.0)
    truncate_html (0.9.3)
    ttfunk (1.8.0)
      bigdecimal (~> 3.1)
    turbo-rails (1.1.1)
      actionpack (>= 6.0.0)
      activejob (>= 6.0.0)
      railties (>= 6.0.0)
    tzinfo (2.0.5)
      concurrent-ruby (~> 1.0)
    uglifier (4.2.0)
      execjs (>= 0.3.0, < 3)
    unf (0.1.4)
      unf_ext
    unf_ext (*******)
    unicorn (6.1.0)
      kgio (~> 2.6)
      raindrops (~> 0.7)
    unicorn-rails (2.2.1)
      rack
      unicorn
    unicorn-worker-killer (0.4.5)
      get_process_mem (~> 0)
      unicorn (>= 4, < 7)
    validates_overlap (1.0.0)
      rails (>= 6.0.0)
    validates_timeliness (4.1.1)
      timeliness (>= 0.3.10, < 1)
    version_gem (1.1.0)
    warden (1.2.9)
      rack (>= 2.0.9)
    web-console (4.2.0)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webrick (1.7.0)
    webrobots (0.1.2)
    websocket (1.2.9)
    websocket-driver (0.7.5)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    whenever (1.0.0)
      chronic (>= 0.6.3)
    xml-simple (1.1.9)
      rexml
    yard (0.9.28)
      webrick (~> 1.7.0)
    zeitwerk (2.6.0)
    zengin_code (1.0.1.20220725)

PLATFORMS
  aarch64-linux
  arm64-darwin-21
  arm64-darwin-22
  x86_64-darwin-18
  x86_64-linux

DEPENDENCIES
  RedCloth
  aasm
  actionpack-page_caching
  active_model_serializers (~> 0.10.0)
  acts-as-taggable-on
  ahoy_matey
  ancestry
  anycable-rails
  apexcharts (~> 0.2.0)
  aws-sdk (~> 3)
  aws-sdk-core
  aws-sdk-s3
  aws-ses
  axlsx!
  better_errors
  binding_of_caller
  bootsnap
  bootstrap-daterangepicker-rails
  browser
  byebug
  cancancan
  capistrano (~> 3.5.0)
  capistrano-bundler
  capistrano-database-yml (~> 1.0.0)
  capistrano-env-config
  capistrano-rails
  capistrano-rbenv
  capistrano-rbenv-install
  capistrano-safe-deploy-to (~> 1.1.1)
  capistrano-sidekiq
  capistrano3-unicorn
  carrierwave
  caxlsx_rails
  ckeditor (~> 5.1)
  coffee-rails (~> 5.0.0)
  config
  database_cleaner
  debase (= 0.2.5.beta2)
  debase-ruby_core_source
  devise
  docx
  doorkeeper
  dotenv-rails
  draper
  draper-cancancan
  dry-schema (~> 1.13.0)
  enum_help
  exception_notification!
  ffaker
  fog-aws
  font-awesome-sass
  fugit
  fuubar
  gemoji
  gon
  grape-swagger-rails
  groupdate
  highcharts-rails
  htmlbeautifier
  httparty
  i18n (= 1.8.5)
  importmap-rails
  impressionist
  jbuilder
  jquery-datetimepicker-rails
  jwt
  kaminari
  koala
  launchy
  letter_opener_web
  liquid
  liquid-c
  lograge
  mechanize
  mini_magick
  mojinizer
  momentjs-rails
  mysql2 (~> 0.5)
  natto
  nkf
  nokogiri (>= 1.8.1)
  nyan-cat-formatter
  omniauth
  omniauth-facebook
  omniauth-stripe-connect
  omniauth-twitter
  paranoia (~> 2.2)
  pdf-reader
  pinecone (= 1.2.1)
  pretender
  pry-byebug
  pry-doc
  pry-rails
  pry-stack_explorer
  puma (~> 5.0)
  puma_worker_killer
  rack-cors
  rack-rewrite
  rails (~> *******)
  rails-controller-testing
  rails-file-icons
  rails-footnotes!
  rails_autolink
  ranked-model
  ransack
  recaptcha
  redcarpet
  redis (~> 4.0)
  redis-rails (~> 5)
  remotipart (~> 1.2)
  roo
  roo-xls
  rouge-rails
  rspec-rails
  rspec-retry
  rspec_junit_formatter
  ruby-openai (= 7.1)
  ruby_llm!
  rubyzip (>= 1.2.1)
  select2-rails
  selenium-webdriver
  sidekiq (~> 5.2.9)
  sidekiq-status
  simple_form
  simplecov
  simplemde-rails
  slack-notifier
  slim-rails
  spring
  sprockets-rails
  stimulus-rails
  stripe
  swagger-docs
  systemu (~> 2.6, >= 2.6.5)
  tiktoken_ruby
  truncate_html
  turbo-rails
  tzinfo-data
  uglifier (>= 1.3.0)
  unicorn
  unicorn-rails
  unicorn-worker-killer
  validates_overlap
  validates_timeliness (~> 4.0)
  vimeo_me2!
  web-console (>= 3.3.0)
  whenever
  zengin_code

RUBY VERSION
   ruby 3.1.2p20

BUNDLED WITH
   2.4.22
