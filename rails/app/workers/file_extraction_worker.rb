class FileExtractionWorker
  include Sidekiq::Worker
  sidekiq_options queue: :file_extraction, retry: 3

  def perform(file_source_id)
    file_source = AiTutorFileSource.find(file_source_id)

    Rails.logger.info "Starting file extraction for #{file_source_id}: #{file_source.filename}"

    begin
      file_source.start_extraction!

      extracted_content = extract_content(file_source)

      if extracted_content.present?
        if extracted_content.length > 50_000
          create_chunked_text_sources(file_source, extracted_content)
          file_source.complete_extraction!("Content split into #{(extracted_content.length / 50_000.0).ceil} chunks")
        else
          file_source.complete_extraction!(extracted_content)
        end
        Rails.logger.info "Completed file extraction for #{file_source_id}: #{extracted_content.length} characters"
      else
        file_source.fail_extraction!('No content could be extracted from the file')
        Rails.logger.warn "No content extracted from file #{file_source_id}"
      end

    rescue => e
      Rails.logger.error "File extraction failed for #{file_source_id}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")

      file_source.fail_extraction!(e.message)
      raise e
    end
  end

  private

  def extract_content(file_source)
    file_path = file_source.file_path.path
    return nil unless File.exist?(file_path)

    case file_source.file_type
    when 'text/plain', 'text/csv'
      extract_text_file(file_path)
    when 'application/pdf'
      extract_pdf_content(file_path)
    when 'application/msword'
      extract_doc_content(file_path)
    when 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      extract_docx_content(file_path)
    when 'application/vnd.ms-excel'
      extract_xls_content(file_path)
    when 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      extract_xlsx_content(file_path)
    when 'application/vnd.ms-powerpoint'
      extract_ppt_content(file_path)
    when 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
      extract_pptx_content(file_path)
    else
      raise "Unsupported file type: #{file_source.file_type}"
    end
  end

  def extract_text_file(file_path)
    encodings_to_try = ['UTF-8', 'Shift_JIS', 'EUC-JP', 'ISO-2022-JP', 'Windows-31J']

    best_result = nil
    best_score = 0

    encodings_to_try.each do |encoding|
      begin
        content = File.read(file_path, encoding: encoding)

        if encoding != 'UTF-8'
          content = content.encode('UTF-8', invalid: :replace, undef: :replace)
        end

        fixed_content = fix_japanese_encoding(content)

        score = score_japanese_content(fixed_content)

        if score > best_score
          best_score = score
          best_result = fixed_content
        end

        if encoding == 'UTF-8' && score > 0
          return fixed_content
        end

      rescue Encoding::InvalidByteSequenceError, Encoding::UndefinedConversionError
        next
      end
    end

    return best_result if best_result

    begin
      content = File.read(file_path, mode: 'rb')
      content.force_encoding('UTF-8')
      content = content.scrub('?')
      fix_japanese_encoding(content)
    rescue => e
      Rails.logger.error "Failed to read text file #{file_path}: #{e.message}"
      ""
    end
  end

  def extract_pdf_content(file_path)
    begin
      require 'pdf-reader'
      reader = PDF::Reader.new(file_path)
      content = []

      reader.pages.each_with_index do |page, index|
        begin
          page_text = page.text

          if page_text.present?
            cleaned_text = fix_japanese_encoding(page_text)
            content << cleaned_text if cleaned_text.present?
          end
        rescue => e
          Rails.logger.warn "Failed to extract text from PDF page #{index + 1}: #{e.message}"
        end
      end

      extracted_content = content.join("\n\n")

      if extracted_content.blank?
        Rails.logger.warn "No text content extracted from PDF: #{file_path}"
      else
        extracted_content
      end
    rescue LoadError
      Rails.logger.error "pdf-reader gem not available"
      raise "PDF extraction requires 'pdf-reader' gem. Please add it to your Gemfile."
    rescue => e
      Rails.logger.error "PDF extraction failed: #{e.message}"
    end
  end

  private

  def fix_japanese_encoding(text)
    return text if text.blank?

    begin
      if text.encoding != Encoding::UTF_8
        text = text.encode('UTF-8', invalid: :replace, undef: :replace)
      end

      text = text.scrub('?')

      text = text.gsub(/\x00/, '')

      text = text.unicode_normalize(:nfc) if text.respond_to?(:unicode_normalize)

      text
    rescue => e
      Rails.logger.warn "Failed to fix encoding for text: #{e.message}"
      text.to_s
    end
  end

  def score_japanese_content(text)
    return 0 if text.blank?

    hiragana_count = text.scan(/[\p{Hiragana}]/).length
    katakana_count = text.scan(/[\p{Katakana}]/).length
    kanji_count = text.scan(/[\p{Han}]/).length

    total_japanese = hiragana_count + katakana_count + kanji_count
    total_chars = text.length

    return 0 if total_chars == 0

    japanese_percentage = (total_japanese.to_f / total_chars) * 100

    variety_bonus = 0
    variety_bonus += 10 if hiragana_count > 0
    variety_bonus += 10 if katakana_count > 0
    variety_bonus += 10 if kanji_count > 0

    (japanese_percentage + variety_bonus).round
  end

  def extract_docx_content(file_path)
    # Using docx gem (add to Gemfile if not present)
    begin
      require 'docx'
      doc = Docx::Document.open(file_path)

      paragraphs = doc.paragraphs.map(&:text)
      content = paragraphs.join("\n")

      fix_japanese_encoding(content)
    rescue LoadError
      extract_docx_as_zip(file_path)
    rescue => e
      Rails.logger.error "DOCX extraction failed: #{e.message}"
      extract_docx_as_zip(file_path)
    end
  end

  def extract_docx_as_zip(file_path)
    require 'zip'

    begin
      Zip::File.open(file_path) do |zip_file|
        document_xml = zip_file.find_entry('word/document.xml')
        if document_xml
          xml_content = document_xml.get_input_stream.read

          xml_content = xml_content.force_encoding('UTF-8')

          text_nodes = xml_content.scan(/<w:t[^>]*>([^<]*)<\/w:t>/).flatten
          content = text_nodes.join(' ')

          fix_japanese_encoding(content)
        else
          Rails.logger.warn "No document.xml found in DOCX file"
          ""
        end
      end
    rescue => e
      Rails.logger.error "Failed to extract DOCX as ZIP: #{e.message}"
      ""
    end
  end

  def extract_doc_content(file_path)
    raise "DOC file extraction not implemented yet. Please convert to DOCX format."
  end

  def extract_xlsx_content(file_path)
    begin
      require 'roo'
      spreadsheet = Roo::Spreadsheet.open(file_path)
      content = []

      spreadsheet.sheets.each do |sheet_name|
        spreadsheet.sheet(sheet_name)
        content << "Sheet: #{sheet_name}"

        (1..spreadsheet.last_row).each do |row|
          row_data = (1..spreadsheet.last_column).map do |col|
            spreadsheet.cell(row, col)
          end.compact.join("\t")
          content << row_data if row_data.present?
        end
        content << ""
      end

      content.join("\n")
    rescue LoadError
      raise "XLSX extraction requires 'roo' gem. Please add it to your Gemfile."
    end
  end

  def extract_xls_content(file_path)
    begin
      require 'roo'
      spreadsheet = Roo::Excel.new(file_path)
      content = []

      spreadsheet.sheets.each do |sheet_name|
        spreadsheet.sheet(sheet_name)
        content << "Sheet: #{sheet_name}"

        (1..spreadsheet.last_row).each do |row|
          row_data = (1..spreadsheet.last_column).map do |col|
            spreadsheet.cell(row, col)
          end.compact.join("\t")
          content << row_data if row_data.present?
        end
        content << ""
      end

      content.join("\n")
    rescue LoadError
      raise "XLS extraction requires 'roo' gem. Please add it to your Gemfile."
    end
  end

  def extract_pptx_content(file_path)
    require 'zip'
    content = []

    Zip::File.open(file_path) do |zip_file|
      zip_file.entries.each do |entry|
        if entry.name.match?(/ppt\/slides\/slide\d+\.xml/)
          xml_content = entry.get_input_stream.read
          slide_text = xml_content.scan(/<a:t[^>]*>([^<]*)<\/a:t>/).flatten.join(' ')
          content << slide_text if slide_text.present?
        end
      end
    end

    content.join("\n\n")
  end

  def extract_ppt_content(file_path)
    raise "PPT file extraction not implemented yet. Please convert to PPTX format."
  end

  def create_chunked_text_sources(file_source, content)
    chunk_size = 50_000
    chunks = []

    sentences = content.split(/[.!?]+/)
    current_chunk = ""

    sentences.each do |sentence|
      sentence = sentence.strip
      next if sentence.empty?

      if (current_chunk + sentence).length > chunk_size
        chunks << current_chunk.strip if current_chunk.present?
        current_chunk = sentence
      else
        current_chunk += (current_chunk.empty? ? "" : ". ") + sentence
      end
    end

    chunks << current_chunk.strip if current_chunk.present?

    chunks.each_with_index do |chunk, index|
      AiTutorTextSource.create!(
        ai_tutor_source: file_source.ai_tutor_source,
        content: chunk,
        format: 'plain',
        character_count: chunk.length,
        word_count: chunk.split.length
      )
    end

    Rails.logger.info "Created #{chunks.length} text source chunks for file #{file_source.filename}"
  end
end
