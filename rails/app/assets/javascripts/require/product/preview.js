// Make cart a global variable that can be accessed from anywhere
let globalCart;
let globalUpdateServerCart;
let globalAddToCart;
let globalTransformToGoCart;

document.addEventListener('DOMContentLoaded', function() {
  // ===== Cart Management =====
  const cart = {
    items: [],
    count: 0,
    total: 0,
    maxItems: 20, // Define maximum cart size

    // Add item to cart
    addItem(id, title, price, imageUrl = '', productType = 'Course') {
      // Check if this item is already in cart
      if (this.isInCart(id, productType)) {
        return false;
      }

      // Check if cart has reached max capacity
      if (this.count >= this.maxItems) {
        showMaxItemsAlert();
        return false;
      }

      // Add new item
      this.items.push({
        id,
        title,
        price,
        type: productType,
        imageUrl,
        quantity: 1
      });

      this.count++;
      this.total += parseInt(price);
      this.save();
      return true;
    },

    // Check if item is in cart
    isInCart(id, productType = 'Course') {
      return this.items.some(item => item.id === id && item.type === productType);
    },

    // Save cart to session storage
    save() {
      sessionStorage.setItem('cart', JSON.stringify({
        items: this.items,
        count: this.count,
        total: this.total
      }));

      // Update navbar cart counter whenever cart is saved
      this.updateNavCounter();
    },

    // Add a method to update the navbar counter specifically
    updateNavCounter() {
      const cartBadge = document.getElementById('cart-counter');
      const productNumber = document.getElementById('product-number');
      const value = parseInt(productNumber.dataset.value, 10);

      if (cartBadge) {
        // cartBadge.textContent = this.count.toString();
        cartBadge.textContent = (value + cart.count).toString();

        // Show/hide based on items in cart
        if (this.count > 0) {
          cartBadge.classList.remove('d-none');
        } else {
          cartBadge.classList.add('d-none');
        }
      }
    },

    // Load cart from session storage
    load() {
      const savedCart = sessionStorage.getItem('cart');
      if (savedCart) {
        const parsedCart = JSON.parse(savedCart);
        this.items = parsedCart.items || [];
        this.count = parsedCart.count || 0;
        this.total = parsedCart.total || 0;

        // Update UI with loaded cart data
        updateCartCounter();
        updateTotalPrice();
      }
    }
  };

  // Assign the cart to the global variable
  globalCart = cart;
  globalUpdateServerCart = updateServerCart;
  globalAddToCart = addToCart;

  // Format numbers with commas
  function numberWithCommas(x) {
    return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  }

  // Show alert for maximum cart items reached
  function showMaxItemsAlert() {
    let alertContainer = document.getElementById('max-items-alert-container');

    // Create alert container if it doesn't exist
    if (!alertContainer) {
      alertContainer = document.createElement('div');
      alertContainer.id = 'max-items-alert-container';
      alertContainer.style.position = 'fixed';
      alertContainer.style.top = '20px';
      alertContainer.style.right = '20px';
      alertContainer.style.zIndex = '9999';
      document.body.appendChild(alertContainer);
    }

    // Create the alert element
    const alertElement = document.createElement('div');
    alertElement.className = 'alert alert-warning alert-dismissible fade show';
    alertElement.role = 'alert';
    alertElement.innerHTML = `
      <strong>カートがいっぱいです！</strong> カートには最大20個までの商品が追加できます。
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    // Add the alert to the container
    alertContainer.appendChild(alertElement);

    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (alertElement.parentNode) {
        alertElement.classList.remove('show');
        setTimeout(() => alertElement.remove(), 150);
      }
    }, 5000);

    // Initialize Bootstrap's alert dismiss functionality
    alertElement.querySelector('.btn-close').addEventListener('click', function() {
      alertElement.classList.remove('show');
      setTimeout(() => alertElement.remove(), 150);
    });
  }

  // Update cart counter UI including the navbar icon badge
  function updateCartCounter() {
    // Update all cart count elements
    document.querySelectorAll('.cart-count').forEach(counter => {
      counter.textContent = cart.count.toString();
    });

    // Update the cart icon badge
    const cartBadge = document.getElementById('cart-counter');
    const productNumber = document.getElementById('product-number');
    const value = parseInt(productNumber.dataset.value, 10);

    if (cartBadge) {
      cartBadge.textContent = cart.count.toString();

      // Show/hide badge based on items in cart
      if (cart.count > 0) {
        cartBadge.classList.remove('d-none');
        // cartBadge.textContent = (value + cart.count).toString();
      // } else if (value > 0) {
      //   cartBadge.classList.remove('d-none');
      //   cartBadge.textContent = (value + cart.count).toString();
      } else {
        cartBadge.classList.add('d-none');
      }
    }
  }

  // Update total price display
  function updateTotalPrice() {
    document.querySelectorAll('.cart-total').forEach(el => {
      el.textContent = '¥' + numberWithCommas(cart.total);
    });
  }

  // CONSOLIDATED: Transform button to "カートへ進む"
  function transformToGoCart(element) {
    if (element && (element.tagName === 'BUTTON' || element.classList.contains('add-to-cart-btn'))) {
      element.textContent = 'カートへ進む';
      element.classList.remove('btn-primary');
      element.classList.add('btn-success');

      // Change button behavior
      element.setAttribute('href', '/purchase/cart');
    }
  }

  globalTransformToGoCart = transformToGoCart;

  // Update the cart modal with product info
  function updateCartModal(id, title, price, imageUrl) {
    const modal = document.getElementById('cartModal');
    if (!modal) return;

    // Update product information
    const productTitle = modal.querySelector('.product-title');
    if (productTitle) productTitle.textContent = title;

    const productPrice = modal.querySelector('.product-price');
    if (productPrice) productPrice.textContent = '¥' + numberWithCommas(price);

    // Update image if exists
    const productImage = modal.querySelector('.product-image');
    if (productImage && imageUrl) {
      productImage.src = imageUrl;
      productImage.alt = title;
    }

    // Update cart summary
    const cartCount = modal.querySelector('.cart-count');
    if (cartCount) cartCount.textContent = cart.count.toString();

    const cartTotal = modal.querySelector('.cart-total');
    if (cartTotal) cartTotal.textContent = '¥' + numberWithCommas(cart.total);
  }

  // Send cart update to server
  function updateServerCart(id, title, price, productType) {
    const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    if (!token) return;

    fetch('/purchase/cart/add_item', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': token
      },
      body: JSON.stringify({
        product_id: id,
        product_type: productType,
        quantity: 1
      })
    })
    .then(response => response.json())
    .then(data => console.log('Product added to cart:', data))
    .catch(error => console.error('Error adding product to cart:', error));
  }

  // Update all buttons for a specific product
  function updateButtonsForProduct(id, productType) {
    // Find and update all add-to-cart buttons with this product ID
    document.querySelectorAll(`.add-to-cart-btn[data-product-id="${id}"]`)
      .forEach(transformToGoCart);
  }

  // ===== Add to Cart Logic =====

  // Add to cart function
  function addToCart(id, title, price, imageUrl = '', productType = 'Course') {
    if (cart.isInCart(id, productType)) {
      // If already in cart, just go to cart
      window.location.href = '/purchase/cart';
      return;
    }

    // Check if cart has reached max capacity
    if (cart.count >= cart.maxItems) {
      showMaxItemsAlert();
      return;
    }

    // Add item to cart
    if (cart.addItem(id, title, price, imageUrl, productType)) {
      // Update UI
      updateCartCounter();
      updateTotalPrice();

      // Update buttons for this product
      updateButtonsForProduct(id, productType);

      // Send to server
      updateServerCart(id, title, price, productType);

      // Hide popover if visible
      const popover = document.querySelector('.udemy-popover');
      if (popover) {
        popover.style.display = 'none';
      }

      // Update and show modal
      updateCartModal(id, title, price, imageUrl);
      const cartModal = document.getElementById('cartModal');
      if (cartModal) {
        const bsModal = new bootstrap.Modal(cartModal);
        bsModal.show();
      }
    }
  }

  // ===== Popover Logic =====

  // Get the popover element
  const popover = document.querySelector('.udemy-popover');
  if (!popover) return;

  // Get all product cards
  const allCards = document.querySelectorAll('.card-with-popover');

  // Load cart data and initialize UI
  cart.load();
  updateCartCounter();
  updateTotalPrice();

  // Update buttons for items already in the cart
  cart.items.forEach(item => {
    updateButtonsForProduct(item.id, item.type);
  });

  // Set up hover events for cards
  allCards.forEach((card, index) => {
    if (index === allCards.length - 1) card.classList.add('card-last');

    // Add hover event to each card
    card.addEventListener('mouseenter', function(e) {
      // Get data for this specific product
      const productId = this.getAttribute('data-product-id');
      const productTitle = this.getAttribute('data-product-title');
      const productDescription = this.getAttribute('data-product-description') || '';
      const productPrice = parseInt(this.getAttribute('data-product-price'), 10) || 0;
      const productDuration = this.getAttribute('data-product-duration') || '12';
      const productLevel = this.getAttribute('data-product-level') || '初級';
      const productUpdated = this.getAttribute('data-product-updated') || '';
      const productType = this.getAttribute('data-product-type') || 'Course';

      // Fetch recommendations via AJAX
      fetchRecommendations(productId);

      const productImg = this.querySelector('img.card-img-top');
      const productImgSrc = productImg ? productImg.getAttribute('src') : '';

      // Update popover content with this product's data
      popover.querySelector('h5').textContent = productTitle;
      popover.querySelector('.product-image').setAttribute('src', productImgSrc);
      popover.querySelector('.product-image').setAttribute('alt', productTitle);

      // Update product metadata in popover
      popover.querySelector('.product-updated-date').textContent = productUpdated;
      popover.querySelector('.product-duration').textContent = productDuration;
      popover.querySelector('.product-level').textContent = productLevel;
      popover.querySelector('.product-description').textContent = productDescription;

      // Position the popover near the card
      const cardRect = card.getBoundingClientRect();
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

      popover.style.position = 'absolute';
      popover.style.top = (cardRect.top + scrollTop) + 'px';

      // Position horizontally - for right side of screen, show on left side of card
      const windowWidth = window.innerWidth;
      if (cardRect.right > windowWidth - 350) {
        // Show on left side if card is on right side of screen
        popover.style.left = cardRect.left - 320 - 10 - 10 + 'px';
        popover.style.right = 'auto';
        popover.classList.add('right-side');
      } else {
        // Show on right side of the card
        popover.style.left = cardRect.right + 'px';
        popover.style.right = 'auto';
        popover.classList.remove('right-side');
      }

      // Show the popover
      popover.style.display = 'block';

      // Update "Add to Cart" button with product data
      const addToCartBtn = popover.querySelector('.add-to-cart-btn');
      if (addToCartBtn) {
        addToCartBtn.setAttribute('data-product-id', productId);
        addToCartBtn.setAttribute('data-product-title', productTitle);
        addToCartBtn.setAttribute('data-product-price', productPrice);
        addToCartBtn.setAttribute('data-product-image', productImgSrc);
        addToCartBtn.setAttribute('data-product-type', productType);

        // Check if item is already in cart and update button appearance
        if (cart.isInCart(productId, productType)) {
          addToCartBtn.textContent = 'カートへ進む';
          addToCartBtn.classList.add('btn-success');
          addToCartBtn.classList.remove('btn-primary');
        } else {
          addToCartBtn.textContent = 'カートに入れる';
          addToCartBtn.classList.add('btn-primary');
          addToCartBtn.classList.remove('btn-success');
        }
      }
    });

    // Hide popover when leaving card
    card.addEventListener('mouseleave', function() {
      if (!popover.matches(':hover')) {
        popover.style.display = 'none';
      }
    });
  });

  // Handle mouse events on the popover itself
  popover.addEventListener('mouseenter', () => {
    popover.style.display = 'block';
  });

  popover.addEventListener('mouseleave', () => {
    popover.style.display = 'none';
  });

  // Add click handler for the "Add to Cart" button in popover
  popover.querySelector('.add-to-cart-btn')?.addEventListener('click', function(e) {
    e.preventDefault();
    const id = this.getAttribute('data-product-id');
    const title = this.getAttribute('data-product-title');
    const price = parseInt(this.getAttribute('data-product-price'), 10) || 0;
    const image = this.getAttribute('data-product-image') || '';
    const type = this.getAttribute('data-product-type') || 'Course';

    addToCart(id, title, price, image, type);
  });

  // Event handler for continue shopping button in modal
  document.addEventListener('click', function(e) {
    if (e.target && e.target.classList.contains('continue-shopping-btn')) {
      const modal = bootstrap.Modal.getInstance(document.getElementById('cartModal'));
      if (modal) modal.hide();
    }
  });

  // Initialize the cart badge on page load
  // Load cart data
  cart.load();
  
  // Setup cart badge
  const cartBadge = document.getElementById('cart-counter');
  const productNumber = document.getElementById('product-number');
  const value = parseInt(productNumber.dataset.value, 10);

  if (cartBadge) {
    cartBadge.textContent = cart.count.toString();
    if (cart.count === 0) {
      cartBadge.classList.add('d-none');
    } else {
      cartBadge.classList.remove('d-none');
    }
  }
});

// Initialize cart on page load
document.addEventListener('DOMContentLoaded', function() {
  // First load saved cart data
  cart.load();
  
  // Initialize UI
  updateCartCounter();
  updateTotalPrice();
  
  // Initialize navbar cart counter 
  cart.updateNavCounter();
  
  // Rest of your init code...
});

// ---------- LOGIC PRODUCT RECOMMENDATIONS ---------------
document.addEventListener('DOMContentLoaded', function() {
  // Function to populate product recommendations in the cart modal
  function populateProductRecommendations(productRecommendData) {
    if (!productRecommendData) return;

    try {
      // Parse the product recommend data if it's a string
      let recommendedProducts;
      if (typeof productRecommendData === 'string') {
        // Replace HTML entities with actual characters
        recommendedProducts = JSON.parse(productRecommendData);
      } else {
        recommendedProducts = productRecommendData;
      }

      if (!Array.isArray(recommendedProducts) || recommendedProducts.length === 0) {
        // Hide recommendations section if there are no recommendations
        const recommendSection = document.querySelector('#cartModal h6.mb-3');
        if (recommendSection) recommendSection.style.display = 'none';

        // Hide product recommendation container
        const recommendContainer = document.querySelector('.data-recommendation');
        if (recommendContainer) recommendContainer.style.display = 'none';
        return;
      } else {
        // Show product recommendation container if there are recommendations
        const recommendContainer = document.querySelector('.data-recommendation');
        if (recommendContainer) recommendContainer.style.display = 'block';
      }
      // Get the container and template element
      const recommendContainer = document.querySelector('.data-recommendation');
      if (!recommendContainer) return;

      // Clear existing recommendations
      recommendContainer.innerHTML = '';

      // Create recommendation cards
      recommendedProducts.forEach(product => {
        // Format price
        const price = parseInt(product.price, 10);
        const formattedPrice = price === 0 ? '無料' : `¥${numberWithCommas(price)}`;

        // Create the card
        const productCard = document.createElement('div');
        productCard.className = 'col-12 product-recommend-data';
        productCard.innerHTML = `
          <div class="card mb-3">
            <div class="row g-0">
              <div class="col-md-2">
                <img src="${product.image || '#'}" class="img-fluid rounded-start product-recommend-image" alt="${product.name || 'コースサムネイル'}">
              </div>
              <div class="col-md-10">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h6 class="product-recommend-image-title card-title">${product.name || ''}</h6>
                      <p class="small mb-1 product-recommend-teacher">${product.teacher || ''}</p>
                      <div class="d-flex align-items-center mb-2">
                        <span class="fw-bold me-1">4.7</span>
                        <div class="rating-stars me-1">
                          <i class="bi bi-star-fill"></i>
                          <i class="bi bi-star-fill"></i>
                          <i class="bi bi-star-fill"></i>
                          <i class="bi bi-star-fill"></i>
                          <i class="bi bi-star-half"></i>
                        </div>
                        <span class="text-muted small">(8)</span>
                      </div>
                      ${product.bestseller ? '<span class="bestseller-badge">ベストセラー</span>' : ''}
                    </div>
                    <div class="text-end">
                      <p class="fw-bold mb-1 product-recommend-price">${formattedPrice}</p>
                      <button class="btn btn-outline-primary btn-sm add-recommended-btn" 
                        data-course-id="${product.id || ''}" 
                        data-course-title="${product.name || ''}" 
                        data-course-price="${price}" 
                        data-course-image="${product.image || ''}"
                        data-course-type="${product.type || 'Course'}">
                        <i class="bi bi-plus"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        `;
        recommendContainer.appendChild(productCard);
      });
      
      // Add event listeners to add-recommended buttons
      document.querySelectorAll('.add-recommended-btn').forEach(button => {
        button.addEventListener('click', function() {

          const courseId = this.getAttribute('data-course-id');
          const courseTitle = this.getAttribute('data-course-title');
          const coursePrice = parseInt(this.getAttribute('data-course-price'), 10);
          const courseImage = this.getAttribute('data-course-image');
          const courseType = this.getAttribute('data-course-type') || 'Course';

          // Add to cart using the existing addToCart function
          addToCartRecommend(courseId, courseTitle, coursePrice, courseImage, courseType);

          // Disable the button after adding
          this.disabled = true;
          this.innerHTML = '<i class="bi bi-check"></i>';
          this.classList.remove('btn-outline-primary');
          this.classList.add('btn-success');
        });
      });
      
      // Show recommendations section
      const recommendSection = document.querySelector('#cartModal h6.mb-3');
      if (recommendSection) recommendSection.style.display = 'block';
      
    } catch (error) {
      console.error('Error populating product recommendations:', error);
      
      // Hide recommendations section on error
      const recommendSection = document.querySelector('#cartModal h6.mb-3');
      if (recommendSection) recommendSection.style.display = 'none';
    }
  }
  
  // Format numbers with commas
  function numberWithCommas(x) {
    return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  }
  
  // Add the populateProductRecommendations function to the window object
  // so it can be called from other scripts
  window.populateProductRecommendations = populateProductRecommendations;
  
  // Find all add-to-cart buttons and add event handlers to populate recommendations
  document.querySelectorAll('.add-to-cart-btn').forEach(button => {
    const originalClickHandler = button.onclick;

    button.onclick = function(event) {
      // Show cart icons for both mobile and desktop views
      document.querySelectorAll('.icon-cart.d-lg-none, .icon-cart.d-none.d-lg-block')
        .forEach(icon => icon.classList.remove('hidden'));

      // Execute original handler if exists
      if (originalClickHandler) {
        originalClickHandler.call(this, event);
      }

      // After adding to cart, populate recommendations
      const productRecommend = this.getAttribute('data-product-recommend') || '';
      populateProductRecommendations(productRecommend);
    };
  });

  // Listen for the cartModal being shown
  const cartModal = document.getElementById('cartModal');
  if (cartModal) {
    cartModal.addEventListener('show.bs.modal', function(event) {
      // Get the button that triggered the modal
      const button = event.relatedTarget;

      if (button) {
        // Get product recommend data from the button
        const productRecommend = button.getAttribute('data-product-recommend') || '';
        populateProductRecommendations(productRecommend);
      }
    });
  }
  
  // Handle continue shopping button click
  const continueShoppingBtn = document.querySelector('.continue-shopping-btn');
  if (continueShoppingBtn) {
    continueShoppingBtn.addEventListener('click', function() {
      // Close the modal
      const cartModal = bootstrap.Modal.getInstance(document.getElementById('cartModal'));
      if (cartModal) {
        cartModal.hide();
      }
    });
  }
});

// Add this function before the populateProductRecommendations function
function addToCartRecommend(id, title, price, imageUrl = '', productType = 'Course') {
  // First, make sure the globalCart object is available
  if (typeof globalCart === 'undefined') {
    console.error('Cart object is not defined');
    return;
  }

  // Check if item is already in cart
  if (globalCart.isInCart(id, productType)) {
    // If already in cart, just show a message and do nothing
    console.log('Item is already in cart');
    return;
  }

  // Check if cart has reached max capacity
  if (globalCart.count >= globalCart.maxItems) {
    showMaxItemsAlert();
    return;
  }

  // Add item to cart
  if (globalCart.addItem(id, title, price, imageUrl, productType)) {
    // Update UI
    if (typeof updateCartCounter === 'function') {
      updateCartCounter();
    }

    if (typeof updateTotalPrice === 'function') {
      updateTotalPrice();
    }

    document.querySelectorAll(`.add-to-cart-btn[data-product-id="${id}"]`).forEach(button => {
      if (typeof globalTransformToGoCart === 'function') {
        globalTransformToGoCart(button);
      }
    });

    // Send to server
    globalUpdateServerCart(id, title, price, productType);

    // Show a success message in the cart modal
    const recommendContainer = document.querySelector('.product-recommend-data');
    if (recommendContainer) {
      // Create success alert if it doesn't exist
      if (!document.getElementById('recommend-success-alert')) {
        const alertElement = document.createElement('div');
        alertElement.id = 'recommend-success-alert';
        alertElement.className = 'alert alert-success mt-3';
        alertElement.innerHTML = `
          <i class="bi bi-check-circle-fill me-2"></i>
          <strong>${title}</strong> をカートに追加しました
        `;

        // Insert at the top of recommendations
        recommendContainer.insertBefore(alertElement, recommendContainer.firstChild);

        // Auto-remove after 3 seconds
        setTimeout(() => {
          if (alertElement && alertElement.parentNode) {
            alertElement.remove();
          }
        }, 3000);
      }
    }
  }
}

// Make sure this function is available globally
window.addToCartRecommend = addToCartRecommend;

document.addEventListener('DOMContentLoaded', function() {
  // Rest of your existing code remains the same...

  // When you bind event listeners to .add-recommended-btn buttons:
  document.querySelectorAll('.add-recommended-btn').forEach(button => {
    button.addEventListener('click', function() {
      const courseId = this.getAttribute('data-course-id');
      const courseTitle = this.getAttribute('data-course-title');
      const coursePrice = parseInt(this.getAttribute('data-course-price'), 10);
      const courseImage = this.getAttribute('data-course-image');
      const courseType = this.getAttribute('data-course-type') || 'Course';

      // Use the dedicated function for recommendations
      addToCartRecommend(courseId, courseTitle, coursePrice, courseImage, courseType);

      // Update this button's state
      this.disabled = true;
      this.innerHTML = '<i class="bi bi-check"></i>';
      this.classList.remove('btn-outline-primary');
      this.classList.add('btn-success');
    });
  });
});

// Add the fetchRecommendations function
function fetchRecommendations(courseId) {
  if (!courseId) return;

  // Get CSRF token for AJAX request
  const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
  if (!token) return;

  // Create fetch request to get recommendations
  fetch(`/courses/${courseId}/get_recommendations`, {
    method: 'GET',
    headers: {
      'X-CSRF-Token': token,
      'Accept': 'application/json'
    }
  })
  .then(response => {
    if (!response.ok) {
      throw new Error('Network response was not ok');
    }
    return response.json();
  })
  .then(data => {
    // Update the data-product-recommend attribute
    updateRecommendationAttributes(courseId, data);
  })
  .catch(error => {
    console.error('Error fetching recommendations:', error);
  });
}

// Function to update recommendation attributes on buttons
function updateRecommendationAttributes(courseId, recommendationsData) {
  // Find all buttons for this course and update their data-product-recommend attribute
  document.querySelectorAll(`.add-to-cart-btn[data-product-id="${courseId}"]`).forEach(button => {
    button.setAttribute('data-product-recommend', JSON.stringify(recommendationsData));
  });

  // Also update the current popover if it's visible
  const popover = document.querySelector('.udemy-popover');
  if (popover && popover.style.display === 'block') {
    const popoverButton = popover.querySelector('.add-to-cart-btn');
    if (popoverButton && popoverButton.getAttribute('data-product-id') === courseId) {
      popoverButton.setAttribute('data-product-recommend', JSON.stringify(recommendationsData));
    }
  }
}

// Add event listeners to all add-to-cart-btn in screen index and show buttons
document.addEventListener('DOMContentLoaded', function() {
  const detailPageButton = document.querySelector('.add-to-cart-btn-in-show');
  if (detailPageButton) {
    const courseId = detailPageButton.getAttribute('data-product-id');

    if (courseId) {
      fetchRecommendations(courseId);
    }

    const savedCart = sessionStorage.getItem('cart');
    if (savedCart) {
      try {
        const cartData = JSON.parse(savedCart);

        // Check if the course exists in the cart items
        if (cartData.items && Array.isArray(cartData.items)) {
          const courseInCart = cartData.items.some(item =>
            item.id.toString() === courseId.toString() && item.type === 'Course'
          );

          if (courseInCart) {
            document.querySelectorAll(`.add-to-cart-btn[data-product-id="${courseId}"]`).forEach(btn => {
              if (typeof globalTransformToGoCart === 'function') {
                globalTransformToGoCart(btn);
              }
            });
          }
        }
      } catch (error) {
        console.error('Error parsing cart data:', error);
      }
    }
  }

  document.querySelectorAll('.add-to-cart-btn-index, .add-to-cart-btn-in-show').forEach(button => {
    button.addEventListener('click', function(e) {
      console.log('Add to cart button clicked');
      e.preventDefault();

      // Get product data from data attributes
      const id = this.getAttribute('data-product-id');
      const title = this.getAttribute('data-product-title');
      const price = parseInt(this.getAttribute('data-product-price'), 10) || 0;
      const imageUrl = this.getAttribute('data-product-image') || '';
      const type = this.getAttribute('data-product-type') || 'Course';

      if (typeof globalAddToCart === 'function') {
        globalAddToCart(id, title, price, imageUrl, type);
      } else {
        console.error('addToCart function is not available');
      }
    });
  });
});
