class CoursePurchaseSerializer < BaseSerializer
  def render_default
    {
      id: object.id, 
      price: object.price, 
      paied_price: object.paied_price, 
      status: object.status, 
      created_at: object.created_at, 
      start_at: object.start_at, 
      paied_at: object.paied_at, 
      course_id: object.course.id,
      course_name: object.course.name,
      course_description: object.course.description,
      course_url: object.course.image.url,
      installment_count_current: object.installment_count_current,
      installment_count_total: object.installment_count_total,
      force_status: object.force_status,
    }
  end

  def subscription_method
    object.method
  end
end
