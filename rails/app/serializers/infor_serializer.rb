class InforSerializer < BaseSerializer
  include ActionView::Helpers::SanitizeHelper
  include ActionView::Helpers::TextHelper

  def render_notice
    {
      id: object.id,
      title: object.title,
      updated_at: object.updated_at,
      show_dashboard: object.show_dashboard,
      created_at: object.created_at,
      description: truncate(object.content, length: 100),
      content: sanitize(UtilityHelper.markdown_to_html object.content).html_safe,
      readed: check_read,
      image: object.image
    }
  end

  def render_default
    render_notice
  end

  private
  def check_read
    return false if @options[:read_id].nil?
    @options[:read_id].include?(object.id)
  end
end
