class SubscriptionSerializer < BaseSerializer
  def render_default
    {
      id: object.id,
      purchase_price: object.life_time? ? object.purchase_price : (@options[:full_attribute] ? get_next_price : nil),
      purchase_type: object.purchase_type,
      price_plan:  object.life_time? ? object.price_plan&.name : nil,
      status: object.status,
      created_at: object.created_at,
      price: object.premium_service.get_purchase_price,
      paied_at: object.paied_at,
      subscription_method: subscription_method,
      expired_at: object.expired_at_from_stripe,
      is_active: is_active,
      premium_service_id: object.premium_service.id,
      premium_service_name: object.premium_service.name,
      premium_service_description: object.premium_service.description,
      premium_service_url: object.premium_service.image.url,
      premium_service_price: object.premium_service.price,
      installment_count_current: object.installment_count_current,
      installment_count_total: object.installment_count_total,
      force_status: object.force_status,
      next_pay: @options[:full_attribute] ? get_next_price : nil,
      end_date: object.end_date,
      stripe_customer_default_card_number: object.stripe_customer_default_card_number,
      stripe_customer_default_card_expired_at: object.stripe_customer_default_card_expired_at,
      need_membership_fee: object.need_membership_fee,
      membership_fee: object.membership_fee,
      membership_fee_paid_at: object.membership_fee_paid_at,
      membership_fee_charge_at: object.membership_fee_charge_at,
      membership_fee_charge_failed: object.membership_fee_charge_failed,
      membership_fee_charge_failed_message: object.membership_fee_charge_failed_message
    }
  end

  def is_active
    object.active?
  end

  def subscription_method
    object.method
  end

  def get_next_pay
    stripe_upcoming_invoice = object.get_stripe_upcoming_invoice
    return unless stripe_upcoming_invoice

    Time.at(stripe_upcoming_invoice["next_payment_attempt"])
  end

  def get_next_price
    @get_next_price ||= begin
      stripe_upcoming_invoice = object.get_stripe_upcoming_invoice
      return unless stripe_upcoming_invoice

      stripe_upcoming_invoice["amount_remaining"]
    end
  end
end
