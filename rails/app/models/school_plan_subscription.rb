class SchoolPlanSubscription < ApplicationRecord
  belongs_to :user
  belongs_to :school_plan
  belongs_to :school
  has_many :school_plan_invoices
  before_destroy :remove_plan

  # 未払金
  STATUS_PENDING = 1

  # 支払い済み
  STATUS_DONE = 2

  # 契約停止
  STATUS_STOPED = 3

  # 体験中
  STATUS_TRIALING = 4

  # 強制設定しない
  FORCE_STATUS_NONE = 0

  # 強制再開
  FORCE_STATUS_START = 1

  # 強制停止
  FORCE_STATUS_STOP = 2

  enum plan_type: { monthly: 1, annually: 2, handle: 3 }

  def force_status_start?
    self.force_status == FORCE_STATUS_START
  end

  def force_status_none?
    self.force_status == FORCE_STATUS_NONE
  end

  def force_status_stop?
    self.force_status == FORCE_STATUS_STOP
  end
  
  def stoped?
    self.status == STATUS_STOPED
  end

  def done?
    self.status == STATUS_DONE
  end

  def pending?
    self.status == STATUS_PENDING
  end

  def create_payment(stripe_email, stripe_token, user_name)
    ActiveRecord::Base.transaction do
      customer = Stripe::Customer.create(
        :email => stripe_email,
        :source  => stripe_token
      )
      self.stripe_customer_id = customer.id
      self.save
    end
  end

  def update_subscription_status
    # 自動決済対象のばあい、Skipします。
    return unless self.force_status_none?

    # 終了したばあい、スキップします。
    return if self.stoped? 

    # 支払い危険になってないばあい、スキップします。
    return if self.next_pay_at.present? && self.next_pay_at > Time.now

    error = nil
    ActiveRecord::Base.transaction do
      charge = Stripe::Charge.create(
        :customer    => self.stripe_customer_id,
        :amount      => self.purchase_price,
        :description => "School  #{self.school.name} サブスクリプションプラン #{self.school_plan.title}",
        :currency    => "jpy"
      )

      # Invoice作成
      school_plan_invoice = SchoolPlanInvoice.new
      school_plan_invoice.user = self.user
      school_plan_invoice.school_id = self.school_id
      school_plan_invoice.school_plan_id = self.school_plan_id
      school_plan_invoice.school_plan_subscription = self
      school_plan_invoice.stripe_charge_id = charge["id"]
      school_plan_invoice.amount_paid = charge["amount"]
      school_plan_invoice.paied_at = Time.at(charge["created"])
      school_plan_invoice.save

      self.status = SchoolPlanSubscription::STATUS_DONE

      self.paied_at = Time.at(charge["created"])

      self.save

      self.apply_plan
    rescue Exception => e
      puts e
      error = e
      raise ActiveRecord::Rollback
    end
    if error.present?
      self.payment_error = error.message || 'UNKNOW ERROR'
      self.status = SchoolPlanSubscription::STATUS_PENDING
      self.save
    end
  end

  def next_pay_at
    next_pay_day_number = self.plan_type == "annually" ? 365.days : 30.days

    return self.paied_at + next_pay_day_number if self.paied_at.present? && self.done?

    nil
  end

  def refund_amound
    return nil unless self.paied_at.present? && self.done?

    next_pay_day_number = self.plan_type == "annually" ? 365.0 : 30.0

    used_amound =  (self.used_day * self.purchase_price / next_pay_day_number).round

    return 0 if used_amound > self.purchase_price

    self.purchase_price - used_amound
  end

  def used_day
    (Time.zone.now - self.paied_at)/(24.0*60*60).round
  end
  

  def self.add_subscription(school, user, school_plan, plan_type)
    # 契約を作成する
    subscription = SchoolPlanSubscription.new
    subscription.user = user
    subscription.school = school
    subscription.school_plan = school_plan
    subscription.plan_type = plan_type == "annually" ? :annually : :monthly
    subscription.status = SchoolPlanSubscription::STATUS_PENDING
    subscription.force_status = FORCE_STATUS_NONE
    subscription.purchase_price = plan_type == "annually" ? school_plan.annually || 50 : school_plan.monthly || 50
    subscription.save

    subscription
  end

  def self.add_by_handle(school, user, school_plan)
    # 契約を作成する
    subscription = SchoolPlanSubscription.new
    subscription.user = user
    subscription.school = school
    subscription.school_plan = school_plan
    subscription.plan_type = :handle
    subscription.status = SchoolPlanSubscription::STATUS_DONE
    subscription.paied_at = Time.zone.now
    subscription.force_status = FORCE_STATUS_START
    subscription.save

    # プランをスクールに登録する
    subscription.apply_plan
  end

  # プラン登録する時に
  def apply_plan
    # 利用機能反映する
    self.school.use_premium_service     ||= self.school_plan.use_premium_service
    self.school.use_course_lesson       ||= self.school_plan.use_course_lesson
    self.school.use_user_question       ||= self.school_plan.use_user_question
    self.school.use_blog                ||= self.school_plan.use_blog
    self.school.use_dashboard           ||= self.school_plan.use_dashboard
    self.school.use_goal                ||= self.school_plan.use_goal
    self.school.use_mail                ||= self.school_plan.use_mail
    self.school.use_skill               ||= self.school_plan.use_skill
    self.school.use_user_graph          ||= self.school_plan.use_user_graph
    self.school.use_exam_stock          ||= self.school_plan.use_exam_stock
    self.school.use_exam_recommendation ||= self.school_plan.use_exam_recommendation
    self.school.use_banner              ||= self.school_plan.use_banner
    self.school.use_user_dashboard      ||= self.school_plan.use_user_dashboard
    self.school.use_exam                ||= self.school_plan.use_exam
    self.school.registerable            ||= self.school_plan.registerable
    self.school.lazy_sign_up            ||= self.school_plan.lazy_sign_up

    self.school.use_school_onboarding   ||= self.school_plan.use_school_onboarding
    self.school.use_vector_button       ||= self.school_plan.use_vector_button
    self.school.use_meeting             ||= self.school_plan.use_meeting
    self.school.use_meeting_admin           ||= self.school_plan.use_meeting_admin
    self.school.use_evaluate_lesson         ||= self.school_plan.use_evaluate_lesson
    self.school.use_user_list               ||= self.school_plan.use_user_list
    self.school.use_user_blocks             ||= self.school_plan.use_user_blocks
    self.school.use_teacher_list            ||= self.school_plan.use_teacher_list
    self.school.use_iams                    ||= self.school_plan.use_iams
    self.school.use_affiliates              ||= self.school_plan.use_affiliates
    self.school.use_catalog                 ||= self.school_plan.use_catalog
    self.school.use_exam_prompt             ||= self.school_plan.use_exam_prompt
    self.school.use_question_list           ||= self.school_plan.use_question_list
    self.school.use_course_question         ||= self.school_plan.use_course_question
    self.school.use_user_group              ||= self.school_plan.use_user_group
    self.school.use_signup_email_template   ||= self.school_plan.use_signup_email_template
    self.school.use_step_email              ||= self.school_plan.use_step_email
    self.school.use_coupon                  ||= self.school_plan.use_coupon
    self.school.use_access_report           ||= self.school_plan.use_access_report
    self.school.use_school_designs_code_editor ||= self.school_plan.use_school_designs_code_editor
    self.school.use_school_designs_general  ||= self.school_plan.use_school_designs_general
    self.school.use_lists                   ||= self.school_plan.use_lists
    self.school.use_terms                   ||= self.school_plan.use_terms
    self.school.use_activities              ||= self.school_plan.use_activities
    self.school.use_school_plan             ||= self.school_plan.use_school_plan
    self.school.use_custom_texts            ||= self.school_plan.use_custom_texts
    self.school.use_ai_chat                 ||= self.school_plan.use_ai_chat
    self.school.use_ai_chat_question        ||= self.school_plan.use_ai_chat_question
    self.school.use_ai_model_api            ||= self.school_plan.use_ai_model_api
    self.school.use_ai_chat_lesson          ||= self.school_plan.use_ai_chat_lesson
    self.school.use_ai_exam_generation      ||= self.school_plan.use_ai_exam_generation
    self.school.use_ai_chat_goal            ||= self.school_plan.use_ai_chat_goal
    self.school.use_ai_chat_exam            ||= self.school_plan.use_ai_chat_exam
    self.school.use_ai_grading              ||= self.school_plan.use_ai_grading

    self.school.save

    # プランをスクールで展開する
    self.active_school_plan
  end

  # プラン終了・削除のとき
  def remove_plan
    self.school.use_premium_service = false     if self.school_plan.use_premium_service
    self.school.use_course_lesson = false       if self.school_plan.use_course_lesson
    self.school.use_user_question = false       if self.school_plan.use_user_question
    self.school.use_blog = false                if self.school_plan.use_blog
    self.school.use_dashboard = false           if self.school_plan.use_dashboard
    self.school.use_goal = false                if self.school_plan.use_goal
    self.school.use_mail = false                if self.school_plan.use_mail
    self.school.use_skill = false               if self.school_plan.use_skill
    self.school.use_user_graph = false          if self.school_plan.use_user_graph
    self.school.use_exam_stock = false          if self.school_plan.use_exam_stock
    self.school.use_exam_recommendation = false if self.school_plan.use_exam_recommendation
    self.school.use_banner = false              if self.school_plan.use_banner
    self.school.use_user_dashboard = false      if self.school_plan.use_user_dashboard
    self.school.use_exam = false                if self.school_plan.use_exam
    self.school.use_embed = false               if self.school_plan.use_embed
    self.school.allow_user_edit_group = false   if self.school_plan.allow_user_edit_group
    self.school.registerable = false            if self.school_plan.registerable
    self.school.lazy_sign_up = false            if self.school_plan.lazy_sign_up

    self.school.use_school_onboarding = false       if self.school_plan.use_school_onboarding
    self.school.use_vector_button = false           if self.school_plan.use_vector_button
    self.school.use_meeting = false                 if self.school_plan.use_meeting
    self.school.use_meeting_admin = false           if self.school_plan.use_meeting_admin
    self.school.use_evaluate_lesson = false         if self.school_plan.use_evaluate_lesson
    self.school.use_user_list = false               if self.school_plan.use_user_list
    self.school.use_user_blocks = false             if self.school_plan.use_user_blocks
    self.school.use_teacher_list = false            if self.school_plan.use_teacher_list
    self.school.use_iams = false                    if self.school_plan.use_iams
    self.school.use_affiliates = false              if self.school_plan.use_affiliates
    self.school.use_catalog = false                 if self.school_plan.use_catalog
    self.school.use_exam_prompt = false             if self.school_plan.use_exam_prompt
    self.school.use_question_list = false           if self.school_plan.use_question_list
    self.school.use_course_question = false         if self.school_plan.use_course_question
    self.school.use_user_group = false              if self.school_plan.use_user_group
    self.school.use_signup_email_template = false   if self.school_plan.use_signup_email_template
    self.school.use_step_email = false              if self.school_plan.use_step_email
    self.school.use_coupon = false                  if self.school_plan.use_coupon
    self.school.use_access_report = false           if self.school_plan.use_access_report
    self.school.use_school_designs_code_editor = false if self.school_plan.use_school_designs_code_editor
    self.school.use_school_designs_general = false  if self.school_plan.use_school_designs_general
    self.school.use_lists = false                   if self.school_plan.use_lists
    self.school.use_terms = false                   if self.school_plan.use_terms
    self.school.use_activities = false              if self.school_plan.use_activities
    self.school.use_school_plan = false             if self.school_plan.use_school_plan
    self.school.use_custom_texts = false            if self.school_plan.use_custom_texts
    self.school.use_ai_chat = false                 if self.school_plan.use_ai_chat
    self.school.use_ai_chat_question = false        if self.school_plan.use_ai_chat_question
    self.school.use_ai_model_api = false            if self.school_plan.use_ai_model_api

    self.school.use_ai_chat_lesson = false          if self.school_plan.use_ai_chat_lesson
    self.school.use_ai_exam_generation = false      if self.school_plan.use_ai_exam_generation
    self.school.use_ai_chat_goal = false            if self.school_plan.use_ai_chat_goal
    self.school.use_ai_chat_exam = false            if self.school_plan.use_ai_chat_exam
    self.school.use_ai_grading = false              if self.school_plan.use_ai_grading

    self.school.school_price_status = School::SCHOOL_PRICE_STATUS_NONE
    self.school.save
  end

  # プラン開始する（登録・再開したら、これを実行する）
  def active_school_plan
    self.school.school_price_status = School::SCHOOL_PRICE_STATUS_ACTIVE

    self.school.save
  end

  # プラン一時的に停止する（お支払い失敗の場合）
  def pending_school_plan
    self.school.school_price_status = School::SCHOOL_PRICE_STATUS_PENDING

    self.school.save
  end

  def total_amount_paid
    self.school_plan_invoices.sum(:amount_paid)
  end

  def start
    SchoolPlanSubscription.transaction do
      self.force_status = SchoolPlanSubscription::FORCE_STATUS_START
      self.status = SchoolPlanSubscription::STATUS_DONE
      self.apply_plan

      self.save
    rescue Exception => e
      puts e
      raise ActiveRecord::Rollback
    end
  end

  def stop
    SchoolPlanSubscription.transaction do
      # 強制ストップ
      self.force_status = SchoolPlanSubscription::FORCE_STATUS_STOP
      self.status = SchoolPlanSubscription::STATUS_STOPED
      self.remove_plan

      self.save
    rescue Exception => e
      puts e
      raise ActiveRecord::Rollback
    end
  end

  def refund_subscription
    # 強制サブスクリプションはrefund出来ません
    return unless self.force_status_none?

    # refund_amoundがない場合、refund出来ません
    return unless self.refund_amound.present?

    # Invoiceがない場合、refundが出来ません
    last_invoice = self.school_plan_invoices.order(created_at: :desc).first
    return unless last_invoice.present?

    # Stripe charge id がない場合、refund出来ません
    return unless last_invoice.stripe_charge_id.present?
    
    Stripe::Refund.create({
      charge: last_invoice.stripe_charge_id,
      amount: self.refund_amound
    })

    last_invoice.status = SchoolPlanInvoice::STATUS_REFUNDED
    last_invoice.refunded_amount = self.refund_amound
    last_invoice.amount_paid = last_invoice.amount_paid - last_invoice.refunded_amount
    last_invoice.save
  end
  
  def stop_contract
    SchoolPlanSubscription.transaction do
      self.status = SchoolPlanSubscription::STATUS_STOPED
      self.remove_plan

      self.save
    rescue Exception => e
      puts e
      raise ActiveRecord::Rollback
    end
  end

  def resume(invoice_price, invoice_created_date)
    SchoolPlanSubscription.transaction do
      subscription_invoice = SchoolPlanInvoice.new
      subscription_invoice.user = self.user
      subscription_invoice.school_id = self.school_id
      subscription_invoice.school_plan_subscription_id = self.id
      subscription_invoice.school_plan_id = self.school_plan_id
      subscription_invoice.stripe_charge_id = ""
      subscription_invoice.amount_paid = invoice_price
      subscription_invoice.paied_at = invoice_created_date || Time.zone.now
      subscription_invoice.save
      self.save
    rescue Exception => e
      puts e
      raise ActiveRecord::Rollback
    end
  end
  
end

