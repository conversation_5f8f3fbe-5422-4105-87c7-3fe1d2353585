class PageCustom < ApplicationRecord
  after_commit :remove_cache
  has_ancestry
  belongs_to :school
  has_many :page_custom_page_templates, dependent: :destroy
  has_many :page_templates, through: :page_custom_page_templates
  mount_uploader :image, ImageUploader
  belongs_to :contact_form, foreign_key: "confirm_contact_id"
  scope :published, -> {where.not(published_at: nil)}
  scope :find_with_slag, ->(id_slug) {where(id: id_slug).or(where(slag_name: id_slug))}
  scope :filter_by_query, ->(query) {where("title LIKE ? OR design_content LIKE ?", "%#{query}%", "%#{query}%")}
  validates :slag_name, format: { without: /\s/, message: "must contain no spaces" }

  def page_template(school_design)
    self.page_templates.where(school_design_id: school_design.id).first
  end


  def active_design_content
    if self.body_type == 0 && self.design_content.present?
      self.design_content
    elsif self.body_type == 1 && self.ck_body.present?
      self.ck_body
    else
      ''
    end
  end

  private
  def remove_cache
    Rails.cache.delete_matched("pages-#{self.id}*")
  end
end
