class Product < ApplicationRecord
  belongs_to :productable, polymorphic: true

  has_many :cart_items, dependent: :destroy
  has_many :order_items

  has_many :recommended_products,
           class_name: "ProductRecommend",
           foreign_key: :product_id,
           dependent: :destroy

  has_many :recommenders,
           class_name: "ProductRecommend",
           foreign_key: :recommended_product_id,
           dependent: :destroy
end
