class Lesson < ApplicationRecord
  include Publicable
  include FreeTextable
  include RemoveCache
  include Slugable

  after_commit :remove_muti_course_cache
  # after_update :update_pinecone_data
  mount_uploader :image, ImageUploader
  is_impressionable counter_cache: true
  belongs_to :chapter

  has_many :teacher_lessons, dependent: :destroy
  has_many :teachers, through: :teacher_lessons, dependent: :destroy
  has_many :course_lessons, dependent: :destroy
  has_many :courses, through: :course_lessons, dependent: :destroy
  has_many :enrollment_lessons, dependent: :destroy
  has_many :enrollments, through: :enrollment_lessons, dependent: :destroy
  has_many :lesson_exams, dependent: :destroy
  has_many :exams, through: :lesson_exams
  has_many :question_relatives, as: :relativeable, dependent: :destroy
  has_many :questions, through: :question_relatives
  has_many :skill_item_managers, as: :managerable, dependent: :destroy
  has_many :skill_items, through: :skill_item_managers
  has_many :comments, as: :commentable, dependent: :destroy
  has_many :courses_managers_users, through: :courses, source: :managers_users
  has_many :votes, as: :votable, dependent: :destroy
  has_many :lesson_qs, dependent: :destroy
  has_many :qs, through: :lesson_qs
  has_many :ai_chat_questionables, -> { where(questionable_type: 'Lesson') }, as: :questionable
  has_many :ai_chat_questions, through: :ai_chat_questionables
  has_many_attached :materials

  after_save :update_vimeo_name, if: -> { saved_change_to_name? && self.vimeo.present? }
  after_save :update_copied_mimics, if: ->{ @update_child_mimic }
  after_save :create_course_published_lessons_count
  after_save :update_video_info_lesson, if: :should_update_video_info?
  after_commit :remove_lesson_cache

  attr_accessor :vimeo_complete_uri
  attr_accessor :update_child_mimic
  attr_accessor :run_update_vimeo

  validates :name, length: {maximum: Settings.max_length.name}, presence: true, allow_blank: true
  validates :description, length: {maximum: Settings.max_length.description}, allow_blank: true
  validates :body, length: {maximum: Settings.max_length.body}, allow_blank: true

  scope :published, -> {where.not(published_at: nil)}
  scope :previewable, -> { where(is_previewable: true) }
  enum lesson_type: { video: 1, text: 2, exam: 3, live: 4 }

  VIDEO_TYPE_DEFAULT = 1
  VIDEO_TYPE_VIMEO = 2

  def school
    self.courses.first&.school
  end

  def create_course_published_lessons_count
    courses.each do |course|
      course.published_lessons_count = course.lessons.where.not(published_at: nil).count
      course.save
    end
  end

  def should_update_video_info?
    !@skip_update_video_info
  end

  def skip_update_video_info!
    @skip_update_video_info = true
  end

  def vimeo_complete_uri= value
    return if value.blank?

    vimeo_util = VimeoUtil.new(self.school)

    if value == "destroy"
      self.vimeo = nil
      return
    end
    return unless value&.match(/^\/videos\/[0-9]+/)
    call = vimeo_util.client.get("#{value}?fields=status")
    self.vimeo = value.split("/").last if call["status"] != "uploading"
  end

  def create_enrollment_lesson(enrollment, goal)
    c = enrollment.enrollment_lessons.find_or_initialize_by(lesson_id: self.id, goal_id: goal)
    c.count_up
    c.finished_at = Time.now  # ここで finished_at も設定
    c.save

    c
  end

  # app/models/lesson.rb
  def revert_enrollment_lesson(enrollment, goal)
    c = enrollment.enrollment_lessons.find_by(lesson_id: self.id)
    return unless c

    Activity.where(
      action: 'enrollment_lesson',
      object: c,
      user: enrollment.user
    ).destroy_all  # 該当するActivityを削除

    c.finished_at = nil
    c.save

    c
  end

  # def revert_enrollment_lesson(enrollment, goal)
  #   c = enrollment.enrollment_lessons.find_by(lesson_id: self.id)
  #   return unless c

  #   c.finished_at = nil
  #   c.save

  #   c
  # end


  def next_lesson(course)
    course_lessons = course.course_lessons

    lesson_id = self.course_lessons.where(course_id: course.id).pluck(:id).first
    return course.lessons.first if lesson_id.blank?
    position = course_lessons.rank(:row_order).pluck(:id).index(lesson_id) || -1
    return course_lessons[position+1].try(:lesson)
  end

  def update_teachers(teacher_ids)
    TeacherLesson.where(school: self.school, lesson: self).destroy_all
    teacher_ids.each do |teacher_id|
      if teacher_id.present?
        teacher = Teacher.where(id: teacher_id).first
        if teacher.present?
          TeacherLesson.find_or_create_by(lesson: self, teacher_id: teacher_id, school: self.school)
          teacher.update_summary
        end
      end
    end
  end

  def update_vimeo_name
    begin
      vimeo_video = VimeoMe2::Video.new(Settings.vimeo.token, self.vimeo)
      vimeo_video.name = self.name
      vimeo_video.update
    rescue Exception => e
    end
  end

  def get_contexts
    [self]
  end

  def update_child_mimic= value
    @update_child_mimic = value
  end

  def comments_notification_target
    self.courses_managers_users.uniq
  end

  def course
    courses.first
  end

  def remove_exam exam
    lesson_exams.where(exam_id: exam).destroy_all
  end

  def answer_count
    self.questions.includes(:answers).where(answers: { accepted: true }).shared.count
  end

  def video_available
    vimeo_util = VimeoUtil.new(self.school)

    if self.vimeo.present?
      vimeo_video_info = vimeo_util.client.get("/videos/#{self.vimeo}")
      return vimeo_video_info["status"] == "available"
    end

    return true
  end

  def youtube_id
    self.video.present? ? UtilityHelper.get_youtube_id(self.video) : ""
  end

  def video_url
    return "https://player.vimeo.com/video/#{self.video}" if video_source_vimeo?

    return "https://player.vimeo.com/video/#{self.vimeo}?h=9f19d6f0ce" if self.vimeo.present? && self.video_type == VIDEO_TYPE_VIMEO

    return "https://www.youtube.com/embed/#{self.youtube_id}" if self.youtube_id.present? && self.video_type == VIDEO_TYPE_DEFAULT

    return ""
  end

  def thumbnail_url
    return self.vimeo_thumbnail if self.vimeo.present? && self.video_type == VIDEO_TYPE_VIMEO || video_source_vimeo?

    return self.youtube_thumbnail if self.youtube_id.present? && self.video_type == VIDEO_TYPE_DEFAULT

    return ""
  end

  def update_video_info_lesson
    vimeo_util = VimeoUtil.new(self.school)

    if saved_change_to_vimeo?
      if self.vimeo.present?
        vimeo_video_info = vimeo_util.client.get("/videos/#{self.vimeo}")
        vimeo_thumbnail = vimeo_video_info["pictures"]["sizes"][-2]["link_with_play_button"]
        vimeo_duration = vimeo_video_info["duration"]
        self.update_columns vimeo_thumbnail: vimeo_thumbnail, vimeo_duration: UtilityHelper.seconds_to_hms(vimeo_duration.to_i)
      end

      if attribute_before_last_save("vimeo").present?
        begin
          unless Rails.env.development?
            vimeo_util.client.delete("/videos/#{attribute_before_last_save("vimeo")}", code: 204)
            VimeoLog.where(
              target: self,
              school_id: self.school.id,
              vimeo: attribute_before_last_save("vimeo")
            ).destroy_all
          end
        rescue => exception
          p exception
          SlackClient.client.ping('Vimeo Delete Eror')
          SlackClient.client.ping("/videos/#{attribute_before_last_save("vimeo")}")
          SlackClient.client.ping(exception)
        end
      end
    end

    if self.vimeo.present? && saved_change_to_vimeo?
      vimeo_video_info = vimeo_util.client.get("/videos/#{self.vimeo}")
      vimeo_thumbnail = vimeo_video_info["pictures"]["sizes"][-2]["link_with_play_button"]
      vimeo_duration = vimeo_video_info["duration"]
      self.update_columns vimeo_thumbnail: vimeo_thumbnail, vimeo_duration: UtilityHelper.seconds_to_hms(vimeo_duration.to_i)

      # get upload_folder_id
      upload_folder_id = vimeo_util.get_or_create_folder("#{ENV["VIMEO_UPLOAD_FOLDER_PREFIX"]}_school_#{self.school.id}")

      # Move to upload folder
      vimeo_util.client.put("/me/projects/#{upload_folder_id}/videos/#{self.vimeo}", code: 204)

      # Save to VimeoLog
      VimeoLog.create!(
        target: self,
        school_id: self.school.id,
        vimeo_account_id: self.school.vimeo_account_id,
        vimeo: self.vimeo,
        size: vimeo_video_info.dig("files")[0]["size"].to_i
      )
    end
    if self.video.present? && saved_change_to_video?
      video_id = self.video

      if video_source_vimeo?
        vimeo_video_info = vimeo_util.client.get("/videos/#{video_id}")
        vimeo_thumbnail = vimeo_video_info["pictures"]["sizes"][-2]["link_with_play_button"]
        vimeo_duration = vimeo_video_info["duration"]
        self.update_columns vimeo_thumbnail: vimeo_thumbnail, vimeo_duration: UtilityHelper.seconds_to_hms(vimeo_duration.to_i)
      else
        youtube_duration = YoutubeApi.get_duration_video(video_id)
        youtube_thumbnail = YoutubeApi.get_thumnail_video(video_id)
        self.update_columns youtube_duration: UtilityHelper.seconds_to_hms(youtube_duration.to_i), youtube_thumbnail: youtube_thumbnail, video: video_id
      end
    end
  end

  def active_body
    if self.body_type == 0 && self.md_body.present?
      self.md_body
    elsif self.body_type == 1 && self.ck_body.present?
      self.ck_body
    else
      ''
    end
  end

  def update_pinecone_data
    if previous_changes.keys.any? { |key| ["md_body", "name", "ck_body"].include?(key) }
      PineconeData.send_request(self, "update")
    end
  end
  def video_source_vimeo?
    video_type == VIDEO_TYPE_DEFAULT && video.present? && video.match?(/^\d+$/)
  end

  def video_id_for(provider)
    if provider == "vimeo" && video_type == VIDEO_TYPE_VIMEO
      return vimeo
    end

    if provider == "vimeo" && video_type == VIDEO_TYPE_DEFAULT && video_source_vimeo?
      return video
    end

    if provider == "default" && video_type == VIDEO_TYPE_DEFAULT && !video_source_vimeo?
      return youtube_id
    end

    nil
  end
end
