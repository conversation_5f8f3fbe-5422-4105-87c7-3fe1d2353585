class Course < ApplicationRecord
  include RankedModel
  include Publicable
  include Price
  include Couponable
  include RemoveCache
  include Slugable

  acts_as_paranoid

  after_commit :remove_course_cache
  after_commit :remove_muti_exam_cache

  ranks :row_order

  mount_uploader :image, ImageUploader
  acts_as_ordered_taggable_on :skills
  acts_as_ordered_taggable_on :tags
  is_impressionable counter_cache: true
  paginates_per Settings.paginations.default_perpage_new
  include Importable
  include Manageable

  belongs_to :school, counter_cache: true
  belongs_to :term
  belongs_to :coupon

  has_many :chat_rooms, as: :chatable, dependent: :destroy
  has_many :teacher_courses, dependent: :destroy
  has_many :teachers, through: :teacher_courses, dependent: :destroy
  has_many :enrollments, dependent: :destroy
  has_many :users, through: :enrollments
  has_many :course_lessons
  has_many :lessons, through: :course_lessons, dependent: :destroy
  has_many :skills, dependent: :destroy
  has_many :skill_items, dependent: :destroy
  has_many :exams, dependent: :destroy
  has_many :lesson_exams, through: :lessons, source: :exams, class_name: "Exam"
  has_many :milestone_courses, dependent: :delete_all
  has_many :milestones, through: :milestone_courses
  has_many :announcement_mails, as: :context, dependent: :destroy
  has_many :question_relatives, as: :relativeable, dependent: :destroy
  has_many :questions, through: :question_relatives
  has_many :chapters, dependent: :destroy
  has_many :master_milestones, through: :milestone_courses # it is belong_to
  has_many :course_purchases, dependent: :destroy
  has_many :exam_courses, dependent: :destroy
  has_many :reviews, dependent: :destroy
  has_many :exams, through: :exam_courses, dependent: :destroy
  has_many :premium_service_courses, dependent: :destroy
  has_many :learns, dependent: :destroy
  has_many :targets, dependent: :destroy
  has_many :comments, as: :commentable, dependent: :destroy
  has_many :prerequisits, dependent: :destroy
  has_many :pickup_resources, as: :objectionable, dependent: :destroy
  has_many :list_courses, dependent: :destroy
  has_many :lists, through: :list_courses
  has_many_attached :materials
  has_one :product, as: :productable, dependent: :destroy
  has_many :enrollments

  after_save :save_master_milestone, if: ->{ master_milestone.present? }

  accepts_nested_attributes_for :learns
  accepts_nested_attributes_for :targets
  accepts_nested_attributes_for :prerequisits
  accepts_nested_attributes_for :list_courses
  before_validation :check_slug
  validates_uniqueness_of :slug, scope: [:school_id]
  validates :name, length: { maximum: Settings.max_length.name }, presence: true
  validates :description, length: { maximum: Settings.max_length.description }, allow_blank: true
  validates :body, length: { maximum: Settings.max_length.body }, allow_blank: true

  scope :filter_by_scope, ->(scope) { where(id: scope.courses) }
  scope :filter_by_query, ->(query) { where("name LIKE ? OR description LIKE ? OR body LIKE ?", query, query, query) }
  scope :published, -> { where.not(published_at: nil) }
  scope :not_publish, -> { where(published_at: nil) }
  scope :from_master_schools, -> { where(school: School.master)}
  scope :find_with_name, -> (name) { where("name LIKE ?","%#{sanitize_sql_like(name.strip.presence || " ")}%") }
  scope :find_with_slug, ->(id_slug) {where(id: id_slug).or(where(slug: id_slug))}

  enum type_is: { free: 0, sign_in: 1, premium: 2, text: 3, product: 4 }
  enum level: { Beginner: 0, Easy: 1, Normal: 2, Hard: 3, VeryHard: 4 }
  attr_accessor :master_milestone

  def teachers_with_rank
    @teachers_with_rank ||= Teacher.published.joins(:teacher_courses).where("teacher_courses.course_id = #{self.id}").order("teacher_courses.row_order asc").to_a
  end

  def self.find_with_context(context, courses)
    courses =
    if context == "tut"
      courses.where(type_is: :text)
    elsif context == "video"
      courses.where.not(type_is: :text)
    else
      courses
    end
    return courses
  end

  def self.by_type(type)
    if type.present?
      includes(:school).published.where(type_is: type)
    else
      includes(:school).published
    end
  end

  def published_course_lessons
    self.course_lessons.includes(:lesson).references(:lessons).where("lessons.published_at <= ?", Time.now)
  end

  def lessons_count
    self.lessons.published.size
  end

  def review_count
    self.reviews.count
  end

  def level_name
    Course.levels_i18n[self.level]
  end

  def duration_text
    hour = self.duration/60
    minute = self.duration % 60
    minute = minute > 10 ? minute : "0#{minute}"

    "#{hour}h#{minute}m"
  end

  def cut_price
    if cut_rate > 0
      price - (price / cut_rate)
    else
      price
    end
  end

  def save_master_milestone
    _master_milestone = MasterMilestone.find self.master_milestone
    self.milestone_courses.where.not(master_milestone_id: self.master_milestone).destroy_all
    enrollments.includes(:milestones).where.not(milestones: {master_milestone: self.master_milestone}).each do |enrollment|
      new_milestone = _master_milestone.milestones.includes(:user_goal).where("user_goals.user_id" => enrollment.user_id).first
      enrollment.enrollment_milestones.update milestone_id: new_milestone
    end
    _master_milestone.courses << self unless _master_milestone.course_ids.include?(self.id)
    _master_milestone.update_milesotne_course(self)
  end

  def associations_should_be_copied
    [:lessons]
  end

  def self.top_course(school)
    result = {}
    result['today'] = Course.top_in_range(school, Time.zone.now, Time.zone.now)
    result['one_week'] = Course.top_in_range(school, Time.zone.now - 7.days, Time.zone.now)
    result['one_month'] = Course.top_in_range(school, Time.zone.now - 30.days, Time.zone.now)
    result['one_year'] = Course.top_in_range(school, Time.zone.now - 365.days, Time.zone.now)

    result
  end

  def self.top_in_range(school, start_date, end_date, limit = 10)
    enrollments = school.enrollments.where("#{UtilityHelper.convert_tz_sql("enrollments.created_at")} >= '#{start_date.strftime('%Y-%m-%d')}' AND #{UtilityHelper.convert_tz_sql("enrollments.created_at")} <= '#{end_date.strftime('%Y-%m-%d')}'")

    enrollments.select("COUNT(enrollments.user_id) as count_course, enrollments.course_id").group("enrollments.course_id").order("count_course DESC").limit(limit)
  end

  def import_lessons(file, goal)
    imported_lessons = 0
    return self.each_row(file, ->(_){
      raise ImportException.new "何もインポートしませんでした" if imported_lessons == 0
      }) do |row, importer|
      if importer.first_row?
        importer.require_field(["name", "description", "body", "chapter_name", "youtube_url", "type", "publish"])
      else

        importer.require_field(["name"])
        chapter_id = nil
        published_at = nil
        if row["chapter_name"].present?
          chapter = Chapter.find_or_create_by(name: row["chapter_name"], course_id: self.id)
          chapter_id = chapter&.id
        end
        if row["publish"] == "yes"
          published_at = DateTime.now
        elsif row["publish"] != ""
          # convert string datetime to datetime
          published_at = DateTime.parse(row["publish"]) rescue nil
        end
        lesson = self.lessons.create(
          name: row["name"],
          description: row["description"],
          md_body: row["body"],
          video: row["youtube_url"],
          chapter_id: chapter_id,
          lesson_type: row["type"].present? ? row["type"] : :video,
          published_at: published_at,
          vimeo: row["vimeo"],
          subtitle_h2: row["subtitle_h2"],
          subtitle_h3: row["subtitle_h3"]
        )
        importer.check_valid lesson
        imported_lessons += 1
        if row["skill_items"].present?
          skill = self.skills.first
          row["skill_items"].split(",").each do |skill_item_name|
            skill_item = skill.skill_items.find_or_create_by(name: skill_item_name)
            importer.check_valid skill_item
            lesson.skill_items << skill_item
          end
        end
      end
    end
  end

  def answer_count
    self.questions.includes(:answers).where(answers: { accepted: true }).shared.count
  end

  def review_average_rank
    self.reviews.average(:rank).to_f.round(1)
  end

  def review_rank_distributions
    hash = {}
    review_count = self.reviews.count
    self.reviews.group(:rank).count.each do |i, value|
      hash[i] = (value * 100/review_count)
    end
    (1..5).each do |i|
      if hash[i].nil?
        hash[i] = 0
      end
    end
    hash.sort.reverse
  end

  def user_can_join_course?(user)

    # コースが無料の場合
    return true unless self.product?

    # コースが購入された
    return true if CoursePurchase.find_by(user: user, course: self).present?

    false
  end

  def check_slug
    if self.slug.to_s == ""
      self.slug = self.name.gsub(" ","-")
      course = Course.find_by(name: self.name)
      slug_course = Course.find_by(slug: self.slug)
      if course || slug_course
        self.slug = "#{self.name.gsub(" ","-")}-#{Time.now.to_i}"
      end
    end
  end

  def copy_status school_id
    copy_processes = CopyProcess.where(
      origin_resource_id: self.id,
      copied_resource_school_id: school_id,
      # resource: self.class.name
    )
    if copy_processes.any?
      return copy_processes.last.status_label
    else
      return "コピー"
    end
  end

  def active_body
    if self.body_type == 0 && self.body.present?
      self.body
    elsif self.body_type == 1 && self.ck_body.present?
      self.ck_body
    else
      ''
    end
  end

  def import_info(file)
    imported_data_count = 0

    learns_attributes = {}
    prerequisits_attributes = {}
    targets_attributes = {}

    current_attribute_state = :learns_attributes
    current_attributes_index = 0

    import_errors = []

    self.each_row(file, ->(_){
      raise ImportException.new "何もインポートしませんでした" if imported_data_count == 0
    }) do |row, importer|
      unless importer.first_row?
        if(!row.key?("ユーザー書き込み部分") || !row.key?("項目"))
          import_errors.push("フォーマットが不正です。")
          raise ImportException.new "フォーマットが不正です。"
        end

        value = row["ユーザー書き込み部分"]

        if value.blank? && ["概要", "コースタイトル"].include?(row["項目"])
          import_errors.push("#{importer.current_line}行目にデータが空です")
          raise ImportException.new "#{importer.current_line}行目にデータが空です"
        end

        case row["項目"]
        when "コースタイトル"
          self.name = value
        when "概要"
          self.description = value
        when "目的"
          current_attributes_index = -1
          current_attribute_state = :learns_attributes
          next if value.blank?

          current_attributes_index += 1
          learns_attributes["#{current_attributes_index}"] = {
            "name"=>"#{value}",
            "order"=>"#{current_attributes_index}"
          }
        when "条件"
          current_attributes_index = -1
          current_attribute_state = :prerequisits_attributes
          next if value.blank?

          current_attributes_index += 1
          prerequisits_attributes["#{current_attributes_index}"] = {
            "name"=>"#{value}",
            "order"=>"#{current_attributes_index}"
          }
        when "対象者"
          current_attributes_index = -1
          current_attribute_state = :targets_attributes
          next if value.blank?

          current_attributes_index += 1
          targets_attributes["#{current_attributes_index}"] = {
            "name"=>"#{value}",
            "order"=>"#{current_attributes_index}"
          }
        when nil
          next if value.blank?

          current_attributes_index += 1
          current_attributes_value = {
            "name"=>"#{value}",
            "order"=>"#{current_attributes_index}"
          }
          case current_attribute_state
          when :learns_attributes
            learns_attributes["#{current_attributes_index}"] = current_attributes_value
          when :prerequisits_attributes
            prerequisits_attributes["#{current_attributes_index}"] = current_attributes_value
          when :targets_attributes
            targets_attributes["#{current_attributes_index}"] = current_attributes_value
          else
            import_errors.push("インポート項目情報は不明でした")
            raise ImportException.new "インポート項目情報は不明でした"
          end
        else
          import_errors.push("インポートデータは不明でした")
          raise ImportException.new "インポートデータは不明でした"
        end
        imported_data_count += 1
      end
    end

    if import_errors.empty?
      ActiveRecord::Base.transaction do
        assumed_learner_params = {
          "learns_attributes" => learns_attributes,
          "targets_attributes" => targets_attributes,
          "prerequisits_attributes" => prerequisits_attributes
        }

        self.save_assumed_learner(assumed_learner_params)
        self.save!
      end

      nil
    else
      import_errors
    end
  end

  # {"learns_attributes"=>{"0"=>{"name"=>"韓国の歴史が学べる", "order"=>"1"}, "1"=>{"name"=>"歴史と韓国語の表現の関係について学べる", "order"=>"2"}, "2"=>{"name"=>"", "order"=>"3"}}, "targets_attributes"=>{"0"=>{"name"=>"韓国の歴史に興味のある方", "order"=>""}, "1"=>{"name"=>"美しい韓国語を習得したい方", "order"=>""}, "2"=>{"name"=>"よりレベルの高い韓国語会話技術を身につけたい方", "order"=>""}}, "prerequisits_attributes"=>{"0"=>{"name"=>"PCかスマホを持っていること", "order"=>""}, "1"=>{"name"=>"日本語が理解できること", "order"=>""}, "2"=>{"name"=>"インターネットの環境が整っている", "order"=>""}}}
  def save_assumed_learner(params)
    self.learns.destroy_all
    self.targets.destroy_all
    self.prerequisits.destroy_all

    self.update!(params)
  end

  def list_names_under(list_name)
    root_list = self.lists.find_by(name: list_name)
    return "" unless root_list

    root_id = root_list.id
    ancestry_prefix = root_id.to_s

    descendant_lists = self.lists.select do |list|
      list.ancestry&.split('/')&.include?(ancestry_prefix)
    end

    descendant_lists.map(&:name)
  end

  def list_checked
    self.lists&.where(ancestry: nil).map do |list|
      {
        list.name => list_names_under(list.name).join(", ")
      }
    end
  end

  def list_names_summary_excluding_special
    exclude_keys = ["学校の種類", "大学"]

    lists.where(ancestry: nil).map do |list|
      { list.name => list_names_under(list.name).join(", ") }
    end.reject { |hash| exclude_keys.include?(hash.keys.first) }
  end
end
