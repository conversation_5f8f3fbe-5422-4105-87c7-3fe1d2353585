class Meeting::PaymentHistory < ApplicationRecord
  self.table_name = "meeting_payment_histories"

  acts_as_paranoid

  belongs_to :user
  belongs_to :ticket, class_name: 'Meeting::Ticket'
  belongs_to :voucher, class_name: 'Meeting::Coupon', optional: true

  enum ticket_type: { purchased: 0, bonus: 1 }

  scope :by_teacher, ->(teacher) {
    joins(:ticket).where(meeting_tickets: { teacher_id: teacher.id })
  }

  scope :by_school_id, -> (school_id) { where(school_id: school_id) }
end
