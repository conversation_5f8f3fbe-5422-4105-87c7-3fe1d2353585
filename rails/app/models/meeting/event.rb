class Meeting::Event < ApplicationRecord
  CONTENT_OPTIONS = [
    [I18n.t('meeting.content.exam'), 'exam'],
    [I18n.t('meeting.content.grammar'), 'grammar'],
    [I18n.t('meeting.content.speaking'), 'speaking'],
    [I18n.t('meeting.content.writing'), 'writing'],
    [I18n.t('meeting.content.other'), 'other']
  ].freeze

  acts_as_paranoid

  belongs_to :teacher, optional: true
  belongs_to :user
  belongs_to :meeting_category, class_name: 'Meeting::Category', foreign_key: :category_id

  has_many :messages, class_name: 'Meeting::Message', foreign_key: :event_id, dependent: :destroy
  has_one :feedback, class_name: 'Meeting::Feedback', foreign_key: :event_id, dependent: :destroy
  has_one :ticket_usage, class_name: 'Meeting::TicketUsage', foreign_key: :event_id, dependent: :destroy

  enum status: { pending: 0, approved: 1, rejected: 2, canceled: 3 }

  validate :teacher_schedule_available, if: -> { teacher_id.present? && start_at.present? && end_at.present? }

  scope :waiting_for_reservation, -> { where("start_at > ?", Time.zone.now).where(status: :pending).order(start_at: :asc) }
  scope :past_events, -> { where("end_at < ?", Time.zone.now).where(status: :approved).order(start_at: :asc) }
  scope :today_events, -> { where(start_at: Time.zone.today.all_day).where(status: :approved).order(start_at: :asc) }
  scope :upcoming_events, -> { where("start_at > ?", Time.zone.now).where(status: :approved).order(start_at: :asc) }
  scope :nearest_events, -> {
    tz = Time.zone.tzinfo.identifier
    order(Arel.sql("ABS(TIMESTAMPDIFF(SECOND, start_at, CONVERT_TZ(NOW(), 'SYSTEM', '#{tz}')))")).limit(5)
      .where(status: :approved)
  }

  scope :filled, -> { where(status: [:pending, :approved]) }
  scope :date_eq, -> (date) do
    parsed_date = date.is_a?(String) ? Date.parse(date) : date
    where(start_at: parsed_date.beginning_of_day..parsed_date.end_of_day)
  end

  scope :by_school_id, -> (school_id) { where(school_id: school_id) }

  before_validation -> {
    if start_at && end_at && end_at < start_at
      self.end_at = start_at.end_of_day
    end
  }
  before_save :create_zoom_meeting, if: -> { status_changed? && approved? }

  ransacker :custom_status, formatter: proc { |v| v.to_s } do
    now = Time.zone.now.to_s(:db)
    Arel.sql(
      "CASE " \
      "WHEN status = 1 AND end_at < '#{now}' THEN 'done' " \
      "ELSE CAST(status AS CHAR) " \
      "END"
    )
  end

  # todo: hot fix ticket counter
  def ticket_number
    super || (end_at - 30.minutes > start_at ? 2 : 1)
  end

  def ongoing?
    start_at <= Time.zone.now && end_at >= Time.zone.now && !rejected? && !canceled?
  end

  def content_value
    CONTENT_OPTIONS.to_h.invert[content]
  end

  def create_zoom_meeting
    oauth_service = Zoom::OauthService.new(**teacher.meeting_setting.zoom_config)
    zoom_service = Zoom::MeetingService.new(oauth_service)

    if zoom_service.instance_variable_get(:@access_token).nil?
      errors.add(:base, "Zoomの接続に失敗しました。管理者にお問い合わせください。")
      throw(:abort)
    end

    response = zoom_service.create_meeting(
      topic: "Meeting with #{user&.name} created by #{teacher&.name}",
      start_time: start_at,
      duration:((end_at - start_at) / 60).to_i,
    )

    self.meet_data = response
  end

  def end?
    end_at && end_at < Time.zone.now
  end

  def done?
    approved? && Time.zone.now > end_at
  end

  def date
    start_at.to_datetime
  end

  def date_string
    date.to_date.to_s
  end

  def period
    [start_at.strftime("%H:%M"), end_at.strftime("%H:%M")]
  end

  def self.ransackable_scopes(auth_object = nil)
    %i(date_eq)
  end

  def has_feedback_from?(user)
    feedback&.user_id == user.id
  end

  def feedback_from(user)
    feedback if feedback&.user_id == user.id
  end

  def teacher_schedule_available
    return if persisted? && !start_at_changed? && !end_at_changed? && !teacher_id_changed?

    conflicts = Meeting::Event.filled.where(teacher_id: teacher_id)
                              .where("(start_at < ? AND end_at > ?) OR
                                     (start_at < ? AND end_at > ?) OR
                                     (start_at >= ? AND end_at <= ?)",
                                     end_at, start_at,      # Event starts before this one ends and ends after this one starts
                                     start_at, start_at,    # Event starts before and overlaps with this one's start
                                     start_at, end_at)      # Event is completely within this one's timeframe

    if conflicts.exists?
      conflict = conflicts.first
      conflict_time = "#{conflict.start_at.strftime("%H:%M")} - #{conflict.end_at.strftime("%H:%M")}"
      conflict_date = conflict.start_at.strftime("%Y年%-m月%-d日")
      errors.add(:base, "申し訳ありませんが、#{conflict_date}の#{conflict_time}は、既に別の予約が入っています。別の時間帯をお選びください。")
    end
  end

  def next_scheduled
    teacher.meeting_events.where(user_id: user_id).where(status: :approved).where( "start_at > ?", end_at).first&.start_at
  end

  def student_name
    user.name
  end

  def rating
    review_star || 0
  end

  def grade
    # TODO: implement grade
  end

  def past_meetings_count
    teacher.meeting_events.where(user_id: user_id).past_events.size
  end

  def first_meeting_date
    teacher.meeting_events.where(user_id: user_id).order(start_at: :asc).first&.start_at
  end

  def feedback_summary
    {
      positive_feedback: feedback&.positive_feedback,
      improvement_feedback: feedback&.improvement_feedback,
      learnings: feedback&.learnings
    }
  end

  def can_cancel?
    return false if canceled? || end? || rejected?

    start_at - (teacher.meeting_setting&.limit_cancel_period_hours || 0).hours >= Time.zone.now
  end
end
