class Meeting::WorkTime < ApplicationRecord
  acts_as_paranoid

  belongs_to :teacher

  scope :in_period, ->(ranger) { where(date: ranger).or(where(work_type: :circle)) }
  scope :from_today, ->  { where(date: Date.today..).or(where(work_type: :circle)) }

  enum work_type: { circle: 0, one_time: 1, work_off: 2 }

  after_initialize :reset_circle_wdays
  before_validation :set_default_times

  validates :start_time, presence: true, if: -> { one_time? || circle? }

  def display_wdays
    if circle?
      circle_wdays.map do |num|
        I18n.t("date.abbr_day_names")[num.to_i]
      end.join(", ")
    else
      I18n.l(date) if date
    end
  end

  def display_work_type
    work_off? ? "利用不可" : "利用可能"
  end

  private

  def reset_circle_wdays
    value = circle_wdays.present? ? circle_wdays.select { |w| w.present? } : []
    self.circle_wdays = value if circle_wdays != value
  end

  def set_default_times
    return unless one_time? || work_off?

    self.start_time = "9:00" if start_time.blank?
    self.end_time = "19:00" if end_time.blank?
  end
end
