class CoursePurchase < ApplicationRecord
  belongs_to :course
  belongs_to :user
  belongs_to :school
  belongs_to :order_item, class_name: 'OrderItem', foreign_key: 'order_item_id'
  has_many :course_purchase_invoices
  after_create_commit :send_mail_for_success
  after_create_commit :notify_to_admins

  validates :terms_of_use, :acceptance => true

  before_destroy :stop_service

  # 未払金
  STATUS_PENDING = 1

  # 支払い済み
  STATUS_DONE = 2

  # 契約停止
  STATUS_STOPED = 3

  # 体験中
  STATUS_TRIALING = 4

  # 強制設定しない
  FORCE_STATUS_NONE = 0

  # 強制再開
  FORCE_STATUS_START = 1

  # 強制停止
  FORCE_STATUS_STOP = 2

  def force_status_start?
    self.force_status == FORCE_STATUS_START
  end

  def force_status_none?
    self.force_status == FORCE_STATUS_NONE
  end

  def installment_next_pay?
    return false unless self.force_status_none?
    self.installment_count_total > 1 && self.installment_count_total > self.installment_count_current
  end

  def stoped?
    self.status == STATUS_STOPED
  end

  def done?
    self.status == STATUS_DONE
  end

  def pending?
    self.status == STATUS_PENDING
  end

  def create_payment
    error = nil
    ActiveRecord::Base.transaction do
      new_paied_price = self.price / self.installment_count_total

      charge = Stripe::Charge.create({
        :currency        => "jpy",
        :customer        => self.stripe_customer_id,
        :description     => "Buy course #{self.course.name} #{self.installment_count_current}/#{self.installment_count_total}",
      }.merge(
        self.course.school.get_transfer_data_for_charge(new_paied_price)
      ))

      # subscription_invoice保存
      course_purchase_invoices = CoursePurchaseInvoice.find_or_initialize_by(
        user: self.user,
        school_id: self.school_id,
        course_purchase_id: self.id,
        course_id: self.course.id,
        stripe_charge_id: charge["id"]
      )

      course_purchase_invoices.amount_paid = new_paied_price
      course_purchase_invoices.paied_at = Time.at(charge["created"])
      course_purchase_invoices.save

      self.stripe_customer_id = self.stripe_customer_id
      self.status = STATUS_DONE
      self.paied_at = Time.current
      self.installment_count_current = self.installment_count_current + 1
      self.paied_price = self.paied_price + new_paied_price
      self.save

      enrollment = self.user.enrollments.find_or_initialize_by(course: self.course, enrollment_type: self.course.type_is)
      enrollment.save
    rescue Exception => e
      puts e
      error = e
      raise ActiveRecord::Rollback
    end
    if error.present?
      self.payment_error = error.message || 'UNKNOW ERROR'
      self.status = STATUS_PENDING
      self.save

      # お支払い失敗の場合、サービスを停止します。
      self.stop_service
    end
  end

  def update_installment_status
    # お支払い完了
    return if self.installment_count_total <= self.installment_count_current

    # 終了
    return if self.stoped?

    # 時間になっていません
    return if Time.now() < ( self.start_at + self.installment_count_current * 30.days)
    
    create_payment
  end

  def stop_service
    self.delete_stripe_subscription
    self.user.enrollments.where(course: self.course).destroy_all
  end

  def delete_stripe_subscription
    unless self.stripe_subscription_id.blank?
      # ストライプ削除
      Stripe::Subscription.delete(self.stripe_subscription_id)
    end
  end

  def delete_payment
    return if self.status == STATUS_STOPED
    ActiveRecord::Base.transaction do
      self.delete_stripe_subscription
      # プレミアムサービスに関連するコーザを削除
      self.stop_service
      
      self.status = STATUS_STOPED
      self.save
    end
  end

  def get_last_installment_invoice
    # 前のお支払い取ります
    CoursePurchaseInvoices.where(
      user_id: self.user_id,
      school_id: self.school_id,
      course_purchase_id: self.id,
      course_id: self.course.id,
    ).order(paied_at: :desc).first
  end

  def change_token(stripe_email, stripe_token)
    customer = Stripe::Customer.create(
      :email => stripe_email,
      :source  => stripe_token
    )

    self.stripe_customer_id = customer.id
    self.save!

    self.update_installment_status
  end

  private
  def send_mail_for_success
    CoursePurchaseSuccessMailWorker.perform_async(self.id)
  end

  def notify_to_admins
    User.admins.each do |admin|
      admin.notifications.create action: "course_purchase", target: self, school_id: self.course.school_id
    end
  end
end
