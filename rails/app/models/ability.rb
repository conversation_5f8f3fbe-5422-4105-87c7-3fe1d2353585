class Ability
  include CanCan::Ability
  prepend Draper::CanCanCan

  def initialize(user)
    @user = user || User.new
    guest

    can :manage, :all
    if @user.is_admin?
      admin
    elsif @user.is_manager_or_employer?
      manager @user
    end
  end

  def guest
    # can :read, :all
  end

  def admin

  end

  def manager user
    @skip_check = []
    cannot :manage, User
    cannot :manage, School
    cannot :manage, Goal
    cannot :manage, MasterMilestone
    cannot :manage, Course
    cannot :manage, Lesson
    cannot :manage, Project
    cannot :manage, Manager
    cannot :update, ExamDoneScore
    cannot :update, ExamAverageScore
    cannot :manage, Skill
    cannot :manage, SchoolEvaluation
    cannot :manage, EvaluationSheet
    cannot :manage, EvaluationUser
    cannot :manage, SchoolUser
    cannot :manage, Blog
    cannot :manage, PremiumService
    cannot :manage, AnnouncementMail
    cannot :manage, Activity
    cannot :manage, Question
    cannot :manage, Plan
    cannot :manage, Exam
    cannot :manage, List
    cannot :manage, Blog
    cannot :manage, SchoolDesign
    cannot :manage, Inquiry
    cannot :manage, UserGroup
    cannot :manage, UserBlock
    cannot :manage, Banner
    cannot :manage, Coupon
    cannot :manage, PageCustom
    cannot :manage, Term
    cannot :manage, Infor
    cannot :manage, ContactForm
    cannot :manage, Teacher
    cannot :manage, UserOnboard
    cannot :manage, <PERSON><PERSON><PERSON><PERSON>unt
    cannot :manage, SchoolPlan
    cannot :manage, I<PERSON>
    cannot :manage, Affiliate
    cannot :manage, Catalog
    cannot :manage, Doorkeeper::Application
    cannot :manage, AccessSummary
    cannot :manage, SaleManagement

    iams = user.iams.where("iams.school_id IS NOT NULL")
    if SchoolManager.loaded_school_ids.present?
      iams = iams.where(school_id: SchoolManager.loaded_school_ids)
    end
    iams.each do |iam|
      school = iam.school

      next if school.deleted?

      # スクールマネジャー
      if iam.school_manager?
        manage_school(school)
      else
        can :read, School, id: school.id

        # Manager権限
        if iam.manager?
          manage_for_blog(iam, school)
          manage_for_banner(iam, school)
          manage_for_coupon(iam, school)
          manage_for_question(iam, school)
          manage_for_activity(iam, school)
          manage_for_premium_service(iam, school)
          manage_for_goal(iam, school)
          manage_for_manager(iam, school)
          manage_for_user(iam, school)
          manage_for_user_block(iam, school)
          manage_for_plan(iam, school)
          manage_for_exam(iam, school)
          manage_for_infor(iam, school)
          manage_for_course(iam, school)
          manage_for_mail(iam, school)
          manage_for_school_design(iam, school)
          manage_for_page_custom(iam, school)
          manage_for_inquiry(iam, school)
          manage_for_contact_form(iam, school)
          manage_for_user_onboard(iam, school)
          manage_for_affiliate(iam, school)
          manage_for_contact_term(iam, school)
          manage_for_access_summary(iam, school)
          manage_for_sale_management(iam, school)
        end

        # Employer権限
        if iam.employer?
          employ_for_exams iam.employ_resource if iam.is_employ_for_resource("Exam")
          employ_for_course iam.employ_resource if iam.is_employ_for_resource("Course")
          employ_for_school_evaluation iam.employ_resource if iam.is_employ_for_resource("SchoolEvaluation")
          employ_for_goal iam.employ_resource if iam.is_employ_for_resource("Goal")
          employ_for_project iam.employ_resource if iam.is_employ_for_resource("Project")
          employ_for_premium_service iam.employ_resource if iam.is_employ_for_resource("PremiumService")
          employ_for_user_onboard iam.employ_resource if iam.is_employ_for_resource("UserOnboard")
          employ_for_page_custom iam.employ_resource if iam.is_employ_for_resource("PageCustom")
        end
      end

      # スクールによる使用する機能の制限
      school_permission(school)
    end
  end

  # スクールによる使用する機能の制限
  def school_permission(school)
    unless school.use_premium_service
      cannot :manage, PremiumService, school_id: school.id
    end

    unless school.use_course_lesson
      school.courses.each do |course|
        cannot :manage, Course, id: course.id
        cannot :manage, Lesson, id: course.skills.ids
      end
    end

    unless school.use_user_question
      cannot :manage, Question, school_id: school.id
    end

    unless school.use_blog
      cannot :manage, Blog, school_id: school.id
    end

    unless school.use_banner
      cannot :manage, Banner, school_id: school.id
    end

    unless school.allow_user_edit_group
      cannot :manage, UserBlock, school_id: school.id
    end

    unless school.use_goal
      # 上にするとレッスンの素材アップロードできないため、以下にします
      cannot :manage, Goal, school_id: school.id
    end

    unless school.use_mail
      cannot :manage, UserGroup, school_id: school.id
      cannot :manage, AnnouncementMail, school_id: school.id
    end

    unless school.use_skill
      cannot :manage, Skill, school_id: school.id
    end

    unless school.use_exam
      cannot :manage, Exam, school_id: school.id
    end
  end

  def employ_for_exams exam, skip_school=false
    return if exam.nil?
    @skip_check << "#{exam.class.name}-#{exam.id}"
    can :read, School, id: exam.school_id unless skip_school
    # can [:read], User
    can [:read, :update, :destroy], Exam, id: exam.id
    can [:create], Exam
  end

  ## for Course manager
  def employ_for_course course, skip_school=false
    return if course.nil?
    @skip_check << "#{course.class.name}-#{course.id}"
    can :read, School, id: course.school_id unless skip_school
    can [
      :read,
      :update,
      :assumed_learner,
      :save_assumed_learner,
      :introduce,
      :save_introduce,
      :setting,
      :save_setting,
      :pricing,
      :save_pricing,
      :publishing,
      :save_publishing,
      :destroy,
      :import,
      :sort,
      :purchases
    ], Course, id: course.id
    can [
      :read,
      :update,
      :destroy,
      :vimeo,
      :classroom_preview,
      :update_chapter,
      :classroom_preview,
      :link_to_exam,
      :update_published,
      :multi_publish,
      :check_vimeo,
      :multi_unpublish,
      :multi_delete,
      :multi_chapter_edit
    ], Lesson, id: course.lesson_ids
    can [:create, :index, :import, :add], Lesson
    # can [:create], Exam
    # can [:create], AnnouncementMail
    # can [:read, :update, :destroy], AnnouncementMail, id: course.announcement_mails.ids
    # can [:update, :destroy], Exam, id: course.lesson_exam_ids
    # can [:read, :update, :destroy], Question, id: course.questions.ids
    can [:index, :create], Skill
    can [:read, :update, :destroy], Skill, id: course.skills.ids
  end

  ## for Evaluation manager
  def employ_for_school_evaluation school_evaluation, skip_school=false
    return if school_evaluation.nil?
    @skip_check << "#{school_evaluation.class.name}-#{school_evaluation.id}"
    can :read, School, id: school_evaluation.school_id unless skip_school
    can [:manage, :import], EvaluationSheet, school_evaluation: {id: school_evaluation.id}
    can [:manage], EvaluationUser, school_evaluation: {id: school_evaluation.id}
    can [:manage, :import], SchoolEvaluation, id: school_evaluation.id if school_evaluation.school.limitation.can_evaluation?
  end

  ## for Goal manager
  def employ_for_goal goal, skip_school=false
    return if goal.nil?
    @skip_check << "#{goal.class.name}-#{goal.id}"
    can :read, School, id: goal.school_id
    can [:read, :import], Goal, id: goal.id
    can :manage, MasterMilestone, id: goal.master_milestones.ids
    can [:create, :index, :read], MasterMilestone
    can [:create], Course
    can :manage, Course, id: goal.master_milestones_course_ids
    can [:index, :create], Skill
    can :manage, Skill, id: goal.skills.ids
    can [:create], Exam
    can :manage, Exam, id: goal.exams.ids
    can [:create, :index], AnnouncementMail
    can :manage, AnnouncementMail, id: goal.announcement_mails.ids
    can :manage, Question, id: goal.questions.ids
    can :manage, Activity, id: goal.activities.ids
    goal.master_milestones_course.each do |course|
      employ_for_course course, true
    end
    goal.milestones_course do |course|
      employ_for_course course, true
    end
  end

  def employ_for_project project, skip_school=false
    return if project.nil?
    @skip_check << "#{project.class.name}-#{project.id}"

    can :read, School, id: project.school_id
    can :read, Project, id: project.id
    (project.added_goals + project.goals).each do |goal|
      employ_for_goal goal, true
    end
  end

  def employ_for_premium_service premium_service, skip_school=false
    return if premium_service.nil?
    @skip_check << "#{premium_service.class.name}-#{premium_service.id}"

    can :read, School, id: premium_service.school_id
    can :manage, PremiumService, id: premium_service.id
  end

  def employ_for_user_onboard user_onboard, skip_school=false
    return if user_onboard.nil?
    @skip_check << "#{user_onboard.class.name}-#{user_onboard.id}"

    can :read, School, id: user_onboard.school_id
    can :manage, UserOnboard, id: user_onboard.id
  end

  def employ_for_page_custom page_custom, skip_school=false
    return if page_custom.nil?
    @skip_check << "#{page_custom.class.name}-#{page_custom.id}"

    can :read, School, id: page_custom.school_id
    can :manage, PageCustom, id: page_custom.id
  end

  def manage_for_banner(iam, school)
    return unless iam.is_manager_for_resource("Banner")
    can [:create, :index], Banner
    can :manage, Banner, school_id: school.id
  end

  def manage_for_coupon(iam, school)
    return unless iam.is_manager_for_resource("Coupon")
    can [:create, :index], Coupon
    can :manage, Coupon, school_id: school.id
  end

  def manage_for_question(iam, school)
    return unless iam.is_manager_for_resource("Question")
    can [:create, :index], Question
    can :manage, Question, school_id: school.id
  end

  def manage_for_activity(iam, school)
    return unless iam.is_manager_for_resource("Activity")
    can [:create, :index], Activity
    can :manage, Activity, school_id: school.id
  end

  def manage_for_premium_service(iam, school)
    return unless iam.is_manager_for_resource("PremiumService")
    can [:create, :index], PremiumService
    can :manage, PremiumService, school_id: school.id
  end

  def manage_for_goal(iam, school)
    return unless iam.is_manager_for_resource("Goal")
    can [:create, :index], Goal
    can :manage, Goal, school_id: school.id
  end

  def manage_for_manager(iam, school)
    return unless iam.is_manager_for_resource("Manager")
    can [:create, :index], Manager
    can :manage, Manager, school_id: school.id
  end

  def manage_for_user(iam, school)
    return unless iam.is_manager_for_resource("User")
    can [:create, :index], User
    can :manage, User, id: school.user_ids
  end

  def manage_for_user_block(iam, school)
    return unless iam.is_manager_for_resource("UserBlock")
    can [:create, :index], UserBlock
    can :manage, UserBlock, id: school.user_ids
  end

  def manage_for_plan(iam, school)
    return unless iam.is_manager_for_resource("Plan")
    can [:create, :index], Plan
    can :manage, Plan, school_id: school.id
  end

  def manage_for_infor(iam, school)
    return unless iam.is_manager_for_resource("Infor")
    can [:create, :index], Infor
    can :manage, Infor, school_id: school.id
  end

  def manage_for_course(iam, school)
    return unless iam.is_manager_for_resource("Course")
    can [:create, :index], Course
    can :manage, Course, school_id: school.id
    can [:create, :index], Catalog
    can :manage, Catalog, school_id: school.id
    school.courses.each do |course|
      employ_for_course course
    end
  end

  def manage_for_mail(iam, school)
    return unless iam.is_manager_for_resource("AnnouncementMail") || iam.is_manager_for_resource("UserGroup")
    can [:manage], AnnouncementMail

    can [:manage, :update_status, :copy], UserGroup, school_id: school.id
    # can [:read], User, id: school.user_ids
  end

  def manage_for_blog(iam, school)
    return unless iam.is_manager_for_resource("Blog")
    can [:create, :index], Blog
    can :manage, Blog, school_id: school.id
  end

  def manage_for_exam(iam, school)
    return unless iam.is_manager_for_resource("Exam")
    can [:create, :index], Exam
    can :manage, Exam, school_id: school.id
    can [:create, :index], Skill
    can :manage, Skill, school_id: school.id
    can [:create, :index], List
    can :manage, List, school_id: school.id
  end

  def manage_for_page_custom(iam, school)
    return unless iam.is_manager_for_resource("PageCustom")
    can [:create, :index], PageCustom
    can :manage, PageCustom, school_id: school.id
  end

  def manage_for_inquiry(iam, school)
    return unless iam.is_manager_for_resource("Inquiry")
    can [:create, :index], Inquiry
    can :manage, Inquiry, school_id: school.id
  end

  def manage_for_contact_form(iam, school)
    return unless iam.is_manager_for_resource("ContactForm")
    can [:create, :index], ContactForm
    can :manage, ContactForm, school_id: school.id
  end

  def manage_for_contact_term(iam, school)
    return unless iam.is_manager_for_resource("Term")
    can [:create, :index], Term
    can :manage, Term, school_id: school.id
  end

  def manage_for_access_summary(iam, school)
    return unless iam.is_manager_for_resource("AccessSummary")
    can [:create, :index], AccessSummary
    can :manage, AccessSummary, school_id: school.id
  end

  def manage_for_user_onboard(iam, school)
    return unless iam.is_manager_for_resource("UserOnboard")
    can [:create, :index], UserOnboard
    can :manage, UserOnboard, school_id: school.id
    can :onboarding, User, id: school.user_ids
    can :read, User, id: school.user_ids
  end

  def manage_for_sale_management(iam, school)
    return unless iam.is_manager_for_resource("SaleManagement")
    can [:create, :index], SaleManagement
    can :manage, SaleManagement, school_id: school.id
  end

  def manage_for_teacher(iam, school)
    return unless iam.is_manager_for_resource("Teacher")
    can [:create, :index], Teacher
    can :manage, Teacher, school_id: school.id
  end

  def manage_for_affiliate(iam, school)
    return unless iam.is_manager_for_resource("Affiliate")
    can [:create, :index], Affiliate
    can :manage, Affiliate, school_id: school.id
  end

  def manage_for_school_design(iam, school)
    return unless iam.is_manager_for_resource("SchoolDesign")
    can [:create, :index], SchoolDesign
    can [:menu,
        :save_menu,
        :save_page_main,
        :header,
        :save_header,
        :general,
        :save_general,
        :page_main,
        :page_exam_list,
        :save_page_exam_list,
        :page_exam_show,
        :save_page_exam_show,
        :page_course_list,
        :save_page_course_list,
        :page_purchase_cart,
        :save_page_purchase_cart,
        :page_purchase_checkout,
        :save_page_purchase_checkout,
        :page_purchase_thank,
        :save_page_purchase_thank,
        :page_course_show,
        :save_page_course_show,
        :page_lesson_show,
        :save_page_lesson_show,
        :page_blog_list,
        :save_page_blog_list,
        :page_blog_show,
        :save_page_blog_show,
        :page_premium_list,
        :save_page_premium_list,
        :save_page_premium_show,
        :page_premium_show,
        :save_page_inquiry,
        :page_custom_page_sign_up,
        :save_page_custom_page_sign_up,
        :page_custom_page_sign_in,
        :save_page_custom_page_sign_in,
        :page_custom_page_course_purchases_new,
        :save_page_custom_page_course_purchases_new,
        :page_custom_page_subscriptions_new,
        :save_page_custom_page_subscriptions_new,
        :update_page_custom,
        :page_inquiry,
        :active,
        :read,
        :review,
        :update,
        :page_default_page_template,
        :save_page_default_page_template,
        :page_default_css,
        :save_page_default_css,
        :page_default_footer,
        :save_page_default_footer,
        :page_default_header,
        :save_page_default_header,
        :design_images,
        :page_teacher_show,
        :save_page_teacher_show,
    ], SchoolDesign, school_id: school.id
  end

  ## for School manager
  def manage_school school
    # prevent dupplicate check
    @skip_check << "#{school.class.name}-#{school.id}"
    return if school.deleted?

    can :read, School, id: school.id
    can [:show, :update], Manager, id: school.manager_ids
    can :destroy, Manager
    can [:create, :index], Manager
    [
      UserGroup,
      Skill,
      Blog,
      Banner,
      Question,
      UserBlock,
      Coupon,
      Goal,
      Teacher,
      PageCustom,
      Term,
      ContactForm,
      Exam,
      Infor,
      Activity,
      PremiumService,
      Inquiry,
      Course,
      SchoolDesign,
      StripeAccount,
      SchoolPlan,
      Iam,
      Affiliate,
      List,
      Catalog,
      AccessSummary,
      SaleManagement,
      UserOnboard
    ].each do |c|
      can [:create, :index], c
      can :manage, c, school_id: school.id
    end

    [
      AnnouncementMail,
    ].each do |c|
      can :manage, c
    end

    can [
      :index,
      :delete_multiple,
      :read,
      :destroy,
      :update,
      :create,
      :update_user_block,
      :impersonate,
      :restore
    ], User

    school.projects.each do |project|
      employ_for_project project, true
    end
    school.goals.each do |goal|
      employ_for_goal goal, true
    end
    school.exams.each do |exam|
      employ_for_exams exam, true
    end
    school.courses.each do |course|
      employ_for_course course, true
    end
    can [:read, :import], SchoolEvaluation, id: school.school_evaluation.id if school.limitation.can_evaluation?
    can :manage, SchoolUser, school_user: {school_id: school.id}
    can :read, Course, id: school.course_ids
    can :index, Course
  end
end
