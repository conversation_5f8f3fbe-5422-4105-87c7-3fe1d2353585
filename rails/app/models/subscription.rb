class Subscription < ApplicationRecord
  # attr_accessor :stripeEmail, :stripeToken
  attr_accessor :params
  belongs_to :user
  belongs_to :premium_service
  belongs_to :price_plan
  belongs_to :goal
  belongs_to :school

  has_many :subscription_cancellations
  has_many :queue_subscription_cancellations, -> { where(execute_date: (Time.current..)) }, class_name: 'SubscriptionCancellation'

  scope :by_school_ids, -> (school_ids) { where(school_id: school_ids) if school_ids.present? }

  acts_as_paranoid

  after_create_commit :notify_to_admin
  after_create_commit :notify_to_managers
  after_create_commit :notify_to_user

  before_destroy :stop_premium_service_force

  validates :terms_of_use, :acceptance => true

  # カードでお支払
  # カードの場合stripe_plan_id, stripe_subscription_id, stripe_customer_id, status,paied_at情報が必要
  METHOD_CARD = 1

  # 手動でお支払
  # 手動の場合method, expired_at情報が必要
  METHOD_MANUAL = 2

  # 未払金
  STATUS_PENDING = 1

  # 支払い済み
  STATUS_DONE = 2

  # 契約停止
  STATUS_STOPED = 3

  # 管理者から停止する
  FORCE_STATUS_STOPED = 5

  # 体験中
  STATUS_TRIALING = 4

  # 強制設定しない
  FORCE_STATUS_NONE = 0

  # 強制再開
  FORCE_STATUS_START = 1

  # 強制停止
  FORCE_STATUS_STOP = 2

  # Adminから強制停止
  ADMIN_FORCE_STATUS_STOP = 3
  ADMIN_FORCE_STATUS_START = 4
  STRIPE_FEE = 3.6 # Stripe 手数料（3.6%）

  CHANGE_PLAN_NAME = "Change plan" # ../frontend/src/apps/api/v2/api/subscriptions/index.ts:88

  enum purchase_type: { life_time: 1, each_month: 2 }

  class << self
    def bulk_update_stoped_at(stoped_at = Time.current)
      Subscription.transaction do
        all.find_each do |subscription|
          subscription.delete_stripe_subscription
          subscription.update!(stoped_at: stoped_at, status: 3)
        end
      end
    end
  end

  def force_status_start?
    self.force_status == FORCE_STATUS_START
  end

  def force_status_none?
    self.force_status == FORCE_STATUS_NONE
  end

  def admin_stopped?
    self.force_status == ADMIN_FORCE_STATUS_STOP
  end

  def installment_next_pay?
    return false unless self.force_status_none?
    self.installment_count_total > 1 && self.installment_count_total > self.installment_count_current
  end

  def create_payment(premium_service_id, stripe_email, payment_method_id, action_type, purchase_price, user_name, price_plan_id = nil, change_from_subscription_id: nil)
    subscription_before = change_from_subscription_id ? Subscription.find(change_from_subscription_id) : nil
    client_secret = ""
    intent_type =  "payment"
    payment_status = ""
    payment_method_val = ""
    subscription_stripe = ""

    ActiveRecord::Base.transaction do
      premium_service = PremiumService.find(premium_service_id)
      if subscription_before
        premium_service = ChangePremiumServiceCalculation.new(subscription_before, premium_service).compare
      end

      customer = if subscription_before
                   Stripe::Customer.retrieve(subscription_before.stripe_customer_id)
                 else
                   Stripe::Customer.create(
                     :email => stripe_email || self.user.email
                   )
                 end

      has_default_source = customer.default_source.present? || customer.invoice_settings.default_payment_method.present?

      if premium_service.each_month?
        if premium_service.get_membership_fee_with_tax > 0
          self.membership_fee = premium_service.get_membership_fee_with_tax
        end

        # 体験プラン - Trial plan
        if premium_service.trial_flag?
          subscription_data = {
            customer: customer.id,
            items: [
              {
                price: premium_service.stripe_plan_id
              }
            ],
            add_invoice_items: [
              {
                price: premium_service.stripe_trial_plan_id
              }
            ],
            expand: ['latest_invoice.payment_intent']
          }.merge(
            has_default_source ? {} : { payment_behavior: 'default_incomplete' }
          ).merge(
            premium_service.school.get_transfer_data_for_subscription()
          )

          if premium_service.is_compare && premium_service.get_membership_fee_with_tax > 0 && premium_service.trial_end_date.present?
            subscription_data[:add_invoice_items] = subscription_data[:add_invoice_items].push({
              price: premium_service.stripe_membership_fee_id,
            })
          end

          if premium_service.trial_days?
            subscription_data["trial_period_days"] = premium_service.trial_days
          elsif premium_service.trial_end_date? && premium_service.trial_end_date > Date.today
            subscription_data["trial_end"] = premium_service.trial_end_date.to_i.to_s
          end

          if premium_service.get_membership_fee_with_tax > 0
            if premium_service.stripe_membership_fee_id.blank?
              premium_service.send(:create_membership_fee_product)
              premium_service.save
            end

            self.membership_fee = premium_service.get_membership_fee_with_tax
            self.need_membership_fee = true

            trial_end_date = if premium_service.trial_days?
              Time.current + premium_service.trial_days.days
            elsif premium_service.trial_end_date?
              premium_service.trial_end_date
            end

            subscription_data["metadata"] ||= {}
            subscription_data["metadata"]["has_membership_fee"] = "true"

            subscription = Stripe::Subscription.create(subscription_data)


            if action_type == "change_card"
              payment_intent = Stripe::SetupIntent.create({
                customer: customer.id,
                payment_method_types: ['card'],
                usage: 'off_session'
              })
            else
              payment_intent = subscription.latest_invoice&.payment_intent if subscription.latest_invoice.respond_to?(:payment_intent)
            end

            if payment_intent.nil?
              payment_intent = Stripe::SetupIntent.create({
                customer: customer.id,
                payment_method_types: ['card'],
                usage: 'off_session'
              })

              intent_type = "setup"
            end

            if premium_service.get_membership_fee_with_tax > 0 && !(premium_service.is_compare && premium_service.trial_end_date.present?)
              Stripe::InvoiceItem.create({
                customer: customer.id,
                price: premium_service.stripe_membership_fee_id,
                description: "One-time Membership Fee - #{premium_service.name}",
                subscription: subscription.id,

                period: {
                  start: trial_end_date.to_i,
                  end: trial_end_date.to_i
                }
              })
            end

            self.membership_fee_paid_at = premium_service.is_compare ? Time.zone.now : nil
          else
            if payment_method_id.present?
              Stripe::PaymentMethod.attach(payment_method_id, { customer: customer.id })
              Stripe::Customer.update(
                customer.id,
                {
                  invoice_settings: {
                    default_payment_method: payment_method_id
                  }
                }
              )
              subscription_data[:default_payment_method] = payment_method_id
            end

            subscription = Stripe::Subscription.create(subscription_data)
            payment_intent = subscription.latest_invoice.payment_intent if subscription.latest_invoice.respond_to?(:payment_intent)
          end

          payment_status = payment_intent&.status
          payment_method_val = payment_intent&.payment_method
          subscription_stripe = subscription.id
        else
          subscription_items = [{
            price: premium_service.stripe_plan_id
          }]

          if premium_service.get_membership_fee_with_tax > 0
            self.need_membership_fee = true
            metadata = {
              has_membership_fee: "true"
            }

            Stripe::InvoiceItem.create({
              customer: customer.id,
              amount: premium_service.get_membership_fee_with_tax,
              currency: 'jpy',
              description: "One-time Membership Fee - #{premium_service.name}",
            })

            # self.membership_fee_paid_at = Time.zone.now
          end

          subscription = Stripe::Subscription.create({
            customer: customer.id,
            items: subscription_items,
            metadata: metadata,
            expand: ['latest_invoice.payment_intent'],
            currency: 'jpy',
            description: "Membership Fee 1 time when Buy Premium Service #{premium_service.id}",
            # default_payment_method: payment_method_id
          }.merge(
            has_default_source ? {} : { payment_behavior: 'default_incomplete' }
          ).merge(
            premium_service.school.get_transfer_data_for_subscription()
          ))

          if action_type == "change_card"
            payment_intent = Stripe::SetupIntent.create({
              customer: customer.id,
              payment_method_types: ['card'],
              usage: 'off_session'
            })
          else
            payment_intent = subscription.latest_invoice.payment_intent
          end

          payment_status = payment_intent.status
          payment_method_val = payment_intent.payment_method
          subscription_stripe = subscription.id
        end

        self.stripe_subscription_id = subscription.id
        self.stripe_plan_id = premium_service.stripe_plan_id

        client_secret = payment_intent&.client_secret
      else
        payment_intent = Stripe::PaymentIntent.create({
          amount: purchase_price,
          currency: "jpy",
          description: "Buy Premium Service #{premium_service_id} 1回目/#{self.installment_count_total}回",
          metadata: {
            premium_service_id: premium_service.id,
            price_plan_id: price_plan_id,
            user_id: self.user.id
          },
          # default_payment_method: payment_method_id,
          payment_method_options: {
            card: {
              request_three_d_secure: 'automatic'
            }
          }
        }.merge(
          premium_service.school.get_transfer_data_for_charge(purchase_price)
        ))

        client_secret = payment_intent&.client_secret

        # Invoice作成
        subscription_invoice = SubscriptionInvoice.new
        subscription_invoice.user = self.user
        subscription_invoice.school_id = self.school_id
        subscription_invoice.subscription_id = self.id
        subscription_invoice.premium_service_id = premium_service_id
        subscription_invoice.stripe_charge_id = payment_intent.id
        subscription_invoice.amount_paid = purchase_price

        subscription_invoice.paied_at = Time.at(payment_intent.created)
        subscription_invoice.save

        subscription_invoice
        payment_status = payment_intent.status
        payment_method_val = payment_intent.payment_method
      end

      self.stripe_customer_id = customer.id
      self.status = STATUS_DONE
      self.paied_at = Time.current
      self.school_id = SchoolManager.main_school.id
      self.purchase_type = premium_service.purchase_type
      self.price_plan_id = price_plan_id
      self.save

      # 講座をユーザーに追加する
      self.start_premium_service

      # Set bonus ticket if subscription is out of time trial
      premium_service_before = subscription_before&.premium_service

      trial_end_date =
        if premium_service_before&.trial_end_date.present? && premium_service_before&.trial_end_date?
          premium_service_before.trial_end_date
        elsif premium_service_before&.trial_days? && premium_service_before&.trial_days.present? && subscription_before&.created_at.present?
          subscription_before.created_at + premium_service_before.trial_days.days
        else
          nil
        end

      is_trialing = trial_end_date.nil? || Time.current > trial_end_date

      self.set_bonus_ticket(is_trialing) if self.school&.use_meeting_admin?
    end
    self.update_subscription_status(subscription_before.present?) if premium_service.each_month?

    if subscription_before
      subscription_before.stop_premium_service_force
      # create cancellation log
      canceller = SubscriptionCancellation.create!(subscription: subscription_before,
                                                   execute_date: Time.current)
      canceller.execute

      subscription_before.voice_draft_subscription
    end

    return {
      "client_secret" => client_secret,
      "intent_type" => intent_type,
      "payment_status" => payment_status,
      "payment_method_val" => payment_method_val,
      "subscription_stripe" => subscription_stripe
    }
  end

  def change_token(stripe_email, stripe_token)
    customer = Stripe::Customer.retrieve(self.stripe_customer_id)
    customer.email = stripe_email
    customer.source = stripe_token
    customer.save

    invoices = self.get_stripe_invoices
    last_invoice = invoices.first
    if last_invoice && last_invoice.paid == false
      last_invoice.pay

      if last_invoice.paid
        self.status = STATUS_DONE
        self.recovery_enrolments
        self.save
      end
    end
  end

  def delete_stripe_subscription
    unless self.stripe_subscription_id.blank?
      # ストライプ削除
      sub = Stripe::Subscription.retrieve(self.stripe_subscription_id)
      return if sub.status == "canceled" || sub.status == "incomplete_expired" || sub.status == "expired"

      Stripe::Subscription.cancel(self.stripe_subscription_id)
      voice_draft_subscription
    end
  end

  def delete_payment(stop_reason)
    return if self.status == STATUS_STOPED
    ActiveRecord::Base.transaction do
      self.delete_stripe_subscription
      self.membership_fee_charge_at = nil
      # プレミアムサービスに関連するコーザを削除
      self.stop_premium_service

      self.status = STATUS_STOPED
      self.stop_reason = stop_reason
      self.stoped_at = DateTime.now
      self.save
    end
  end

  def get_stripe_subscription
    @stripe_subscription ||= begin
      if self.stripe_subscription_id.present?
        Stripe::Subscription.retrieve(
          self.stripe_subscription_id
        )
      else
        nil
      end
    end
  end

  def get_stripe_invoices(with_refunds = false)
    @stripe_invoices ||= begin
      if self.stripe_subscription_id.present?
        data = Stripe::Invoice.list(
          subscription: self.stripe_subscription_id
        )

        # hotfix for specific subscription id
        if stripe_subscription_id == "sub_1RiYLTBLyLEisqmg084XahCw"
          old_stripe_data = Stripe::Invoice.list(
            subscription: "sub_1RN3tNBLyLEisqmgsFAFDIPK"
          )

          data.data = data.data + old_stripe_data.data

          return data
        end

        return data unless with_refunds

        data = data.as_json
        data["data"].each do |invoice|
          next if invoice["charge"].blank?

          charge = Stripe::Charge.retrieve(invoice["charge"])
          amount_refunded = charge.amount_refunded
          invoice["amount_refunded"] = amount_refunded if amount_refunded > 0
        end

        data
      else
        []
      end
    end


    @stripe_invoices
  end

  def get_stripe_upcoming_invoice
    @upcoming_invoice ||= begin
      if self.stripe_subscription_id.present?
        if Stripe::Invoice.respond_to?(:create_preview)
          Stripe::Invoice.create_preview({
            customer: self.stripe_customer_id,
            subscription: self.stripe_subscription_id
          })
        else
          Stripe::Invoice.upcoming(
            subscription: self.stripe_subscription_id
          )
        end
      else
        nil
      end
    end
  rescue => e
    Rails.logger.error("Error getting upcoming invoice for subscription #{self.stripe_subscription_id}: #{e.message}")
    nil
  end

  def get_last_installment_invoice
    # 前のお支払い取ります
    SubscriptionInvoice.where(
      user_id: self.user_id,
      school_id: self.school_id,
      subscription_id: self.id,
    ).order(paied_at: :desc).first
  end

  def update_installment_status
    # お支払い完了した
    return if self.installment_count_total == self.installment_count_current

    # 前のお支払い取ります
    installment_invoice = self.get_last_installment_invoice

    return if installment_invoice.blank?

    return if installment_invoice.paied_at > (Time.zone.now - 1.month)

    error = nil

    Subscription.transaction do
      # お支払い回数を更新
      installment_count_current = self.installment_count_current + 1
      purchase_price = self.purchase_price + installment_invoice.amount_paid

      charge = Stripe::Charge.create({
        :customer           => self.stripe_customer_id,
        :description        => "Buy Premium Service #{self.premium_service_id} 【#{installment_count_current}回目/#{self.installment_count_total}回】",
        :currency           => "jpy",
      }.merge(
        self.premium_service.school.get_transfer_data_for_charge(installment_invoice.amount_paid)
      ))

      # Invoice作成
      create_installment_invoice(charge)

      self.installment_count_current = installment_count_current
      self.purchase_price = purchase_price
      self.save
    rescue Exception => e
      puts e
      error = e
      raise ActiveRecord::Rollback
    end

    if error.present?
      self.payment_error = error.message || 'UNKNOW ERROR'
      self.stop_premium_service
      self.status = STATUS_PENDING
      self.create_mail_notify_subscription_pendding
      self.save
    else
      if self.status == STATUS_PENDING
        # 講座をユーザーに追加する
        self.start_premium_service

        self.payment_error = ""
        self.status = STATUS_DONE
        self.save
      end
    end
  end

  def complete_installment
    # お支払い完了した
    return if self.installment_count_total == self.installment_count_current

    self.installment_count_current = self.installment_count_total
    # self.status = STATUS_DONE
    self.save

  end

  def resume(invoice_price)
    return unless self.status_pending? && self.life_time?

    Subscription.transaction do
      subscription_invoice = SubscriptionInvoice.new
      subscription_invoice.user = self.user
      subscription_invoice.school_id = self.school_id
      subscription_invoice.subscription_id = self.id
      subscription_invoice.premium_service_id = self.premium_service_id
      subscription_invoice.stripe_charge_id = ""
      subscription_invoice.amount_paid = invoice_price
      subscription_invoice.paied_at = Time.zone.now
      subscription_invoice.save

      # 強制再開します。
      self.force_status = FORCE_STATUS_START
      self.status = STATUS_DONE
      self.installment_count_current = self.installment_count_total
      self.start_premium_service

      self.save
    rescue Exception => e
      puts e
      raise ActiveRecord::Rollback
    end
  end

  def stop
    return unless self.life_time?
    Subscription.transaction do
      self.stop_premium_service

      # 強制ストップ
      self.force_status = FORCE_STATUS_STOP
      self.status = STATUS_PENDING
      self.save
    rescue Exception => e
      puts e
      raise ActiveRecord::Rollback
    end
  end

  def admin_stop
    return unless self.life_time?
    Subscription.transaction do
      self.stop_premium_service

      # 強制ストップ
      self.force_status = ADMIN_FORCE_STATUS_STOP
      self.status = FORCE_STATUS_STOPED
      self.save
    rescue Exception => e
      puts e
      raise ActiveRecord::Rollback
    end
  end

  def admin_resume
    return unless self.life_time?
    return unless self.force_status == ADMIN_FORCE_STATUS_STOP
    Subscription.transaction do
      self.force_status = ADMIN_FORCE_STATUS_START
      self.status = STATUS_DONE
      self.installment_count_current = self.installment_count_total
      self.start_premium_service
      self.expired_at = Time.at(self.expired_at.to_i + (DateTime.now.to_i - self.stoped_at.to_i)).to_datetime
      self.save
    rescue Exception => e
      puts e
      raise ActiveRecord::Rollback
    end
  end

  def update_subscription_status(subscription_before = nil)
    return if self.life_time?
    return if self.method_manual?
    ActiveRecord::Base.transaction do
      stripe_subscription = Stripe::Subscription.retrieve(
        self.stripe_subscription_id
      )
      if stripe_subscription.blank?
        # Todo code here
        return
      end

      # お支払い金額を更新します。
      new_purchase_price = 0
      stripe_invoices = self.get_stripe_invoices

      if stripe_invoices.present?
        stripe_invoices.each do |stripe_invoice|
          new_purchase_price += stripe_invoice["amount_paid"]

          # subscription_invoice保存
          subscription_invoice = SubscriptionInvoice.find_or_initialize_by(
            user: self.user,
            school_id: self.school_id,
            subscription_id: self.id,
            premium_service_id: self.premium_service_id,
            stripe_invoice_id: stripe_invoice["id"]
          )
          subscription_invoice.amount_paid = stripe_invoice["amount_paid"]
          subscription_invoice.paied_at = Time.at(stripe_invoice["created"])
          subscription_invoice.save
        end
      end

      self.purchase_price = new_purchase_price
      stripe_invoice = Stripe::Invoice.retrieve(
        stripe_subscription.latest_invoice
      )

      if stripe_invoice.respond_to?(:payment_intent) && stripe_invoice.payment_intent
        payment_intent = Stripe::PaymentIntent.retrieve(stripe_invoice.payment_intent)
      end

      create_mail_notify_subscription_change(subscription_before, self) if subscription_before.present?

      case stripe_subscription.status
      when 'active'
        self.status = STATUS_DONE
        self.paied_at = Time.at(stripe_invoice.created)
        self.membership_fee_paid_at = Time.at(stripe_invoice.created)
        self.start_premium_service
      when 'trialing'
        self.status = subscription_before.present? || name == CHANGE_PLAN_NAME ? STATUS_DONE : STATUS_TRIALING
      when 'canceled'
        self.stop_premium_service
        self.status = STATUS_STOPED
      else
        if payment_intent&.status != 'requires_payment_method' && payment_intent&.status != 'succeeded'
          self.stop_premium_service
          self.status = STATUS_PENDING
          self.create_mail_notify_subscription_pendding
        end
      end

      self.save
    end
  end

  def update_subscription_data(subscription_id)
    stripe_subscription = Stripe::Subscription.retrieve(subscription_id)
    subscription = Subscription.find_by(stripe_subscription_id: subscription_id)
    stripe_invoice = Stripe::Invoice.retrieve(
      stripe_subscription.latest_invoice
    )

    if stripe_subscription.status == 'active'
      subscription.status = STATUS_DONE
      subscription.paied_at = Time.at(stripe_invoice.created)
      subscription.membership_fee_paid_at = Time.at(stripe_invoice.created)

      subscription.start_premium_service
    elsif stripe_subscription.status == 'trialing'
      subscription.status = STATUS_DONE
    elsif stripe_subscription.status == 'canceled'
      subscription.stop_premium_service
      subscription.status = STATUS_STOPED
    end

    subscription.save
  end

  def create_mail_notify_subscription_change(subscription_before, new_subscription)
    # TODO: check template mail subscription_change
    # notify_admins_of_subscription_change(subscription_before, new_subscription)
    # send_admin_subscription_change_email(subscription_before, new_subscription)
  end

  def notify_admins_of_subscription_change(subscription_before, new_subscription)
    new_subscription.school.users&.admins.each do |admin|
      admin.notifications.create(
        action: "subscription_change",
        target: subscription_before,
        school_id: subscription_before.school_id
      )
    end
  end

  def send_admin_subscription_change_email(subscription_before, new_subscription)
    admin_emails = new_subscription.school.users.admins.pluck(:email).uniq

    return if admin_emails.blank?

    NotificationMailer.subscription_change(
      admin_emails,
      {
        user_name: new_subscription.user.name,
        user_email: new_subscription.user.email,
        old_premium_service_name: subscription_before.premium_service.name,
        old_price: subscription_before.premium_service.price,
        new_premium_service_name: new_subscription.premium_service.name,
        new_price: new_subscription.premium_service.price,
        school_id: new_subscription.school_id,
        changed_at: Time.zone.now,
        school_name: new_subscription.school.name
      }
    ).deliver_later
  end

  def create_mail_notify_subscription_pendding
    SubscriptionNotifyAdminSubscriptionPenddingMailWorker.perform_async(self.id)
    SubscriptionNotifyUserSubscriptionPenddingMailWorker.perform_async(self.id)
  end

  def method_card?
    self.method == METHOD_CARD
  end

  def method_manual?
    self.method == METHOD_MANUAL
  end

  def stoped?
    self.status == STATUS_STOPED
  end

  def done?
    self.status == STATUS_DONE
  end

  def pending?
    self.status == STATUS_PENDING
  end

  def trialing?
    self.status == STATUS_TRIALING
  end

  def active?
    # if self.life_time?
    #   if self.expired_at && Date.today > self.expired_at
    #     return false
    #   end
    #   if  self.status == STATUS_STOPED || self.status == FORCE_STATUS_STOPED
    #     return false
    #   end
    # end
    return true if self.life_time?
    return self.done? if method_card?
    self.expired_at.blank? || self.expired_at >= Date.today
  end

  def stop_premium_service_force
    self.delete_stripe_subscription
    self.premium_service.execute_delete_enrolment_for_user(self.user, true)
  end

  def stop_premium_service
    self.premium_service.execute_delete_enrolment_for_user(self.user)
  end

  def start_premium_service
    self.premium_service.create_enrolment_for_user(self.user)
  end

  def set_bonus_ticket(is_trialing)
    return unless is_trialing # check case change sub

    if self.premium_service.each_month?
      return if self.premium_service.trial_flag || self.premium_service&.bonus_ticket_number.blank? || self.premium_service&.bonus_ticket_number&.zero?

      self.user.payment_histories&.bonus&.create(
        school_id: self.school_id,
        ticket_number: self.premium_service&.bonus_ticket_number
      )
    else
      return if self.price_plan.bonus_ticket_number.blank? || self.price_plan.bonus_ticket_number&.zero?
      self.user.payment_histories&.bonus&.create(
        school_id: self.school_id,
        ticket_number: self.price_plan.bonus_ticket_number
      )
    end
  end

  def change_next_price amount
    begin
      current_subscription = Stripe::Subscription.retrieve(self.stripe_subscription_id)
      updated_subscription = Stripe::Subscription.update(
        self.stripe_subscription_id,
        {
          items: [{
            id: current_subscription.items.data[0].id,
            price_data: {
              currency: current_subscription.items.data[0].price.currency,
              product: current_subscription.items.data[0].price.product,
              recurring: {interval: 'month'},
              unit_amount: amount,
            }
          }],
          proration_behavior: 'none'
        }
      )
      return true
    rescue
      return false
    end
  end

  def recovery_enrolments
    Subscription.transaction do
      self.premium_service.recovery_enrolment_for_user(self.user)
      self.stop_reason = ''
      self.stoped_at = nil
      self.status = STATUS_DONE
      self.save
    end
  end

  def get_enrollments
    course_ids = self.premium_service.premium_service_courses.pluck(:course_id)
    Enrollment.where(user_id: user.id, course_id: course_ids)
  end

  def create_installment_invoice(charge)
    # Invoice作成
    subscription_invoice = SubscriptionInvoice.new
    subscription_invoice.user = self.user
    subscription_invoice.school_id = self.school_id
    subscription_invoice.subscription_id = self.id
    subscription_invoice.premium_service_id = self.premium_service_id
    subscription_invoice.stripe_charge_id = charge["id"]
    subscription_invoice.amount_paid = charge["amount"]
    subscription_invoice.paied_at = Time.at(charge["created"])
    subscription_invoice.save

    subscription_invoice
  end

  def create_manual_invoice(purchase_price)
    return unless self.method_manual?

    # Invoice作成
    subscription_invoice = SubscriptionInvoice.new
    subscription_invoice.user = self.user
    subscription_invoice.school_id = self.school_id
    subscription_invoice.subscription_id = self.id
    subscription_invoice.premium_service_id = self.premium_service_id
    subscription_invoice.stripe_charge_id = ""
    subscription_invoice.amount_paid = purchase_price
    subscription_invoice.paied_at = Time.now
    subscription_invoice.save

    subscription_invoice
  end

  def notify_to_admin
    User.admins.each do |admin|
      admin.notifications.create target: self, action: "subscription_create", school_id: self.school_id
    end
  end

  def notify_to_managers
    self.school.school_managers.each do |manager|
      manager.notifications.create target: self, action: "subscription_create", school_id: self.school_id
    end

    SubscriptionNotifyAdminSubscriptionCreateMailWorker.perform_async(self.id)
  end

  def notify_to_user
    # メール設定は完了してない場合、メール通知しません
    return unless self.premium_service.step_subscription_create_mail_finished?

    subscription_create_mail_title = replace_subscription_create_mail_content(self.premium_service.subscription_create_mail_title)
    subscription_create_mail_body = replace_subscription_create_mail_content(self.premium_service.subscription_create_mail_body)

    announcement_mail = AnnouncementMail.new
    announcement_mail.title = subscription_create_mail_title
    announcement_mail.body = subscription_create_mail_body
    announcement_mail.custom_recipients = [self.user]
    announcement_mail.draft = false
    announcement_mail.context = self.school
    announcement_mail.save

  end

  def end_date
    return unless self.status == Subscription::STATUS_STOPED

    stoped_at
  end

  # TODO: implement this method by stripe
  def expired_at_from_stripe
    Time.zone.now
  end

  def stripe_customer_default_card_number
    value = super
    if value.blank? && self.stripe_customer_id.present?
      get_stripe_card_information

      return super
    end

    value
  end

  def stripe_customer_default_card_expired_at
    value = super
    if value.blank? && self.stripe_customer_id.present?
      get_stripe_card_information

      return super
    end

    value
  end

  def get_stripe_card_information
    return unless self.stripe_customer_id

    customer = Stripe::Customer.retrieve(self.stripe_customer_id)
    if customer.default_source
      card = Stripe::Customer.retrieve_source(customer.id, customer.default_source)
      self.update_columns(
        stripe_customer_default_card_number: card.last4,
        stripe_customer_default_card_expired_at: "#{card.exp_month}/#{card.exp_year}"
      )
    elsif customer.invoice_settings.default_payment_method
      payment_method = Stripe::Customer.retrieve_payment_method(customer.id, customer.invoice_settings.default_payment_method)
      card = payment_method.card

      self.update_columns(
        stripe_customer_default_card_number: card.last4,
        stripe_customer_default_card_expired_at: "#{card.exp_month}/#{card.exp_year}"
      )
    end

  rescue => e
    if Rails.env.production?
      SlackClient.client.ping("Subscription fetch card error: #{self.id}: #{e.message}")
    end

    nil
  end

  def voice_draft_subscription
    Stripe::Invoice.list(subscription: stripe_subscription_id, limit: 100).each do |invoice|
      if invoice.status == 'open'
        invoice.void_invoice
      end
    end

  rescue => e
    if Rails.env.production?
      SlackClient.client.ping("voice_draft_subscription error: #{self.id}: #{e.message}")
    end

    nil
  end

  private

  def replace_subscription_create_mail_content(text_body)
    text_body = text_body.gsub('[user_name]', self.user.name_or_email)
    text_body = text_body.gsub('[premium_service_name]', self.premium_service.name)
    text_body = text_body.gsub('[school_name]', self.school.name)

    text_body
  end

  def get_membership_fee_charge_datetime
    return nil unless premium_service.trial_flag?

    if premium_service.trial_days?
      Time.current + premium_service.trial_days.days
    elsif premium_service.trial_end_date?
      premium_service.trial_end_date
    end
  end
end
