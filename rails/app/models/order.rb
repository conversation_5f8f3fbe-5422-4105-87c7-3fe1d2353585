class Order < ApplicationRecord
  belongs_to :user
  has_many :order_items, dependent: :destroy
  has_one :payment, dependent: :destroy

  def actual_paid_amount(order_item)
    order_item.price_at_time - self.discount_amount(order_item) + self.tax_amount(order_item)
  end

  def discount_amount(order_item)
    if order_item.coupon.present?
      order_item.coupon.cal_discount_price(order_item.price_at_time)
    else
      0
    end
  end

  def tax_amount(order_item)
    # Tax rate (e.g. 10% = 0.1)
    tax_rate = Settings.tax.to_f

    # Total pre-tax amount (after discount) for the whole order
    total_pre_tax_amount = self.order_items.sum do |item|
      item.price_at_time - discount_amount(item).to_f
    end

    return 0 if total_pre_tax_amount.zero?

    # Pre-tax value of the current order_item
    item_pre_tax = order_item.price_at_time - discount_amount(order_item).to_f

    # Proportion of this item in the total order
    ratio = item_pre_tax / total_pre_tax_amount

    # Total tax = total_amount - pre_tax_amount
    total_tax = self.total_amount - total_pre_tax_amount

    # Return the allocated tax amount corresponding to the item
    (total_tax * ratio).abs.round()
  end


  def pre_tax_amount
    (self.total_amount / (1 + Settings.tax)).round()
  end
end
