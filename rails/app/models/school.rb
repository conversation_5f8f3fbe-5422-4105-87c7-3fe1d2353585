class School < ApplicationRecord
  include Manageable
  include Importable
  include IpRestrictable
  include SchoolOnboarding
  include SchoolConstants

  mount_uploader :design_logo, ImageUploader

  MAIN_DOMAIN_ID = ENV['MAIN_DOMAIN_ID'] || 30

  SUBSCRIPTION_NO = 0
  SUBSCRIPTION_OK = 1

  SCHOOL_PRICE_STATUS_NONE = 0
  SCHOOL_PRICE_STATUS_ACTIVE = 1
  SCHOOL_PRICE_STATUS_PENDING = 2

  mount_uploader :image, ImageUploader
  has_many :courses, dependent: :destroy
  has_many :pickup_resources, dependent: :destroy
  has_many :iams, dependent: :destroy
  has_many :school_summaries, dependent: :destroy
  has_many :lessons, through: :courses
  has_many :enrollments, through: :courses
  has_many :projects, dependent: :destroy
  has_many :goals_users, through: :goals, source: :users
  has_many :projects_users, through: :projects, source: :users
  has_many :courses_users, -> { distinct }, through: :projects, source: :users
  has_many :announcement_mails, as: :context, dependent: :destroy
  has_many :premium_services
  has_many :activities, dependent: :destroy
  has_many :questions, dependent: :destroy
  has_many :goals, dependent: :destroy
  has_many :master_milestones, through: :goals
  has_many :school_users, dependent: :destroy
  has_many :users, through: :school_users
  has_many :plans, dependent: :destroy
  has_many :master_plans, through: :plans, source: :original
  has_many :plan_goals, through: :plans, source: :goals
  has_many :tutorials
  has_many :blogs
  has_many :banners
  has_many :reviews, dependent: :destroy
  has_many :catalogs
  has_many :skills, dependent: :destroy
  has_many :skill_items, through: :skills
  has_many :exams, dependent: :destroy
  has_many :inquiries, dependent: :destroy
  has_many :lists, dependent: :destroy
  has_many :subscriptions, dependent: :destroy
  has_many :exam_purchases, dependent: :destroy
  has_many :sale_managements, dependent: :destroy
  has_many :course_purchases, dependent: :destroy
  has_many :merchant_transactions, through: :stripe_account
  has_many :user_blocks, dependent: :destroy
  has_many :user_groups, dependent: :destroy
  has_many :revisions, dependent: :destroy
  has_many :infors, dependent: :destroy
  has_many :page_customs, dependent: :destroy
  has_many :teachers, dependent: :destroy
  has_many :contact_forms, dependent: :destroy
  has_many :user_contacts, dependent: :destroy
  has_many :access_summaries, dependent: :destroy
  has_many :block_emails, dependent: :destroy
  has_many :coupons, dependent: :destroy
  has_many :school_plan_subscriptions, dependent: :destroy
  has_many :user_onboards, dependent: :destroy
  has_many :messages, dependent: :destroy
  has_many :affiliates, dependent: :destroy

  has_many :school_designs, dependent: :destroy
  has_one :limitation, dependent: :destroy
  has_one :school_exam, dependent: :destroy
  has_many :terms, dependent: :destroy
  has_one :stripe_account
  has_one :billing
  belongs_to :vimeo_account
  has_many :vimeo_logs
  has_many :prompts
  has_many :ai_chat_questions
  has_many :step_emails
  has_many :mail_sender_settings
  has_many :signup_email_templates
  has_many :custom_texts
  has_many :ai_chat_settings, dependent: :destroy, class_name: "AiChatSetting", inverse_of: :school
  has_many :ai_platform_schools, dependent: :destroy, inverse_of: :school
  belongs_to :ai_platform_school, optional: true
  has_many :ai_tutor_agents, dependent: :destroy
  has_many :ai_tutor_prompts, through: :ai_tutor_agents
  has_many :ai_tutor_tools, through: :ai_tutor_agents
  has_many :ai_tutor_rag_configs, through: :ai_tutor_agents
  has_many :tickets, class_name: "Meeting::Ticket", inverse_of: :school
  has_many :meeting_coupons, class_name: "Meeting::Coupon", inverse_of: :school

  accepts_nested_attributes_for :limitation
  accepts_nested_attributes_for :ai_platform_schools

  after_create :create_first_project
  after_create :create_school_exam
  after_create :create_school_design
  after_initialize :create_limition

  before_destroy :check_master_school, if: -> { !destroyed_by_association && master? }

  serialize :partner_roles, Hash
  before_save :up_role, if: -> { self.partner_roles_changed? }
  before_save :down_role, if: -> { self.partner_roles_changed? }

  scope :valid, -> { where(deleted: false) }
  scope :master, -> { where(master: true) }
  scope :saleable, -> { joins(:stripe_account).where.not(contract_type: :not_set) }

  validates :name, length: { maximum: Settings.max_length.name }, presence: true
  validates :description, length: { maximum: Settings.max_length.description }, allow_blank: true
  validates :body, length: { maximum: Settings.max_length.body }, allow_blank: true
  validates_date :duration_start_at
  validates :duration, numericality: { only_integer: true, min: 1 }
  validates :subdomain, uniqueness: true, allow_blank: true
  validates :domain, uniqueness: true, allow_blank: true
  validate :valid_domain
  validate :valid_whitelisted_ips

  enum contract_type: [:not_set, :rate, :subscription]

  attr_accessor :copy_plans, :rate_value

  before_save :remove_blank_ips
  serialize :whitelisted_ips, Array

  VALID_DOMAIN_REGEX = /^(xn\-\-)?[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,14}$/i
  VALID_SUBDOMAIN_REGEX = /^(xn\-\-)?[a-z0-9]+([\-]{1}[a-z0-9]+)*$/i
  WHITELISTED_IP = /^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$/

  def active_user_onboard
    self.user_onboards.where(actived: true).first
  end

  def school_design(preview_code = nil)
    if preview_code.present?
      result = self.school_designs.where(preview_code: preview_code).first
      return result if result.present?
    end

    self.school_designs.where(actived: true).first
  end

  def logo_url
    self&.design_logo&.url
  end

  def is_sellable?
    self.merchant_info.present? || self.merchant_info_uid.present?
  end

  def can_receive_payments?
    merchant_info = self.merchant_info
    self.is_sellable? && merchant_info.uid? && merchant_info.provider? && merchant_info.access_code? && merchant_info.publishable_key?
  end

  def create_first_project
    self.projects.create(name: 'first project')
  end

  def exam_ids
    (self.goals.joins(:exams).pluck("exams.id") | self.courses.joins(:exams).pluck("exams.id")).uniq
  end

  def all_users
    User.where(id: self.school_users.with_deleted.pluck(:user_id))
  end

  def import_users(file, user_block=nil)
    imported_users = 0
    plans = self.plans.map { |plan| [plan.name, plan.id] }.to_h
    return self.each_row(file, ->(_) {
      raise ImportException.new("制限を越えました (制限は #{limitation.users} 人までですが #{self.users.size} 人を追加しようとしています)") if limitation.users_limited? && limitation.users < self.users.size
      raise ImportException.new "何もインポートしませんでした" if imported_users == 0
    }) do |row, importer|
      importer.require_field(["name", "email", "password"])
      unless importer.first_row?
        importer.require_regex("email", URI::MailTo::EMAIL_REGEXP)
        user = User.where(email: row["email"]).first

        has_import = false

        unless user.present?
          user = User.new row.to_hash.slice(*User.updatable_attributes)
          importer.check_valid user
          user.skip_confirmation!
          user.save

          has_import = true
        end
        unless SchoolUser.where(school_id: self.id, user_id: user.id).exists?
          school_user = SchoolUser.create(school_id: self.id, user_id: user.id)
          importer.check_valid school_user

          has_import = true
        end
        if user_block.nil?
          if row["user_block"]
            user_block_id = UserBlock.where(school_id: self.id, name: row["user_block"]).first&.id
            unless user_block_id
              user_block_id = UserBlock.create(school_id: self.id, name: row["user_block"]).id
            end
            user_block_user = UserBlockUser.find_or_create_by(user_id: user.id, user_block_id: user_block_id)
          end

          has_import = true
        else
          user_block_user = UserBlockUser.find_or_create_by(user_id: user.id, user_block_id: user_block.id)
          has_import = true
        end

        imported_users += 1 if has_import
      end
    end
  end

  def soft_destroy
    self.goals.includes(:user_goals).each do |goal|
      goal.user_goals.update_all deleted: true
    end
    self.update deleted: true
  end

  def restore
    self.goals.includes(:user_goals).each do |goal|
      goal.user_goals.update_all deleted: false
    end
    self.update deleted: false
  end

  def start_of_duration date = nil
    date ||= Date.today
    diff_month = date.month - self.duration_start_at.month + ((date.year - self.duration_start_at.year) * 12)
    periods = diff_month / self.duration
    month_remain = diff_month % self.duration
    start_day = self.duration_start_at + (periods * self.duration).months
    start_day -= self.duration.months if month_remain == 0 && date.day < self.duration_start_at.day
    start_day += 1.day if start_day.day != self.duration_start_at.day
    start_day
  end

  def end_of_duration date = nil
    date ||= Date.today
    next_period_day = date + self.duration.months
    next_period_day += 1.day if next_period_day.day != date.day
    start_day = start_of_duration(next_period_day)
    start_day - 1.day
  end

  def next_duration date = nil
    date ||= Date.today
    end_of_duration(date) + 1.day
  end

  def previous_duration date = nil
    date ||= Date.today
    start_of_duration(date) - 1.day
  end

  def use_custom_domain
    return "domain" if self.domain.present?
    return "subdomain" if self.subdomain.present?
    return "notuse"
  end

  def school_domain
    return Settings.default_domain if self.id == MAIN_DOMAIN_ID
    return self.domain if self.domain.present?
    return "#{self.subdomain}.#{Settings.default_domain}" if self.subdomain.present?
    return Settings.default_domain
  end

  def admin_school_domain
    return "#{Settings.default_domain}/admin/schools/#{MAIN_DOMAIN_ID}" if self.id == MAIN_DOMAIN_ID
    return "#{self.domain}/admin" if self.domain.present?
    return "#{self.subdomain}.#{Settings.default_domain}/admin" if self.subdomain.present?
    return "#{Settings.default_domain}/admin/schools/#{self.id}"
  end

  def partner_roles_exist? user
    self.partner_roles.values.flatten.uniq.include?(user.id.to_s)
  end

  def remove_exam exam
    exam.destroy
  end

  def user_goal_ids
    self.goals.includes(:user_goals).pluck("user_goals.id")
  end

  def find_or_create_sample_stripe_account
    return self.stripe_account if self.stripe_account.present?

    account = Stripe::Account.create({
      email: "<EMAIL>",
      country: "JP",
      type: "custom",
      business_type: "individual",
      individual: {
        "address_kana":{
          "email": nil,
          "city":nil,
          "country":"JP",
          "line1":nil,
          "line2":nil,
          "postal_code":nil,
          "state":nil,
          "town":nil
        },
        "address_kanji":{
          "city":nil,
          "country":"JP",
          "line1":nil,
          "line2":nil,
          "postal_code":nil,
          "state":nil,
          "town":nil
        },
        "name":nil,
        "name_kana":nil,
        "name_kanji":nil,
        "phone":nil,
        "verification":{
          "document":{
            "back":nil,
            "details":nil,
            "details_code":nil,
            "front":nil
          }
        }
      },
      capabilities: {
        card_payments: {requested: true},
        transfers: {requested: true},
      },
    })
    self.build_stripe_account(merchant_info_uid: account.id).save
    self.stripe_account
  end

  def use_subscription?
    use_premium_service
  end

  def contracted?
    self.contract_type != "not_set"
  end

  def create_school_exam
    SchoolExam.find_or_create_by(school_id: self.id)
  end

  def create_school_design
    school_design = SchoolDesign.new_default("デフォルトTheme", self.id)
    school_design.actived = true
    school_design.actived_at = Time.now
    school_design.save

    self.design_menu = School::MENU_DATA_SET
    self.classroom_design_menu = School::CLASSROOM_MENU_DATA_SET
    self.design_banner = {}
    self.design_general = {}
    self.design_main_page = {}

    self.save
  end

  def user_to_day(day)
    self.users.where("#{UtilityHelper.convert_tz_sql("school_users.created_at")} <= '#{day.strftime('%Y-%m-%d')}'")
  end

  def user_count_to_day(day)
    count = self.user_to_day(day).count
    return count if count > 0
    1
  end

  def user_login_in_day(day)
    Activity.where('school_id = ?', self.id).where("#{UtilityHelper.convert_tz_sql("created_at")} = '#{day.strftime('%Y-%m-%d')}'")
  end

  def user_login_count_in_day(day)
    self.user_login_in_day(day).select("DISTINCT user_id").count
  end

  def fee_for_skill_hub(purchase_price)
    return purchase_price if self.fee_type === "notset"

    case self.fee_type
    when "percent"
      # purchase_price: ユーザーから支払った金額
      # stripe_fee: Stripe 手数料
      # skill_hub_receive_money: SkillHub にもらえる金額
      # skill_hub_percent: SkillHubの割合
      # 予算形式:  skill_hub_receive_money = ( skill_hub_percent(%) + stripe_fee % ) * purchase_price
      return Integer(purchase_price * (100 - self.fee_value + STRIPE_FEE).to_f / 100)
    when "value"
      # purchase_price: ユーザーから支払った金額
      # stripe_fee: Stripe 手数料
      # skill_hub_receive_money: SkillHub にもらえる金額
      # skill_hub_static_fee: SkillHubの定額
      # 予算形式:  skill_hub_receive_money = skill_hub_static_fee + stripe_fee % * purchase_price
      return Integer((purchase_price * STRIPE_FEE).to_f / 100 + self.fee_value)
    else
      return purchase_price
    end
  end

  def self.update_all_school_user_for_school_4
    User.all.each do |user|
      school_user = SchoolUser.find_by(user_id: user.id, school_id: MAIN_DOMAIN_ID)
      if school_user.nil?
        school_user = SchoolUser.new
        school_user.user_id = user.id
        school_user.school_id = MAIN_DOMAIN_ID
        school_user.created_at = user.created_at
        school_user.updated_at = user.created_at
        school_user.save
      end
    end
  end

  def self.update_school_user
    for school in School.all do
      course_ids = school.courses.map { |h| h.id }
      school_user_datas = Enrollment.where("course_id IN (?)", course_ids).select("user_id, MIN(created_at) as created_at").group(:user_id)

      update_count = 0
      create_count = 0

      for school_user_data in school_user_datas do
        school_user = SchoolUser.find_by(user_id: school_user_data.user_id, school_id: school.id)

        if school_user.present?
          update_count += 1

          school_user.created_at = school_user_data.created_at
          school_user.updated_at = school_user_data.created_at
        else
          create_count += 1

          school_user = SchoolUser.new
          school_user.user_id = school_user_data.user_id
          school_user.school_id = school.id
          school_user.created_at = school_user_data.created_at
          school_user.updated_at = school_user_data.created_at
        end
        school_user.save
      end

      pp "School: #{school.id}"
      pp "Update: #{update_count}"
      pp "Create: #{create_count}"
      pp "________________________"
    end
  end

  # MENU
  def show_menus(user_logined, global = true)
    can_show_menus =  self.design_menu["list"].select do | item |
      # ユーザーがないと、ログアウト出さない
      if item["kbn"] == MENU_KBN_LOGOUT && !user_logined
        false
      # ユーザーがあると、ログイン出さない
      elsif item["kbn"] == MENU_KBN_LOGIN && user_logined
        false
      else
        global ? self.is_show_menu(item) : is_show_classroom_menu(item)
      end
    end

    # グローバルメニューに表示する
    global_menus = can_show_menus.select do | item |
      item["kbn"] == MENU_KBN_MENU || item["sub_menu_key"].nil? || item["sub_menu_key"].to_s == '0'
    end

    global_menus.map do |menu|
      if menu["kbn"] != MENU_KBN_MENU
        menu
      else
        menu["menu_items"] = can_show_menus.select do | item |
          item["kbn"] != MENU_KBN_MENU && item["sub_menu_key"].present? && item["sub_menu_key"] == menu["sub_menu_key"]
        end
      end
      menu
    end

    global_menus
  end

  def is_show_menu(item)
      is_school_menu_permission(item["kbn"]) && is_menu_visibility(item)
  end

  def is_show_classroom_menu(item)
    is_school_menu_permission(item["kbn"]) && item['classroom_visibility'] == 'true'
end

  def is_menu_visibility(item)
      item['visibility'] == 'true'
  end

  def is_school_menu_permission(menu_kbn)
      case menu_kbn
      when MENU_KBN_PREMIUM_SERVICE
          self.use_premium_service
      when MENU_KBN_COURSE
          self.use_course_lesson
      when MENU_KBN_BLOG
          self.use_blog
      when MENU_KBN_TEST
          true
      when MENU_KBN_INQUIRY
          true
      when MENU_KBN_MY_CLASSROOM
          true
      when MENU_KBN_GLOBAL_QUESTION
          true
      when MENU_KBN_CUSTOM
          true
      when MENU_KBN_LOGIN
          true
      when MENU_KBN_LOGOUT
          true
      else
          true
      end
  end


  def question_partners
    user_ids = self.partner_roles["Question"] || []

    User.where(id: user_ids)
  end

  def school_managers
    User.joins(:iams)
      .where("iams.school_id = ?", self.id)
      .where("iams.resource_type IS NULL")
      .where("iams.resource_id IS NULL")
      .distinct("users.id")
  end

  def admin_and_school_managers
    admin_ids = User.admins.pluck("users.id")
    school_manager_ids = self.school_managers.pluck("users.id")

    User.where(id: admin_ids + school_manager_ids)
  end

  def is_limit_manager_count?
    self.iams.select(:user_id).distinct.count >= limit_manager_count
  end

  def limit_manager_count
    actived_school_plan_subscription.present? ? actived_school_plan_subscription.school_plan.limit_manager_count : 1
  end

  def vimeo_count
    self.vimeo_logs.count
  end

  def limit_vimeo_count_rate
    ((self.vimeo_count / self.limit_vimeo_count.to_f) * 100).round
  end

  def limit_vimeo_count
    actived_school_plan_subscription.present? ? actived_school_plan_subscription.school_plan.limit_vimeo_count : 10
  end

  def limit_ai_messages
    actived_school_plan_subscription.present? ? actived_school_plan_subscription.school_plan.limit_ai_message : 10
  end

  def actived_school_plan_subscription
    self.school_plan_subscriptions.where(status: SchoolPlanSubscription::STATUS_DONE).first
  end

  def actived_school_plan_subscription_fee(amount)
    # アクティビティがない場合、全部Edbaseにします。
    return amount if actived_school_plan_subscription.blank?

    # Add 10% tax
    commission_rate_fee_with_tax = amount * actived_school_plan_subscription.school_plan.commision_rate * 1.1 / 100
    card_commision_rate_with_tax = amount * actived_school_plan_subscription.school_plan.card_commision_rate * 1.1 / 100

    (commission_rate_fee_with_tax + card_commision_rate_with_tax).round
  end

  def get_transfer_data_for_charge(amount)
    return {
      :amount => amount
    } if actived_school_plan_subscription.blank?

    stripe_account = self.find_or_create_sample_stripe_account

    {
      :amount                   => amount,
      :application_fee_amount   => actived_school_plan_subscription_fee(amount),
      :transfer_data            => {
        :destination            => stripe_account.merchant_info_uid
      }
    }
  end

  def get_transfer_data_for_subscription()
    return {} if actived_school_plan_subscription.blank?

    stripe_account = self.find_or_create_sample_stripe_account

    # Add 10% tax (commission_rate + card_commision_rate)
    {
      :application_fee_percent => ((actived_school_plan_subscription.school_plan.commision_rate + actived_school_plan_subscription.school_plan.card_commision_rate) * 1.1).to_d,
      :transfer_data            => {
        :destination            => stripe_account.merchant_info_uid
      }
    }
  end

  def get_from_mail
    # from_mail = self.from_mail
    # sender_name = self.sender_name
    # return nil if from_mail.blank?
    # if sender_name.blank?
    #   return from_mail
    # else
    #   return "#{NKF.nkf('-jWM', sender_name)} <#{from_mail}>"
    # end
    from_mail
  end


  def ai_platform_school_by_ai_platform_id(ai_platform_id)
    ai_platform_schools.find_or_create_by!(school_id: id, ai_platform_id: ai_platform_id)
  end

  def meeting_minutes
    [meeting_base_minutes, meeting_base_minutes * 2]
  end

  def using_deepseek?
    ai_platform_school&.ai_platform&.provider == "deepseek"
  end

  private

  def remove_blank_ips
    whitelisted_ips.reject!(&:blank?)
  end

  def create_limition
    self.build_limitation unless self.limitation.present?
  end

  def create_school_evaluation
    self.build_school_evaluation unless self.school_evaluation.present?
  end

  def check_master_school
    errors.add :base, "マスタースクールは削除できない"
    throw(:abort)
  end

  def valid_domain
    errors.add(:subdomain, "は不正な値です。") unless self.subdomain.blank? || self.subdomain.match(VALID_SUBDOMAIN_REGEX)
    errors.add(:domain, "は不正な値です。") unless self.domain.blank? || self.domain.match(VALID_DOMAIN_REGEX)
  end

  def valid_whitelisted_ips
    self.whitelisted_ips.each do |whitelisted_ip|
      return errors.add(:whitelisted_ips, "は不正な値です。") unless whitelisted_ip.blank? || whitelisted_ip.match(WHITELISTED_IP)
    end
  end

  def up_role
    self.partner_roles.values.flatten.uniq.each do |user_id|
      user = User.where(id: user_id).first
      user.managers.find_or_create_by(managerable: self, access_all_permission: false) if user.present?
    end
  end

  def down_role
    user_id = self.partner_roles_was.values.flatten.uniq - self.partner_roles.values.flatten.uniq
    user = User.where(id: user_id).first
    user.managers.find_by(managerable: self, access_all_permission: false).destroy if user.present?
  end
end
