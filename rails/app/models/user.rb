require "koala"

class User < ApplicationRecord
  include Importable

  AFFILIATE_CODE_LEN = 10

  paginates_per Settings.paginations.default_perpage_new
  mount_uploader :image, ImageUploader
  # Include default devise modules. Others available are:
  # :lockable, :timeoutable
  devise :database_authenticatable, :registerable,
    :recoverable, :rememberable, :trackable, :validatable, :omniauthable, :confirmable

  has_many :enrollments, dependent: :destroy
  has_many :messages, dependent: :destroy
  has_many :enrollment_lessons, through: :enrollments
  has_many :courses, through: :enrollments
  has_many :course_purchases, dependent: :destroy
  has_many :user_exams, dependent: :destroy
  has_many :exams, through: :user_exams
  has_many :user_goals, dependent: :destroy
  has_many :goals, dependent: :destroy
  has_many :managers, dependent: :destroy
  has_many :chats, dependent: :destroy
  has_many :chat_notifications, dependent: :destroy
  has_many :assessments, dependent: :destroy
  has_many :user_assignments, dependent: :destroy
  has_many :user_projects, dependent: :destroy
  has_many :projects, through: :user_projects
  has_many :skill_scores, as: :resource, dependent: :destroy
  has_many :questions, dependent: :destroy
  has_many :comments, dependent: :destroy
  has_many :subscriptions, dependent: :destroy
  # has_many :impressions, dependent: :destroy
  has_many :exam_purchases, dependent: :destroy
  has_many :user_stock_qs, dependent: :destroy
  has_one :block_email
  has_many :user_block_users
  has_many :block_emails
  has_many :user_blocks, through: :user_block_users
  has_many :votes, dependent: :destroy
  has_many :gradings, class_name: "UserGoal", foreign_key: "grading_user_id"
  has_many :notifications
  has_many :activities
  has_many :visits, class_name: "Ahoy::Visit"
  has_many :events, class_name: "Ahoy::Event"
  has_many :infor_users, dependent: :destroy
  has_many :infors, through: :infor_users
  has_many :tracking_users
  has_many :user_contacts
  has_many :user_catalogs
  has_many :reviews, dependent: :destroy
  has_many :catalogs, through: :user_catalogs
  has_many :school_users, dependent: :destroy
  has_many :schools, through: :school_users
  has_many :access_grants, class_name: "Doorkeeper::AccessGrant",
    foreign_key: :resource_owner_id,
    dependent: :delete_all

  has_many :access_tokens, class_name: "Doorkeeper::AccessToken",
    foreign_key: :resource_owner_id,
    dependent: :delete_all
  has_many :oauth_applications, class_name: "Doorkeeper::Application", as: :owner

  has_many :inquiries
  has_many :user_opened_tracking_mails
  has_many :opened_mails, through: :user_opened_tracking_mails, source: :tracking_mail
  has_many :user_visited_tracking_mails
  has_many :clicked_mails, through: :user_visited_tracking_mails, source: :tracking_mail
  has_many :iams, dependent: :destroy
  has_many :affiliates, dependent: :destroy
  has_many :chat_threads, dependent: :destroy
  has_one :ai_chat_setting, dependent: :destroy
  has_many :payment_histories, class_name: "Meeting::PaymentHistory", foreign_key: :user_id, inverse_of: :user
  has_many :meeting_events, class_name: "Meeting::Event", foreign_key: :user_id, inverse_of: :user
  has_many :meeting_feedbacks, class_name: "Meeting::Feedback", foreign_key: :user_id, inverse_of: :user
  has_many :ticket_usages, class_name: 'Meeting::TicketUsage', foreign_key: :user_id, inverse_of: :user
  has_many :carts
  has_many :orders
  has_many :product_recommends

  validates :name, length: {maximum: Settings.max_length.name}, presence: true
  validates :email, format: {with: URI::MailTo::EMAIL_REGEXP}
  after_create :create_affiliate_code

  scope :filter_by_query, ->(query) {where("name LIKE ? OR email LIKE ?", "%#{query}%", "%#{query}%")}
  scope :filter_with_user_block, ->(query) {
    where("name LIKE ? OR email LIKE ?", "%#{query}%", "%#{query}%").or(
      where(id: UserBlock.where(name: query).includes(:user_block_users).pluck(:user_id)))
  }
  scope :filter_by_scopes, ->(scopes) {where(id: scopes.map(&:user_ids).flatten.uniq)}
  scope :filter_by_scope, ->(scope) {where(id: scope.users)}
  scope :except_by_scopes, ->(scopes) {where.not(id: scopes.map(&:user_ids).flatten.uniq)}
  scope :except_by_scope, ->(scope) {where.not(id: scope.users)}

  def block_email_by_school(school_id)
    self.block_emails.where(school_id: school_id).first
  end

  def is_manager? school
    self.iams
      .where("iams.school_id = #{school.id}")
      .where("iams.resource_type IS NULL")
      .where("iams.resource_id IS NULL")
      .present?
  end

  def is_exam_manager? school
    school_id = school.is_a?(Integer) ? school : school.id
    self.iams
      .where("iams.school_id = #{school_id}")
      .where("iams.resource_type = 'Exam'")
      .where("iams.resource_id is NULL")
      .present?
  end

  def managed_schools
    return School.all if self.is_admin?

    School.joins(:iams)
      .where("iams.user_id = #{self.id}")
      .where("iams.resource_type IS NULL")
      .where("iams.resource_id IS NULL")
      .distinct("schools.id")
  end

  def subscribe chat_room
    self.chat_notifications.find_or_create_by chat_room_id: chat_room.try(:id) || chat_room
  end

  def unsubscribe chat_rooms
    self.chat_notifications.where(chat_room: chat_rooms).destroy_all
  end

  def subscribing? chat_room
    if chat_room.class.name === "Fixnum"
      !self.chat_notifications.where(chat_room_id: chat_room).empty?
    else
      !self.chat_notifications.where(chat_room: chat_room).empty?
    end
  end

  def check_notification
    self.update last_check_notification: Time.now
    NotificationBroadcastJob.perform_later self
  end

  def admin_or_exam_manager? school
    self.is_admin? || is_manager?(school) || is_exam_manager?(school)
  end

  def admin_or_manager? school
    self.is_admin? || is_manager?(school)
  end

  def self.updatable_attributes
    ["name", "email", "password"]
  end

  def name_or_email
    self.name || self.email
  end

  def self.find_or_create_sns_user(auth)
    email = auth.provider == "facebook" ? auth.info.email : User.dummy_email(auth.uid, auth.provider)
    user = User.find_by(email: email)
    unless user
      user = User.new(
        uid: auth.uid,
        provider: auth.provider,
        email: email,
        password: Devise.friendly_token[0, 20],
        name: auth.info.name
      )
      user.save
    end
    user
  end

  def self.find_for_oauth(auth)
    email = auth.provider == "facebook" ? auth.info.email : User.dummy_email(auth.uid, auth.provider)
    user = User.find_by(email: email)
    unless user
      user = User.new(
        uid:      auth.uid,
        provider: auth.provider,
        email:    email,
        password: Devise.friendly_token[0, 20],
        name: auth.info.name
      )
      user.skip_confirmation!
      user.save
    end
    user
  end

  def self.find_for_app_fb_oauth(token)
    fb_client = Koala::Facebook::API.new(token)
    me = fb_client.get_object("me?fields=id,name,email")

    email = me["email"].present? ? me["email"] : User.dummy_email(me["id"], "facebook")
    user = User.find_by(email: email)

    return user if user.present?
    user = User.new({
      :uid => me["id"],
      :provider => "facebook",
      :email => me["email"],
      :password => Devise.friendly_token[0, 20],
      :name => me["name"]
    })
    user.skip_confirmation!
    return user if user.save
    nil
  end

  def self.import file
    imported_users = 0
    self.each_row(file, ->(_){
      raise ImportException.new "何もインポートしませんでした"  if imported_users == 0
    }) do |row, importer|
      importer.require_field(["name", "email", "password"])
      unless importer.first_row?
        importer.require_regex("email", URI::MailTo::EMAIL_REGEXP)
        user = User.where(email: row["email"]).first
        unless user.present?
          user = User.new row.to_hash.slice(*User.updatable_attributes)
          importer.check_valid user
          user.skip_confirmation!
          user.save
          imported_users += 1
        end
      end
    end
  end

  def is_admin?
    self.iams.where(school_id: nil).present?
  end

  def is_manager_or_employer?
    self.iams.present?
  end

  def self.admins
    User.joins(:iams)
      .where("iams.school_id IS NULL")
      .distinct("users.id")
  end

  def is_affiliater?
    return true if self.is_admin?
    self.iams.pluck(:resource_type).include?("Affiliate")
  end

  def create_affiliate_code
    affiliate_code = rand(36**AFFILIATE_CODE_LEN).to_s(36)
    update_attribute(:affiliate_code, "#{self.id}#{affiliate_code}")
  end

  def learning_count (school = nil)
    condition = "user_id = #{self.id}"

    if school.present?
      condition = "#{condition} AND school_id = #{school.id}"
    end

    sql = "
      SELECT SUM(recorded_value) / 3600 as recorded_value
      FROM (
        SELECT user_id, created_at, recorded_value
        FROM tracking_exams
        WHERE #{condition}
        UNION ALL
        SELECT user_id, created_at, recorded_value
        FROM tracking_lessons
        WHERE #{condition}
      ) as t1
    "
    result = ActiveRecord::Base.connection.execute(sql).first.first

    return 0 if result.nil?
    result.round(1)
  end

  def enrollment_lessons_count (school = nil)
    if school.present?
      ids = school.lessons.map{|h| h.id }
      self.enrollment_lessons.where("lesson_id IN (?)", ids).size
    else
      self.enrollment_lessons.size
    end
  end

  def courses_count (school = nil)
    course_lists = self.courses
    if school.present?
      master_milestone_ids = school.master_milestone_ids
      course_lists = course_lists.joins(:master_milestones).where(master_milestones: {id: master_milestone_ids})
    end
    course_lists.where.not(courses: {published_at: nil}).count
  end

  def user_exams_count (school = nil)
    user_exams = actual_user_exams(school)
    user_exams.count
  end

  def user_finished_exams_count (school = nil)
    user_exams = actual_user_exams(school)
    user_exams.finished.count
  end

  def user_passed_exams_count (school = nil)
    user_exams = actual_user_exams(school)
    user_exams.passed.count
  end

  def user_exams_average (school = nil)
    user_exams = actual_user_exams(school)
    return 0.0 if user_exams.count == 0
    sum = 0.0
    user_exams.each { |user_exam|
      p user_exam.average
      sum += user_exam.average if user_exam.finished? }
    (sum / user_exams.count).round(1)
  end

  def actual_user_exams (school = nil)
    user_exams = self.user_exams.actual.finished
    if school.present?
      exams = Exam.where(school_id: school.id)
      user_exams = user_exams.where(exam: exams)
    end
    user_exams
  end

  def user_goals_count (school = nil)
    user_goals = self.user_goals
    if school.present?
      user_goal_ids = school.user_goal_ids
      user_goals = user_goals.where(id: user_goal_ids)
    end
    user_goals.count
  end

  def has_exam_subscription?
    exam_premium_services = SchoolManager.premium_services.where("(premium_services.can_test_all = 1)")
    check_active_premium_service(exam_premium_services)
  end

  def last_subscription_active?
    last_subscription = SchoolManagerUser.subscriptions(self).order("subscriptions.paied_at DESC").first
    last_subscription&.active?
  end

  def check_active_premium_service(premium_services)
    premium_service_ids = case premium_services
                          when PremiumService
                            premium_services.id
                          when PremiumService.const_get(:ActiveRecord_Relation)
                            premium_services.ids
                          else
                            premium_services.map &:id
                          end
    self.subscriptions.where(premium_service_id: premium_service_ids).where(status: [Subscription::STATUS_TRIALING, Subscription::STATUS_DONE]).exists?
  end

  ransacker :signup_range, formatter: proc { |v| v.reverse } do |parent|
    parent.table[:name]
  end

  def clicked_announcement_mails school
    announcement_mails = school.nil? ? AnnouncementMail.all : school.announcement_mails
    tracking_mail_ids = self.clicked_mails&.pluck(:id)
    AnnouncementMail.where(tracking_mail_id: tracking_mail_ids, id: announcement_mails.pluck(:id))
  end

  def opened_announcement_mails school
    announcement_mails = school.nil? ? AnnouncementMail.all : school.announcement_mails
    tracking_mail_ids = self.opened_mails&.pluck(:id)
    AnnouncementMail.where(tracking_mail_id: tracking_mail_ids, id: announcement_mails.pluck(:id))
  end

  # ユーザーは使える講座情報
  def enable_premium_services_course_ids(school)
    course_ids = []
    self.subscriptions.where(status: [Subscription::STATUS_DONE, Subscription::STATUS_TRIALING]).where(school_id: school.id).each do |subscription|
      premium_service = subscription.premium_service
      course_ids = course_ids + premium_service.premium_service_courses.pluck(:course_id)
    end

    course_ids
  end

  def cached
    @cached ||= HashWithIndifferentAccess.new
  end

  private

  def self.dummy_email(uid, provider)
    "#{uid}-#{provider}@example.com"
  end
end
