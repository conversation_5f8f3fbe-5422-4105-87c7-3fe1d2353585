class UserOnboard < ApplicationRecord
  belongs_to :school
  has_many :user_onboard_courses
  has_many :courses, through: :user_onboard_courses, dependent: :destroy

  def step_basic_finished?
    self.title.present?
  end

  def step_questionnaire_finished?
    self.contact_form.present? && self.contact_form.body_html.present?
  end

  def step_course_finished?
    self.user_onboard_courses.length > 0
  end

  def setting_finished?
    self.step_basic_finished? && self.step_questionnaire_finished? && self.step_course_finished?
  end

  def contact_form
    ContactForm.where(target_type: 'UserOnboard', target_id: self.id).first
  end

  def load_user_onboard_data
    {
      welcome_body: ApplicationController.helpers.emojify(UtilityHelper.markdown_to_html(self.welcome_body)).html_safe,
      courses: BaseSerializer.render_list(self.courses.published, CourseSerializer, 'list'),
      body_html: self.contact_form.body_html
    }
  end

  def self.load_user_onboard(user, school)
    return nil if school.user_contacts.where(user_id: user.id).first.present?
    return nil if school.active_user_onboard.blank?

    school.active_user_onboard.load_user_onboard_data
  end

  def self.load_user_onboard_preview(preview_code)
    return nil if preview_code.blank?
    user_onboard = UserOnboard.where(preview_code: preview_code).first
    return nil if user_onboard.blank?

    user_onboard.load_user_onboard_data
  end
end
