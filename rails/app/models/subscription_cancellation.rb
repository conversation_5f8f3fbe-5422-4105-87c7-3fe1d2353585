class SubscriptionCancellation < ApplicationRecord
  belongs_to :subscription
  belongs_to :owner, class_name: 'User', foreign_key: 'owner_id', optional: true

  enum status: { pending: 0, executed: 1 }

  validates :execute_date, presence: true

  def self.create_cancellation(subscription, owner = nil, stop_reason: '', force: true)
    if force
      return if subscription.status == Subscription::STATUS_STOPED

      stripe_subscription = Stripe::Subscription.retrieve(subscription.stripe_subscription_id)
      stripe_subscription.cancel if stripe_subscription.status != 'canceled'

      subscription.status = Subscription::STATUS_STOPED
      subscription.stop_reason = stop_reason
      subscription.stoped_at = Time.now
      subscription.membership_fee_charge_at = nil
      subscription.save!

      SubscriptionCancellation.create!(subscription: subscription,
                                       execute_date: (Time.at(stripe_subscription.current_period_end) + 1.day).to_date,
                                       owner: owner)
    else
      subscription.status = Subscription::STATUS_PENDING
      subscription.stop_reason = stop_reason
      subscription.stoped_at = Time.now
      subscription.membership_fee_charge_at = nil

      SubscriptionCancellation.create!(subscription: subscription,
                                       execute_date: 1.day.from_now.to_date,
                                       owner: owner)

      subscription.save!
    end
  end

  def execute
    return unless subscription # soft delete

    stripe_subscription = Stripe::Subscription.retrieve(subscription.stripe_subscription_id)
    return if %w[active trialing].include? stripe_subscription.status

    transaction do
      subscription.stop_premium_service
      executed!
      subscription.status = stripe_subscription.status == 'canceled' ? Subscription::STATUS_STOPED : Subscription::STATUS_PENDING
      subscription.save!
    end
  end
end
