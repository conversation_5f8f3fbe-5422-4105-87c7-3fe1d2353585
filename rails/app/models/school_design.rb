class SchoolDesign < ApplicationRecord
    include RemoveCache
    belongs_to :school
    has_many :page_templates, dependent: :destroy
    has_many :page_custom_page_templates, dependent: :destroy
    after_commit :remove_all_cache
    mount_uploader :image, ImageUploader
    has_many :revisions, as: :target, dependent: :destroy

    DEFAUL_VALUE = {
      main_page_custom: {},
        exam_list: {},
        exam_show: {},
        course_list: {},
        purchase_cart: {},
        purchase_checkout: {},
        purchase_thank: {},
        course_show: {},
        blog_list: {},
        blog_show: {},
        premium_list: {},
        premium_show: {},
        inquiry: {},
        inquiry_thank: {},
        teacher_show: {},
        teacher_list: {},
        question_list: {},
        question_show: {},
        school_plan_show: {},
        school_plan_list: {},
        review_show: {},
        default_page_template: {"page_description"=>"{{ body }}"},
        default_css: {},
        default_footer: {},
        default_header: {},
        teacher_list: {},
        actived: false,
        preview_code: rand(36**32).to_s(36),
        actived_at: Time.now
    }

    def self.new_default(name, school_id)
        SchoolDesign.create!({
            name: name,
            school_id: school_id,
            main_page_custom: {},
            exam_list: {},
            exam_show: {},
            course_list: {},
            purchase_cart: {},
            purchase_checkout: {},
            purchase_thank: {},
            course_show: {},
            blog_list: {},
            blog_show: {},
            premium_list: {},
            premium_show: {},
            inquiry: {},
            inquiry_thank: {},
            teacher_show: {},
            teacher_list: {},
            question_list: {},
            question_show: {},
            school_plan_show: {},
            school_plan_list: {},
            review_show: {},
            default_page_template: {"page_description"=>"{{ body }}"},
            default_css: {},
            default_footer: {},
            default_header: {},
            teacher_list: {},
            actived: false,
            preview_code: rand(36**32).to_s(36),
            actived_at: Time.now
        })
    end

    def get_page_custom_footer(data = {}, revision_id = nil)
        default_footer = self.default_footer
        default_footer = self.revisions.find(revision_id).body_json if revision_id.present?

        return '' if default_footer.nil? || default_footer.empty? || default_footer["page_description"].nil? || default_footer["page_description"].empty?
        page_data = default_page_data.merge(data)
        result_css = render_page_with_liquid(
            UtilityHelper.get_string_value(default_footer, "page_description_css"),
            page_data
        )
        result_html = render_page_with_liquid(
            UtilityHelper.get_string_value(default_footer, "page_description"),
            page_data
        )

        {
            "css" => render_page_with_image(result_css),
            "html" => render_page_with_image(result_html),
        }
    end

    def get_page_default_header(data = {})
        default_header = self.default_header
        return '' if default_header.nil? || default_header.empty? || default_header["page_description_header"].nil? || default_header["page_description_header"].empty?
        page_data = default_page_data.merge(data)
        result = render_page_with_liquid(
            UtilityHelper.get_string_value(default_header, "page_description_header"),
            page_data
        )

        render_page_with_image(result)
    end

    def get_page_custom_css(page_name, data = {}, revision_id = nil)
        page_data = default_page_data.merge(data)
        page_custom = self.send(page_name)
        page_custom = self.revisions.find(revision_id).body_json if revision_id.present?
        result = render_page_with_liquid(
            UtilityHelper.get_string_value(self.default_css, "page_description_css") +
            UtilityHelper.get_string_value(page_custom, "page_description_css"),
            page_data
        )
        render_page_with_image(result)
    end

    def get_page_custom_header(page_name, data = {}, revision_id = nil)
        page_custom = self.send(page_name)
        page_custom = self.revisions.find(revision_id).body_json if revision_id.present?

        page_default_header = self.get_page_default_header(data)

        return page_default_header if page_custom.nil? || page_custom.empty? || page_custom["page_description_header"].nil? || page_custom["page_description_header"].empty?
        page_data = default_page_data.merge(data)
        result = render_page_with_liquid(UtilityHelper.get_string_value(page_custom, "page_description_header"), page_data)

        page_default_header + render_page_with_image(result)
    end

    def get_page_custom_html(page_name, data, school, user_signed_in, revision_id = nil)
        page_custom = self.send(page_name)
        page_custom = self.revisions.find(revision_id).body_json if revision_id.present?
        return '' if page_custom.nil? || page_custom.empty? || page_custom["page_description"].empty?
        page_data = default_page_data.merge(data)
        result = render_page_with_liquid(
            UtilityHelper.get_string_value(page_custom, "page_description"), page_data
        )
        html_data = render_page_with_image(result)
        school.nil? ? html_data : convert_content(html_data, school, user_signed_in)
    end

    def render_page_with_liquid(liquid_template, data)
        html_data = LiquidRender::Render.render(liquid_template, data)
    end

    def render_page_with_image(html_data)
        return "" if html_data.nil? || html_data.empty?

        # SchoolDesign画像変換
        images = Image.where(imagable: self).order(created_at: :desc)
        images.each do |image|
            html_data = html_data.gsub("\"#{image.relative_path}\"", "\"#{image.s3_url}\"") if image.relative_path
            html_data = html_data.gsub("(#{image.relative_path})", "(#{image.s3_url})") if image.relative_path
        end

        # School画像変換
        images = Image.where(imagable: self.school).order(created_at: :desc)
        images.each do |image|
            html_data = html_data.gsub("\"#{image.relative_path}\"", "\"#{image.s3_url}\"") if image.relative_path
            html_data = html_data.gsub("(#{image.relative_path})", "(#{image.s3_url})") if image.relative_path
        end
        html_data
    end

    def default_page_data
        {
            'school' => LiquidRender::RenderModel.render_data_one(self.school),
            'current_user' => LiquidRender::RenderModel.render_data_one(SchoolManagerUser.current_user),
            'user_signed_in' => SchoolManagerUser.current_user.present?
        }
    end

    private

    def convert_content page_custom_html, school, user_signed_in
        # if school.design_general["use_manual_global_footer"] == "true"
        #   content = ApplicationController.new.render_to_string '/common/_footer.html.erb', layout: false, formats: [:html]
        #   page_custom_html = UtilityHelper.convert_tag_global "[[footer]]", page_custom_html, content
        # end
        # if school.design_general["use_manual_global_header"] == "true"
        #   content = ApplicationController.new.render_to_string '/layouts/_public_header.html.erb', layout: false, formats: [:html], locals: {user_signed_in: user_signed_in}
        #   page_custom_html = UtilityHelper.convert_tag_global "[[header]]", page_custom_html, content
        # end
        page_custom_html
      end
end
