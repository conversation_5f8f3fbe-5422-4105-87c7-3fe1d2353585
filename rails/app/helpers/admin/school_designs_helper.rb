module Admin::SchoolDesignsHelper
  # Helper to render dropdown menu of pages in page_custom_ui
  def render_page_custom_dropdown_menu(school, school_design)
    page_items = [
      { name: :main_page_custom, title: I18n.t("admin.school_designs.pages.main_page_custom"), path: :page_main },
      { name: :exam_list, title: I18n.t("admin.school_designs.pages.exam_list"), path: :page_exam_list },
      { name: :exam_show, title: I18n.t("admin.school_designs.pages.exam_show"), path: :page_exam_show },
      { name: :course_list, title: I18n.t("admin.school_designs.pages.course_list"), path: :page_course_list },
      { name: :purchase_cart, title: I18n.t("admin.school_designs.pages.purchase_cart"), path: :page_course_list },
      { name: :purchase_checkout, title: I18n.t("admin.school_designs.pages.purchase_checkout"), path: :page_course_list },
      { name: :purchase_thank, title: I18n.t("admin.school_designs.pages.purchase_thank"), path: :page_course_list },
      { name: :course_show, title: I18n.t("admin.school_designs.pages.course_show"), path: :page_course_show },
      { name: :lesson_show, title: I18n.t("admin.school_designs.pages.lesson_show"), path: :page_lesson_show },
      { name: :blog_list, title: I18n.t("admin.school_designs.pages.blog_list"), path: :page_blog_list },
      { name: :blog_show, title: I18n.t("admin.school_designs.pages.blog_show"), path: :page_blog_show },
      { name: :premium_list, title: I18n.t("admin.school_designs.pages.premium_list"), path: :page_premium_list },
      { name: :premium_show, title: I18n.t("admin.school_designs.pages.premium_show"), path: :page_premium_show },
      { name: :inquiry, title: I18n.t("admin.school_designs.pages.inquiry"), path: :page_inquiry },
      { name: :inquiry_thank, title: I18n.t("admin.school_designs.pages.inquiry_thank"), path: :page_inquiry_thank },
      { name: :teacher_list, title: I18n.t("admin.school_designs.pages.teacher_list"), path: :page_teacher_list },
      { name: :teacher_show, title: I18n.t("admin.school_designs.pages.teacher_show"), path: :page_teacher_show },
      { name: :review_show, title: I18n.t("admin.school_designs.pages.review_show"), path: :page_review_show },
      { name: :question_list, title: I18n.t("admin.school_designs.pages.question_list"), path: :page_question_list },
      { name: :question_show, title: I18n.t("admin.school_designs.pages.question_show"), path: :page_question_show },
      { name: :custom_page_list, title: I18n.t("admin.school_designs.pages.custom_page_list"), path: :page_custom_page_list },
    ]

    # Add pages that are only displayed for MAIN_DOMAIN_ID
    if school.id == School::MAIN_DOMAIN_ID
      page_items.concat([
                          { name: :school_plan_list, title: I18n.t("admin.school_designs.pages.school_plan_list"), path: :page_school_plan_list },
                          { name: :school_plan_show, title: I18n.t("admin.school_designs.pages.school_plan_show"), path: :page_school_plan_show }
                        ])
    end

    # Add final pages
    page_items.concat([
                        { name: :custom_page_sign_up, title: I18n.t("admin.school_designs.pages.custom_page_sign_up"), path: :page_custom_page_sign_up },
                        { name: :custom_page_sign_in, title: I18n.t("admin.school_designs.pages.custom_page_sign_in"), path: :page_custom_page_sign_in },
                        { name: :custom_page_course_purchases_new, title: I18n.t("admin.school_designs.pages.custom_page_course_purchases_new"), path: :page_custom_page_course_purchases_new },
                        { name: :custom_page_subscriptions_new, title: I18n.t("admin.school_designs.pages.custom_page_subscriptions_new"), path: :page_custom_page_subscriptions_new }
                      ])

    # Add page_templates to the list of pages
    school_design.page_templates.each do |template|
      page_items << {
        name: "template_#{template.id}".to_sym,
        title: "#{I18n.t('admin.school_designs.pages.template')}: #{template.title}",
        path: :page_template,
        template_id: template.id
      }
    end

    # Render items in dropdown
    content_tag(:ul, class: "dropdown-menu w-100") do
      # Render all pages including templates
      items_html = page_items.map do |item|
        is_active = if item[:template_id].present?
          @page_name.to_s == "template_#{item[:template_id]}"
        else
          @page_name == item[:name]
        end

        content_tag(:li, class: is_active ? 'page-custom-design-active' : 'page-custom-design') do
          if item[:template_id].present?
            link_to(item[:title],
                   admin_school_school_design_page_template_path(school, school_design, item[:template_id]),
                   class: "dropdown-item")
          else
            link_to(item[:title],
                   send("#{item[:path]}_admin_school_school_design_path", school, school_design),
                   class: "dropdown-item")
          end
        end
      end

      items_html.join.html_safe
    end
  end

  def preview_url(page_name, school, school_design, revision_id = nil, template_id = nil)
    preview_query = ''
    if school_design.present?
      preview_query = "?preview_code=#{school_design.preview_code}"
      preview_query += "&revision_id=#{revision_id}" if revision_id.present?
    end

    # Handle templates
    if page_name.to_s.start_with?("template_") || template_id.present?
      template_id = template_id || page_name.to_s.gsub("template_", "").to_i
      query = preview_query.blank? ? "?" : "#{preview_query}&"
      return "/previews/show#{query}template_id=#{template_id}&render_page=true"
    end

    case page_name
    when :main_page_custom
      "/#{preview_query}"
    when :exam_list
      "/exams#{preview_query}"
    when :exam_show
      exam = school.exams.distinct.published_exams.published.first
      query = preview_query.blank? ? "?" : "#{preview_query}&"

      return "/previews/show#{query}object_type=exam&no_data=true" if exam.blank?

      "/exams/#{exam.id}#{preview_query}"
    when :course_list
      "/courses#{preview_query}"
    when :purchase_cart
      "/purchase/cart#{preview_query}"
    when :purchase_checkout
      "/purchase/checkout#{preview_query}"
    when :purchase_thank
      "/purchase/payments/thanks_page#{preview_query}"
    when :course_show
      course = school.courses.published.first
      query = preview_query.blank? ? "?" : "#{preview_query}&"

      return "/previews/show#{query}object_type=course&no_data=true" if course.blank?

      "/courses/#{course.id}#{preview_query}"
    when :lesson_show
      lesson = Lesson.joins(:courses).where(courses: { school_id: school.id, publish_global: true }).published.previewable.first
      query = preview_query.blank? ? "?" : "#{preview_query}&"

      return "/previews/show#{query}object_type=lesson&no_data=true" if lesson.blank?

      course = lesson.courses.first
      "/courses/#{course&.id}/lessons/#{lesson&.id}#{preview_query}"
    when :blog_list
      "/blogs#{preview_query}"
    when :blog_show
      blog = school.blogs.published.first
      query = preview_query.blank? ? "?" : "#{preview_query}&"

      return "/previews/show#{query}object_type=blog&no_data=true" if blog.blank?

      "/blogs/#{blog.id}#{preview_query}"
    when :premium_list
      "/premium_services#{preview_query}"
    when :premium_show
      premium_service = PremiumService.published.where("school_id = #{school.id}").first
      query = preview_query.blank? ? "?" : "#{preview_query}&"

      return "/previews/show#{query}object_type=premium_service&no_data=true" if premium_service.blank?

      "/premium_services/#{premium_service.id}#{preview_query}"
    when :inquiry
      "/inquiries/new#{preview_query}"
    when :inquiry_thank
      "/inquiries/thankyou#{preview_query}"
    when :teacher_list
      "/teachers#{preview_query}"
    when :teacher_show
      teacher = school.teachers.first
      query = preview_query.blank? ? "?" : "#{preview_query}&"

      return "/previews/show#{query}object_type=teacher&no_data=true" if teacher.blank?

      "/teachers/#{teacher.id}#{preview_query}"
    when :review_show
      review = school.reviews.first
      query = preview_query.blank? ? "?" : "#{preview_query}&"

      return "/previews/show#{query}object_type=review&no_data=true" if review.blank?

      "/courses/#{review.course_id}/reviews#{preview_query}"
    when :question_list
      "/questions#{preview_query}"
    when :question_show
      question = school.questions.first
      query = preview_query.blank? ? "?" : "#{preview_query}&"

      return "/previews/show#{query}object_type=question&no_data=true" if question.blank?

      "/questions/#{question.id}#{preview_query}"
    when :custom_page_list
      "/pages#{preview_query}"
    when :school_plan_list
      "/school_plans#{preview_query}"
    when :school_plan_show
      school_plan = SchoolPlan.first
      query = preview_query.blank? ? "?" : "#{preview_query}&"

      return "/previews/show#{query}object_type=school_plan&no_data=true" if school_plan.blank?

      "/school_plans/#{school_plan.id}#{preview_query}"
    when :custom_page_sign_up
      query = preview_query.blank? ? "?" : "#{preview_query}&"
      "/previews/show#{query}page_name=custom_page_sign_up&render_page=true"
    when :custom_page_sign_in
      query = preview_query.blank? ? "?" : "#{preview_query}&"
      "/previews/show#{query}page_name=custom_page_sign_in&render_page=true"
    when :custom_page_course_purchases_new
      course = school.courses.published.first
      query = preview_query.blank? ? "?" : "#{preview_query}&"

      return "/previews/show#{query}object_type=course_purchase&no_data=true" if course.blank?

      "/courses/#{course.id}/course_purchases/new#{preview_query}"
    when :custom_page_subscriptions_new
      premium_service = PremiumService.published.where(school_id: school.id).first
      query = preview_query.blank? ? "?" : "#{preview_query}&"

      return "/previews/show#{query}object_type=subscription&no_data=true" if premium_service.blank?

      "/premium_services/#{premium_service.id}/subscriptions/new#{preview_query}"
    else
      preview_query += "&page_name=#{page_name}" if page_name.present?
      "/previews/show#{preview_query}"
    end
  end
end
