module SubscriptionsHelper
  def get_purchase_price(premium_service)
    return 0 if premium_service.price.blank?

    price = (premium_service.price * (100 - premium_service.cut_rate) / 100).round(0)
    number_to_currency(price, precision: 0)
  end

  def get_trial_price(premium_service, value_only = false)
    return 0 if premium_service.trial_price.blank?

    price = (premium_service.trial_price * (100 - premium_service.cut_rate) / 100).round(0)
    return price if value_only
    number_to_currency(price, precision: 0)
  end

  def get_tax(premium_service)
    return 0 if premium_service.price.blank?

    tax = ((premium_service.price * (100 - premium_service.cut_rate) / 100).round(0)) * 0.1
    number_to_currency(tax, precision: 0)
  end

  def get_trial_tax(premium_service)
    return 0 if premium_service.trial_price.blank?

    tax = ((premium_service.trial_price * (100 - premium_service.cut_rate) / 100).round(0)) * 0.1
    number_to_currency(tax, precision: 0)
  end

  def get_price_with_tax(premium_service)
    return 0 if premium_service.price.blank?

    price = (premium_service.price * (100 - premium_service.cut_rate) / 100).round(0)
    tax = price * 0.1
    number_to_currency((price + tax), precision: 0)
  end

  def get_trial_price_with_tax(premium_service, value_only = false)
    return 0 if premium_service.trial_price.blank?

    price = (premium_service.trial_price * (100 - premium_service.cut_rate) / 100).round(0)
    tax = price * 0.1
    return (price + tax).round if value_only
    number_to_currency((price + tax), precision: 0)
  end

  def get_price(premium_service)
    return 0 if premium_service.price.blank?

    price = (premium_service.price * (100 - premium_service.cut_rate) / 100).round(0)
    (price * 1.1).round
  end

  def invoice_status_data
    {
      "draft" => {
        class: "bg-info",
        text: "下書き",
        title: "請求書はまだ使用する準備ができていません。すべての請求書は下書き状態で開始されます。"
      },
      "open" => {
        class: "bg-warning text-dark",
        text: "未支払い",
        title: "請求書は確定済みで支払い待ちです。"
      },
      "paid" => {
        class: "bg-success",
        text: "支払い済み",
        title: "この請求書は支払い済みです。これ以上のアクションは必要ありません。"
      },
      "void" => {
        class: "bg-secondary",
        text: "キャンセル",
        title: "この請求書はキャンセルされました。これ以上のアクションは必要ありません。"
      },
      "uncollectible" => {
        class: "bg-danger",
        text: "回収不能",
        title: "顧客が請求書を支払う可能性が低いです。通常、会計処理で不良債権として扱います。"
      }
    }
  end

  def render_invoice_status(status)
    return unless status.present?

    status_info = invoice_status_data[status]
    return unless status_info.present?

    content_tag :span, class: "badge #{status_info[:class]} text-white", data: { "bs-toggle": "tooltip", "bs-placement": "top" }, title: status_info[:title] do
      safe_join([
        status_info[:text],
        content_tag(:i, nil, class: "fas fa-info-circle ms-1")
      ])
    end
  end

  def render_invoice_amount(invoice)
    amount = invoice["amount_paid"] == 0 && invoice["amount_due"] != 0 ? invoice["amount_due"] : invoice["amount_paid"]
    membership_fee = 0
    if invoice["lines"]["data"].length > 1
      membership_fee = invoice["lines"]["data"].find { |line| line["description"].include?("Membership Fee") }.try(:[], "amount")
    end
    amount -= membership_fee if membership_fee.present?
    number_to_currency(amount)
  end

  def render_invoice_row(invoice)
    return unless invoice.present?

    content_tag :tr do
      safe_join([
        content_tag(:td, "#{render_invoice_amount(invoice)} #{render_refund_value(invoice)}"),
        content_tag(:td) do
          if invoice["created"].present?
            safe_join([
              render_invoice_status(invoice["status"]),
              " ",
              nichi(Time.at(invoice["created"]))
            ])
          end
        end
      ])
    end
  end

  def render_refund_value(invoice)
    return "" if invoice["charge"].blank?

    charge = Stripe::Charge.retrieve(invoice["charge"])
    amount_refunded = charge.amount_refunded
    amount_refunded > 0 ? "（返金済み #{number_with_delimiter(amount_refunded)}円）" : ""
  end

  def render_membership_fee_status(subscription, invoices)
    invoice_membership_fee = invoices.find { |invoice| invoice["lines"]["data"].find { |line| line["amount"] > 0 && line["description"].include?("Membership Fee") } }

    if invoice_membership_fee.present? && invoice_membership_fee["status"] == "paid"
      return {
        class: "bg-success",
        text: "支払い済み",
        title: "入会金の支払いが完了しました。"
      }
    end

    if subscription.membership_fee_paid_at.present?
      {
        class: "bg-success",
        text: "支払い済み",
        title: "入会金の支払いが完了しました。"
      }
    elsif subscription.membership_fee_charge_failed
      {
        class: "bg-danger",
        text: "支払い失敗",
        title: subscription.membership_fee_charge_failed_message
      }
    else
      {
        class: "bg-primary",
        text: "支払い待ち",
        title: "入会金の支払いが必要です。"
      }
    end
  end

  def render_membership_fee_date(subscription, stripe_upcoming_invoice)
    return unless subscription.present? || subscription.need_membership_fee

    if subscription.membership_fee_charge_at.present?
      nichi(subscription.membership_fee_charge_at)
    elsif subscription.membership_fee_paid_at.present?
      nichi(subscription.membership_fee_paid_at)
    elsif subscription.paied_at.present? && stripe_upcoming_invoice.present?
      nichi(Time.at(stripe_upcoming_invoice["created"]))
    else
      nichi(subscription.created_at)
    end
  end

  def should_show_membership_fee?(subscription, stripe_invoices, stripe_upcoming_invoice)
    return false unless subscription&.need_membership_fee?

    status = render_membership_fee_status(subscription, stripe_invoices)
    membership_status = status[:text]

    return false if (subscription.stoped? && membership_status == "支払い待ち") ||
                    (stripe_upcoming_invoice.blank? && membership_status != "支払い済み")

    true
  end
end
