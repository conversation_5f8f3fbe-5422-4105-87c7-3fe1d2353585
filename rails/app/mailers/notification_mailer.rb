class NotificationMailer < ApplicationMailer
  include ActionView::Helpers::<PERSON><PERSON><PERSON><PERSON>
  def inquiry_notification_mail(admin, inquiry)
    @admin = admin
    @inquiry = inquiry
    @school = inquiry.school

    @url = "#{@school.school_domain}/admin/inquiries/#{inquiry[:id]}"
    mail(to: admin[:email], subject: I18n.t("mails.inquiry_notification_mail", id: inquiry[:id]), template_name: "inquiry_notification_mail")
  end
  def inquiry_comment_notification_mail(user, author_name, url, inquiry, comment)
    @author_name = author_name
    @user = user
    @url = url
    @inquiry = inquiry
    @comment = comment
    mail(to: user[:email], subject: I18n.t("mails.inquiry_comment_notification_mail", id: inquiry[:id]), template_name: "inquiry_comment_notification_mail")
  end
  def question_notification_mail(admin, question)
    @admin = admin
    @question = question
    @author_name = question.user.name
    @school = @question.school

    @url = "#{@school.admin_school_domain}/questions/#{question[:id]}"
    mail(to: admin[:email], subject: I18n.t("mails.question_notification_mail", title: question[:title]), template_name: "question_notification_mail")
  end
  def question_comment_notification_mail(user, author_name, url, question, comment)
    @author_name = author_name
    @user = user
    @url = url
    @question = question
    @comment = comment
    mail(to: user[:email], subject: I18n.t("mails.question_comment_notification_mail", question_title: question[:title]), template_name: "question_comment_notification_mail")
  end

  def subscription_notify_user_subscription_pendding_mail(subscription, school)
    @user = subscription.user
    @subscription = subscription
    @premium_service = subscription.premium_service
    @school = school
    @subscription_url = "#{@school.school_domain}/classroom/subscription/#{@subscription.id}"
    @inquiry_url = "#{@school.school_domain}/inquiries/new"
    @type = @subscription.life_time? ? "分割購読" : "定期購読"
    mail(to: @user.email, subject: "【重要】#{@type}のお支払いに失敗しています", template_name: "subscription_notify_user_subscription_pendding_mail")
  end

  def subscription_notify_admin_subscription_pendding_mail(admin, subscription, school)
    @admin = admin
    @subscription = subscription
    @school = school
    @type = @subscription.life_time? ? "分割購読" : "定期購読"
    @url = "#{@school.admin_school_domain}/premium_services/#{@subscription.premium_service_id}/subscriptions/#{@subscription.id}"
    mail(to: @admin.email, subject: "#{@subscription.user.name_or_email}が#{@type}のお支払いに失敗しました", template_name: "subscription_notify_admin_subscription_pendding_mail")
  end

  def subscription_notify_admin_subscription_create_mail(admin, subscription, school)
    @admin = admin
    @subscription = subscription
    @premium_service = subscription.premium_service
    @school = school
    @url = "#{@school.admin_school_domain}/premium_services/#{@subscription.premium_service_id}/subscriptions/#{@subscription.id}"
    mail(to: @admin.email, subject: "#{@subscription.user.name_or_email}さんから「#{@premium_service.name}」のお申し込みがありました", template_name: "subscription_notify_admin_subscription_create_mail")
  end

  def contact_form_notification_mail(user_contact)
    input_mail = user_contact.contact_form.input_kinds.email.first
    return unless input_mail
    email_name = input_mail.name.gsub("\"", "")
    email = user_contact.user_contact_values.where(input_name: email_name).first&.value
    return unless email
    return if email.match(/\A[\w+\-.]+@[a-z\d\-.]+\.[a-z]+\z/i).nil?
    contact_form = user_contact.contact_form
    contact_form_mail = contact_form.contact_form_mail
    return unless contact_form_mail
    from_mail = user_contact.get_from_mail
    values = user_contact.user_contact_values.pluck(:input_name, :value)
    mail_body = contact_form_mail.body
    mail_title = contact_form_mail.title
    values.each do |value| 
      mail_title.gsub!("[#{value[0]}]", value[1])
      mail_body.gsub!("[#{value[0]}]", value[1])
    end
    mail(
      from: from_mail || "Edbase <#{ENV["NOREPLY_MAIL"]}>",
      to: email,
      subject: mail_title,
      body: simple_format(UtilityHelper.markdown_to_html(mail_body))
    )
  end

  def contact_form_to_admin_mail(admin, user_contact)
    contact_form = user_contact.contact_form
    school = user_contact.school
    return unless contact_form
    admin_mail_info = contact_form.contact_form_admin_mail
    if admin_mail_info && admin_mail_info.body
      mail_body = admin_mail_info.body
      mail_title = "#{admin_mail_info.title}-[#{user_contact.id}]"
      values = user_contact.user_contact_values.pluck(:input_name, :value)
      values.each do |value| 
        mail_title.gsub!("[#{value[0]}]", value[1])
        mail_body.gsub!("[#{value[0]}]", value[1])
      end
      from_mail = user_contact.get_admin_from_mail
      mail(
        from: from_mail || "Edbase <#{ENV["NOREPLY_MAIL"]}>",
        to: admin[:email], 
        subject: mail_title,
        body: simple_format(UtilityHelper.markdown_to_html(mail_body))
      )
    else
      @url = "#{school.admin_school_domain}/contact_forms/#{user_contact.id}"

      mail(to: admin[:email], subject: I18n.t("mails.contact_form_notification_mail", id: contact_form.id), template_name: "contact_form_notification_mail")
    end
  end

  def course_purchase_success_mail_worker_to_admin(admin, user, course, enrollment)
    @course = course
    @enrollment = enrollment
    @user = user
    @url = "#{course.school.admin_school_domain}/courses/#{course[:id]}/enrollments/#{enrollment[:id]}"
    mail(to: admin[:email], subject: I18n.t("mails.course_purchase_success_mail_worker_to_admin", user_name: user.name_or_email, course_name: course.name), template_name: "course_purchase_success_mail_worker_to_admin")
  end

  def course_purchase_success_mail_worker_to_user(user, course, enrollment)
    @course = course
    @enrollment = enrollment
    @user = user
    @url = "#{course.school.school_domain}/classroom/courses/#{course[:id]}"
    mail(to: user[:email], subject: I18n.t("mails.course_purchase_success_mail_worker_to_user"), template_name: "course_purchase_success_mail_worker_to_user")
  end

  def subscription_incomplete(subscription)
    @school = subscription.school
    @admins = User.admins
    @user = subscription.user
    @url = "#{@school.school_domain}/classroom/subscription/#{subscription.id}"
    @url = "https://#{@url}" unless @url.include?("http")

    mail(to: @user.email, bcc: @admins.pluck(:email), subject: I18n.t("mails.subscription_incomplete", school_name: @school.name), template_name: "subscription_incomplete")
  end
end
