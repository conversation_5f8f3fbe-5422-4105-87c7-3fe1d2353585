  class MeetingMailer < ApplicationMailer
  # iPhoneメールアプリ対応のデフォルト設定
  default content_type: "multipart/alternative"

  def reminder_to_student(event)
    setup_event(event)
    mail(
      to: @student.email,
      subject: "【まもなく開始】#{@teacher.name}先生との面談は30分後に開始します"
    ) do |format|
      format.html { render }
      format.text { render plain: generate_text_version(:reminder_to_student) }
    end
  end

  def reminder_to_teacher(event)
    setup_event(event)
    mail(
      to: @teacher.user.email,
      subject: "【まもなく開始】#{@student.name}さんとの面談は30分後に開始します"
    ) do |format|
      format.html { render }
      format.text { render plain: generate_text_version(:reminder_to_teacher) }
    end
  end

  def new_booking_notification_to_teacher(event)
    setup_event(event)
    mail(
      to: @teacher.user.email,
      subject: "【新規予約通知】#{@student.name}さんから面談の予約がありました（#{formatted_date} #{formatted_time}）"
    ) do |format|
      format.html { render }
      format.text { render plain: generate_text_version(:new_booking_notification_to_teacher) }
    end
  end

  def cancellation_notification_to_teacher(event)
    setup_event(event)
    mail(
      to: @teacher.user.email,
      subject: "【予約キャンセル】#{@student.name}さんが面談をキャンセルしました（#{formatted_date} #{formatted_time}）"
    )
  end

  def thank_you_to_student(event)
    setup_event(event)
    mail(
      to: @student.email,
      subject: "【面談お礼】#{@teacher.name}先生との面談へのご参加ありがとうございました"
    )
  end

  def booking_rejected_notification_to_student(event)
    setup_event(event)
    mail(
      to: @student.email,
      subject: "【予約不可】#{@teacher.name}先生との面談予約（#{formatted_date} #{formatted_time}）について"
    )
  end

  def booking_approved_notification_to_student(event)
    setup_event(event)
    mail(
      to: @student.email,
      subject: "【予約承認】#{@teacher.name}先生との面談予約が承認されました（#{formatted_date} #{formatted_time}）"
    )
  end

  def reminder_to_student_1_day_before(event)
    setup_event(event)
    mail(
      to: @student.email,
      subject: "【明日の面談】#{@teacher.name}先生との面談は明日 #{@start_time.strftime('%H:%M')} です"
    )
  end

  def new_message_notification_to_student(message)
    @message = message
    @event = message.event
    @student = @event.user
    @teacher = @event.teacher
    @meeting_details_url = "https://#{@teacher.school.school_domain}/classroom/appointment#/events/#{@event.id}"

    mail(
      to: @student.email,
      subject: "【新規メッセージ】#{@teacher.name}先生からメッセージが届きました"
    )
  end

  def new_message_notification_to_teacher(message)
    @message = message
    @event = message.event
    @student = @event.user
    @teacher = @event.teacher
    @meeting_details_url = "https://#{@teacher.school.school_domain}/admin/schools/#{@teacher.school.id}/teachers/#{ @teacher.id }/meeting/events/#{@event.id}"

    mail(
      to: @teacher.user.email,
      subject: "【新規メッセージ】#{@student.name}さんからメッセージが届きました"
    )
  end

  def daily_schedule_to_teacher(teacher, events)
    @teacher = teacher
    @events = events.sort_by(&:start_at)
    @count = events.count
    @teacher_events =  "https://#{@teacher.school.school_domain}/admin/schools/#{@teacher.school.id}/teachers/#{ @teacher.id }/meeting/events"
    mail(
      to: @teacher.user.email,
      subject: "【#{teacher.name}】本日のミーティングスケジュール (#{@count}件)"
    )
  end

  def daily_schedule_to_student(student, events)
    @student = student
    @events = events.sort_by(&:start_at)
    @count = events.count
    @classroom_events = "https://#{@student.school.school_domain}/classroom/appointment#/events"
    mail(
      to: @student.email,
      subject: "【#{student.name}】本日のミーティングスケジュール (#{@count}件)"
    )
  end

  def zoom_ready_notification_to_student(event)
    setup_event(event)
    @meeting_details_url = "https://#{event.teacher.school.school_domain}/classroom/appointment#/events/#{@event.id}"

    mail(
      to: @student.email,
      subject: "【面談準備完了】#{@teacher.name}先生が面談室に入室しました"
    )
  end

  private

  def setup_event(event)
    @event = event
    @student = event.user
    @teacher = event.teacher
    @start_time = event.start_at
    @end_time = event.end_at
    @join_url = event.meet_data&.dig("join_url")

    # HTTPS を明示的に指定し、iPhoneメールアプリでの変換を防ぐ
    base_url = "https://#{@teacher.school.school_domain}"

    # URLヘルパーを使用してより確実なURL生成
    @meeting_detail_for_teacher = "#{base_url}/admin/schools/#{@teacher.school.id}/teachers/#{@teacher.id}/meeting/events/#{event.id}"
    @meeting_details_for_student = "#{base_url}/classroom/appointment#/events/#{event.id}"

    # URLが正しくHTTPSで始まることを確認
    @meeting_detail_for_teacher = @meeting_detail_for_teacher.gsub(/^http:/, 'https:')
    @meeting_details_for_student = @meeting_details_for_student.gsub(/^http:/, 'https:')
  end

  def formatted_date
    @start_time.strftime('%Y年%m月%d日')
  end

  def formatted_time
    @start_time.strftime('%H:%M')
  end

  # iPhoneメールアプリ向けテキスト版生成
  def generate_text_version(template_name)
    case template_name
    when :new_booking_notification_to_teacher
      <<~TEXT
        #{@teacher.name} 様

        新しい面談予約が入りましたのでお知らせします。

        生徒: #{@student.name}
        面談日時: #{l @start_time, format: :simple} - #{l @end_time, format: :simple}
        面談内容: #{@event&.meeting_category&.name}
        #{@event.content_detail.present? ? "詳細: #{@event.content_detail}" : ""}
        ステータス: 承認待ち

        この面談予約はまだ承認されていません。面談を実施する場合は、予約を承認してください。

        予約を承認または拒否するには、下記のリンクから管理画面へアクセスしてください。
        #{@meeting_detail_for_teacher}

        ※このメールに心当たりのない場合は、お手数ですが管理者までご連絡ください。
      TEXT
    when :reminder_to_student
      <<~TEXT
        #{@student.name} 様

        30分後に#{@teacher.name}先生との面談が開始されます。

        面談詳細:
        - 日時: #{l @start_time, format: :simple} 〜 #{l @end_time, format: :simple}
        - 内容: #{@event&.meeting_category&.name}
        #{@event.content_detail.present? ? "- 詳細内容: #{@event.content_detail}" : ""}

        #{@join_url.present? ? "面談に参加する: #{@join_url}" : ""}

        ご質問等ございましたら、こちらのメールにご返信ください。

        よろしくお願いいたします。
      TEXT
    when :reminder_to_teacher
      <<~TEXT
        #{@teacher.name} 先生

        30分後に#{@student.name}さんとの面談が開始されます。

        面談詳細:
        - 日時: #{l @start_time, format: :simple} 〜 #{l @end_time, format: :simple}
        - 内容: #{@event&.meeting_category&.name}
        #{@event.content_detail.present? ? "- 詳細内容: #{@event.content_detail}" : ""}

        #{@join_url.present? ? "面談に参加する: #{@join_url}" : ""}

        ご質問等ございましたら、こちらのメールにご返信ください。

        よろしくお願いいたします。
      TEXT
    else
      "テキスト版メールが生成されました。"
    end
  end
end
