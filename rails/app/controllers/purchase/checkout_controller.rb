class Purchase::CheckoutController < ApplicationController
  layout "home_subschool"
  before_action :authenticate_user!
  before_action :fetch_products, only: [:show]

  def show
    liquid_data = get_liquid_data(@products).merge({
      "settings" => default_settings
    })

    @page_custom_css = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_css(:purchase_checkout, liquid_data, params[:revision_id])
    @page_custom_header = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_header(:purchase_checkout, liquid_data, params[:revision_id])
    @page_custom_footer = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_footer(liquid_data, params[:revision_id])
    @page_custom_html = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_html(:purchase_checkout, liquid_data, @school, user_signed_in?, params[:revision_id])
  end

  private

  def fetch_products
    @products = @cart.cart_items.includes(:product).map(&:product)
  end

  def get_liquid_data(products)
    {
      "products" => LiquidRender::RenderModel.render_data_list(products).each do |product|
        productable = find_product(product["productable_id"], product["productable_type"])
        product["productable"] = LiquidRender::RenderModel.render_data_one(productable).merge({
          "product_type" => productable.class.name,
          "price" => ActionController::Base.helpers.number_with_delimiter(productable["price"])
        }) if productable
      end,
      "cart" => LiquidRender::RenderModel.render_data_one(@cart)
    }
  end

  def find_product(product_id, product_type)
    product_class = product_type.constantize rescue nil
    return nil unless product_class

    product_class.find_by(id: product_id)
  end

  def default_settings
    {
      "customer_name" => "customerName",
      "agreement_check" => "agreementCheck",
      "agreement_checkbox" => "agreement-checkbox",
      "checkout_button" => "checkoutButton",
      "course_image" => "course-image",
      "course_title" => "course-title",
      "original_price" => "original-price",
      "discount_percent" => "discount-percent",
      "discount_amount" => "discount-amount",
      "summary_item_count" => "summaryItemCount product-count",
      "total_price" => "total-price",
      "checkout_btn" => "checkout-btn",
      "stripe_modal" => "stripeModal",
      "stripe_modal_label" => "stripeModalLabel",
      "stripe_payment_form" => "stripe-payment-form",
      "purchase_amount" => "purchase-amount",
      "stripe_card_element" => "stripe-card-element",
      "stripe_card_errors" => "stripe-card-errors",
      "payment_total_display" => "payment-total-display",
      "stripe_submit" => "stripe-submit",
      "loading_modal" => "loadingModal",
    }
  end
end
