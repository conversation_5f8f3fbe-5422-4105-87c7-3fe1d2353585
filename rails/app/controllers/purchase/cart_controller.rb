class Purchase::CartController < ApplicationController
  before_action :set_school
  layout "home_subschool"

  def show
    @products = @cart.cart_items.includes(:product).map(&:product)

    liquid_data = get_liquid_data.merge({
      "class" => default_class_for_cart
    })

    @page_custom_css = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_css(:purchase_cart, liquid_data, params[:revision_id])
    @page_custom_header = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_header(:purchase_cart, liquid_data, params[:revision_id])
    @page_custom_footer = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_footer(liquid_data, params[:revision_id])
    @page_custom_html = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_html(:purchase_cart, liquid_data, @school, user_signed_in?, params[:revision_id])
  end

  def add_item
    Purchase::CartService.new(@cart)
      .add_item(productable_type: params[:product_type], productable_id: params[:product_id])


    respond_to do |format|
      format.json { render json: { success: true, current_cart: @cart } }
    end
  end

  def remove_item
    product_id = params[:product_id]
    product_type = params[:product_type]

    result = Purchase::CartService.new(@cart)
                      .remove_item(product_id: product_id, product_type: product_type)

    coupon_status = nil
    updated_discount = nil

    if session[:cart_coupon].present?
      coupon = Coupon.find_by(id: session[:cart_coupon]["coupon_id"])

      if coupon
        remaining_products = @cart.cart_items.map(&:product)

        if remaining_products.empty?
          session.delete(:cart_coupon)
          coupon_status = 'removed'
        else
          applicable_products = []
          total_discount_amount = 0

          remaining_products.each do |product|
            next unless coupon_applies_to_product?(coupon, product)

            applicable_products << {
              product_id: product.productable.id.to_s,
              product_type: product.productable.class.name,
              original_price: product.price
            }
          end

          if applicable_products.empty?
            session.delete(:cart_coupon)
            coupon_status = 'removed'
          else
            # Second pass to calculate actual discounts
            applicable_products = applicable_products.map do |product_data|
              original_price = product_data[:original_price]
              discount_amount = coupon.cal_discount_price(original_price)
              total_discount_amount += discount_amount

              product_data.merge(
                discount_amount: discount_amount,
                discount_percent: coupon.rate? ? coupon.discount_rate : (discount_amount.to_f / original_price * 100).round,
                discounted_price: original_price - discount_amount
              )
            end

            # Update session with recalculated values
            session[:cart_coupon][:applicable_products] = applicable_products

            # Calculate discount percentage of total cart
            cart_subtotal = @cart.cart_items.sum { |item| item.price_at_time * item.quantity }
            discount_percent = cart_subtotal > 0 ? ((total_discount_amount.to_f / cart_subtotal) * 100).round : 0

            updated_discount = {
              amount: total_discount_amount,
              percent: discount_percent
            }
          end
        end
      end
    end

    respond_to do |format|
      format.html {
        flash[:notice] = "商品がカートから削除されました"
        redirect_to purchase_cart_path
      }
      format.json {
        render json: {
          success: result.present?,
          current_cart: @cart,
          coupon_status: coupon_status,
          updated_discount: updated_discount
        }
      }
    end
  end

  def apply_coupon
    coupon_code = params[:coupon_code]
    products = params[:product_ids] || []

    begin
      coupon = Coupon.find_by(code: coupon_code)

      unless coupon
        return render json: { success: false, message: '無効なクーポンコードです' }
      end

      # Validate coupon
      unless valid_coupon?(coupon)
        return render json: { success: false, message: 'クーポンは有効期限が切れている、または使用上限に達しています' }
      end

      applicable_products = []

      # If products are passed, check each one
      if products.any?
        total_price = 0
        product_number = 0

        # Second pass: calculate discount for each eligible product
        products.each do |product_data|
          product_id = product_data[:id]
          product_type = product_data[:type]

          # Find the product
          product = find_product(product_id, product_type)
          next unless product

          next unless coupon_applies_to_product?(coupon, product)
          next if product.price.zero?

          original_price = product.price
          discount_amount = coupon.cal_discount_price(original_price)

          applicable_products << {
            product_id: product_id.to_s,
            product_type: product_type,
            original_price: original_price,
            discount_amount: discount_amount,
            discount_percent: coupon.discount_rate,
            discounted_price: original_price - discount_amount,
            coupon_id: coupon.id
          }
        end
      end

      if applicable_products.empty?
        return render json: { success: false, message: 'このクーポンはカート内の商品に適用できません' }
      end

      session[:cart_coupon] = {
        code: coupon_code,
        coupon_id: coupon.id,
        applicable_products: applicable_products
      }

      render json: {
        success: true,
        coupon_code: coupon_code,
        description: coupon.name,
        applicable_products: applicable_products
      }
    rescue => e
      Rails.logger.error("Apply coupon error: #{e.message}")
      render json: { success: false, message: 'クーポンの適用中にエラーが発生しました' }, status: :unprocessable_entity
    end
  end

  def remove_coupon
    session.delete(:cart_coupon)

    render json: { success: true }
  end

  private

  def valid_coupon?(coupon)
    current_time = Date.today

    if coupon.start_at.present? && current_time < coupon.start_at
      return false
    end

    if coupon.end_at.present? && current_time > coupon.end_at
      return false
    end

    if coupon.limit.present? && UserCoupon.where(coupon_id: coupon.id).count >= coupon.limit
      return false
    end

    if current_user && coupon.use_limit_per_user.present?
      user_usage_count = UserCoupon.where(coupon_id: coupon.id, user_id: current_user.id).count
      if user_usage_count >= coupon.use_limit_per_user
        return false
      end
    end

    true
  end

  def coupon_applies_to_product?(coupon, product)
    if coupon.respond_to?(:courses) && product.is_a?(Course)
      return coupon.courses.blank? || coupon.courses.include?(product)
    end
  end

  def find_product(product_id, product_type)
    product_class = product_type.constantize rescue nil
    return nil unless product_class

    product_class.find_by(id: product_id)
  end

  def get_liquid_data
    {
      "products" => LiquidRender::RenderModel.render_data_list(@products).each do |product|
        productable = find_product(product["productable_id"], product["productable_type"])
        product["productable"] = LiquidRender::RenderModel.render_data_one(productable).merge({
          "product_type" => productable.class.name,
          "price" => ActionController::Base.helpers.number_with_delimiter(productable["price"]),
          "teachers_name" => productable&.teachers&.map { |teacher| "#{teacher.name}" }&.join(', '),
          "teachers_catch" => productable&.teachers&.map { |teacher| "#{teacher.catch}" }&.join(', ')
        }) if productable
      end,
      "cart" => LiquidRender::RenderModel.render_data_one(@cart)
    }
  end

  def default_class_for_cart
    {
      "cart_item" => "cart-item",
      "card_img" => "card-img-top",
      "course_title" => "course-title",
      "original_price" => "original-price",
      "discounted_price" => "discounted-price",
      "remove_item_button" => "remove-btn",
      "discount_badge" => "discount-badge",
      "checkout_button" => "checkout-btn",
      "coupon_input" => "coupon-code-input",
      "apply_coupon_button" => "apply-coupon-btn",
      "coupon_success_alert" => "coupon-success-alert",
      "coupon_code_display" => "coupon-code-display",
      "coupon_description" => "coupon-description",
      "remove_coupon_btn" => "remove-coupon-btn",
      "coupon_error_alert" => "coupon-error-alert",
      "coupon_error_message" => "coupon-error-message",
    }
  end
end
