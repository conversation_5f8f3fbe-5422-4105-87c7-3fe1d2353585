class Purchase::Orders<PERSON>ontroller < ApplicationController
  before_action :set_school

  layout "home_subschool"

  before_action :authenticate_user!

  def create
    ActiveRecord::Base.transaction do
      if params[:course_type] == "free" || params[:amount]&.zero?
        products = fetch_products

        # sync price case free course admin update item exist in cart
        if params[:course_type] == "free"
          products.each do |product|
            product.update(price: product.productable.try(:price))
          end
        end

        order = create_order(products, nil, (params[:amount]&.zero? ? params[:coupon_code] : nil))

        # Clear the cart item after successful payment
        products.each do |product|
          @cart.cart_items.find_by(product_id: product.id)&.destroy
        end

        if params[:course_type] == "free"
          redirect_to thanks_page_purchase_payments_path(order_id: order.id)
        else
          render json: {
            success: true,
            order_id: order.id,
            message: 'Payment succeeded',
          }
        end
      else
        payment_intent = retrieve_payment_intent(params[:payment_intent])

        if payment_intent_succeeded?(payment_intent)
          products = fetch_products
          order = create_order(products, payment_intent, params[:coupon_code])

          # Clear the cart item after successful payment
          products.each do |product|
            @cart.cart_items.find_by(product_id: product.id)&.destroy
          end

          render_success_response(order)
        else
          raise ActiveRecord::Rollback, 'Payment failed'
        end
      end
    end
  rescue => e
    render_error_response(e)
  end

  private

  def retrieve_payment_intent(payment_intent_id)
    Stripe::PaymentIntent.retrieve(payment_intent_id)
  end

  def payment_intent_succeeded?(payment_intent)
    payment_intent.status == 'succeeded'
  end

  def fetch_products
    @cart.cart_items.includes(:product).map(&:product)
  end

  def calculate_total_discounted_price(products, coupon)
    total_original_price = products.sum { |product| product.price }

    return total_original_price unless coupon

    # Calculate discount for each product and sum them
    total_discount = products.sum do |product|
      product.productable.coupon_id && product.productable.coupon_id == coupon&.id ? coupon&.cal_discount_price(product.price) : 0
    end

    # Apply total discount to the original price
    [total_original_price - total_discount, 0].max
  end

  def create_order(products, payment_intent, coupon_code)
    coupon = @school.coupons&.find_by(code: coupon_code)
    total_amount_value = calculate_total_discounted_price(products, coupon)
    # add tax
    total_amount_value = (total_amount_value * (1 + Settings.tax)).round
    order = Order.create!(
      user_id: current_user.id,
      total_amount: total_amount_value,
      status: 'payment_success',
      school_id: @school.id
    )

    create_order_items(order, products, coupon)
    create_payment(order, total_amount_value, payment_intent)

    if coupon.present?
      UserCoupon.create!(
        school_id: @school.id,
        user_id: current_user.id,
        coupon_id: coupon&.id,
        target_type: 'Order',
        target_id: order.id,
        discount_before_price: (products.sum { |product| product.price }),
        discount_after_price: order.pre_tax_amount,
      )
    end

    order
  end

  def create_order_items(order, products, coupon)
    pairs = []

    products.each do |product|
      order_item = order.order_items.create!(
        product_id: product.id,
        quantity: 1,
        price_at_time: product.price,
        coupon_id: (product.productable.coupon_id == coupon&.id ? coupon&.id : nil),
      )
      Enrollment.create_by_user_and_course(current_user, order_item.product.productable, custom_enrollment_type: 4)
      course_purchase = CoursePurchase.new(
        user: current_user,
        course: product.productable,
        price: product.price,
        paied_price: product.price,
        school: product.productable.school,
        installment_count_total: 1,
        installment_count_current: 0,
        status: CoursePurchase::STATUS_DONE,
        order_item_id: order_item.id,
        start_at: Time.now
      )
      course_purchase.save

      course_purchase_invoice = CoursePurchaseInvoice.new(
        user: course_purchase.user,
        school_id: course_purchase.school_id,
        course_purchase_id: course_purchase.id,
        course_id: product.productable.id,
      )

      course_purchase_invoice.amount_paid = product.price
      course_purchase_invoice.paied_at = course_purchase.start_at
      course_purchase_invoice.skip_sale_management = true
      course_purchase_invoice.save(validate: false)

      sale_management = create_sale_management(course_purchase_invoice)

      pairs << {
        course_purchase: course_purchase,
        order_item: order_item,
        course_purchase_invoice: course_purchase_invoice,
        sale_management: sale_management
      }
    end

    update_course_purchase_paied_prices_with_pairs(order, pairs)
  end

  def create_sale_management(course_purchase_invoice)
    sale_management = SaleManagement.create!(
      price: course_purchase_invoice.amount_paid,
      school_id: course_purchase_invoice.school.id,
      user_id: course_purchase_invoice.user.id,
      name: course_purchase_invoice.course.name,
      purchase_type: "買取",
      kind: Course.name,
      course_id: course_purchase_invoice.course.id,
      username: course_purchase_invoice.user.name,
      commision_rate: course_purchase_invoice.school.actived_school_plan_subscription.blank? ? 100 : course_purchase_invoice.school.actived_school_plan_subscription.school_plan.commision_rate,
      application_fee_amount: course_purchase_invoice.school.actived_school_plan_subscription_fee(course_purchase_invoice.amount_paid)
    )

    sale_management
  end

  def create_payment(order, total_amount_value, payment_intent)
    Payment.create!(
      order_id: order.id,
      amount: total_amount_value,
      stripe_product_id: "",
      status: 'succeeded',
      stripe_customer_id: payment_intent&.customer,
      stripe_payment_intent_id: payment_intent&.id,
    )
  end

  def update_course_purchase_paied_prices_with_pairs(order, pairs)
    ActiveRecord::Base.transaction do
      pairs.each do |pair|
        course_purchase = pair[:course_purchase]
        order_item = pair[:order_item]
        course_purchase_invoice = pair[:course_purchase_invoice]
        sale_management = pair[:sale_management]

        paid_price = order.actual_paid_amount(order_item)

        # Update the course purchase and invoice with the actual paid price
        order_item.update!(
          tax_price: order.tax_amount(order_item),
          discount_price: order.discount_amount(order_item)
        )
        course_purchase.update!(paied_price: paid_price)
        course_purchase_invoice.update!(amount_paid: paid_price)
        sale_management.update!(
          price: paid_price,
          application_fee_amount: course_purchase_invoice.school.actived_school_plan_subscription_fee(paid_price)
        )
      end
    end
  end

  def render_success_response(order)
    render json: {
      success: true,
      order_id: order.id,
      message: 'Payment succeeded',
    }
  end

  def render_error_response(error)
    render json: { error: error.message }, status: :unprocessable_entity
  end
end
