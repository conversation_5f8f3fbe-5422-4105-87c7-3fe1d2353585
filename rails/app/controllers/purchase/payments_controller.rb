class Purchase::PaymentsController < ApplicationController
  before_action :authenticate_user!
  layout "home_subschool"
  skip_before_action :verify_authenticity_token, only: [:create]

  def create
    begin
      amount = params[:amount].to_i
      payment_method_id = params[:payment_method_id]
      customer_name = params[:customer_name]
      cart_items = params[:cart_items] || []

      if amount > 0
        # Create or retrieve Stripe customer
        customer = create_or_retrieve_stripe_customer(payment_method_id, customer_name)

        # Create payment intent with automatic confirmation method
        payment_intent = Stripe::PaymentIntent.create({
          amount: amount,
          currency: 'jpy',
          customer: customer.id,
          payment_method: payment_method_id,
          confirmation_method: 'automatic', # Change from 'manual' to 'automatic'
          confirm: false, # Set to false to allow confirmation on client side
          description: generate_purchase_description(cart_items),
          metadata: {
            user_id: current_user&.id || 'guest',
            user_email: current_user&.email || '',
            cart_items_count: cart_items.length
          }
        })

        render json: {
          requires_action: true,
          client_secret: payment_intent.client_secret,
          payment_intent: payment_intent.id,
          amount: amount
        }
      else
        render json: {
          amount: amount
        }, status: :ok
      end

    rescue Stripe::CardError => e
      render json: { error: e.message }, status: :unprocessable_entity
    rescue => e
      Rails.logger.error "Payment error: #{e.message}"
      render json: { error: 'サーバーエラーが発生しました。もう一度お試しください。' }, status: :internal_server_error
    end
  end

  def thanks_page
    @order = current_user.orders.find_by(id: params[:order_id], school_id: SchoolManager.main_school.id)

    return not_found unless @order

    @order_items = @order&.order_items

    order_payment = current_user.orders.where(status: 'payment_success')
    @product_payment = order_payment.flat_map(&:order_items)
    @original_price = calculate_original_price(@order_items)

    liquid_data = get_liquid_data.merge({
      "settings" => default_settings
    })

    @page_custom_css = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_css(:purchase_thank, liquid_data, params[:revision_id])
    @page_custom_header = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_header(:purchase_thank, liquid_data, params[:revision_id])
    @page_custom_footer = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_footer(liquid_data, params[:revision_id])
    @page_custom_html = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_html(:purchase_thank, liquid_data, @school, user_signed_in?, params[:revision_id])
  end

  private

  def create_or_retrieve_stripe_customer(payment_method_id, customer_name)
    # Create a new Stripe customer
    customer = Stripe::Customer.create(
      email: current_user.email,
      name: customer_name || current_user.full_name,
      payment_method: payment_method_id,
      invoice_settings: {
        default_payment_method: payment_method_id
      },
      metadata: {
        user_id: current_user.id
      }
    )

    return customer
  end

  def generate_purchase_description(cart_items)
    if cart_items.empty?
      return "Purchase on Edbase"
    elsif cart_items.length == 1
      return "Purchase: #{cart_items[0][:name]}"
    else
      first_item = cart_items[0][:name]
      return "Purchase: #{first_item} and #{cart_items.length - 1} other courses"
    end
  end

  def get_liquid_data
    {
      "order_items" => render_order_items(@order_items),
      "cart" => LiquidRender::RenderModel.render_data_one(@cart),
      "order" => render_order_data(@order),
      "discount_price" => calculate_discount_price,
      "tax_price" => "¥#{ActionController::Base.helpers.number_with_delimiter(@order.total_amount - @order.pre_tax_amount)}",
      "original_price" => ActionController::Base.helpers.number_with_delimiter(@original_price),
      "product_payment" => render_order_items(@product_payment)
    }
  end

  def render_order_items(order_items)
    LiquidRender::RenderModel.render_data_list(order_items).each do |order_item|
      product = Product.find_by(id: order_item["product_id"])
      productable = find_productable(product.productable_id, product.productable_type)

      if productable
        order_item["productable"] = LiquidRender::RenderModel.render_data_one(productable).merge({
          "product_type" => productable.class.name,
          "price" => ActionController::Base.helpers.number_with_delimiter(productable["price"])
        })
      end
    end
  end

  def render_order_data(order)
    return unless order

    LiquidRender::RenderModel.render_data_one(order).merge({
      "total_amount" => ActionController::Base.helpers.number_with_delimiter(order["total_amount"])
    })
  end

  def calculate_discount_price
    return unless @order

    original_price = @order&.order_items&.includes(:product)&.map(&:product)&.sum(&:price)

    discount_val = original_price.to_i - @order.pre_tax_amount.to_i
    return "" if discount_val <= 0

    "-¥#{ActionController::Base.helpers.number_with_delimiter(discount_val)}"
  end

  def find_productable(product_id, product_type)
    product_class = product_type.constantize rescue nil
    return nil unless product_class

    product_class.find_by(id: product_id)
  end

  def default_settings
    {
      "order_details" => "order-details",
    }
  end

  def calculate_original_price(order_items)
    return 0 unless order_items.present?

    total = 0
    order_items.each do |order_item|
      product = Product.find_by(id: order_item.product_id)
      if product
        productable = find_productable(product.productable_id, product.productable_type)
        total += productable.price if productable
      end
    end

    total
  end
end
