class Api::V2::ListsController < Api::V2::ApplicationController
  before_action :authenticate_user!

  def index
    lists = List.where(school_id: SchoolManager.main_school).roots
    json_response(BaseSerializer.render_list(lists, ListCompactSerializer, request_mode))
  end

  def my
    lists = List.where(school_id: SchoolManager.main_school).roots
    lists_score = {}
    lists.each do |l|
      subtree = List.sort_by_ancestry(l.subtree).reverse
      subtree.each do |st|
        if st.has_children?
          sum_score = 0
          not_exam_list = 0
          st.children.each do |ci|
            if ci.get_exams(current_user.id).count == 0
              not_exam_list += 1
            end
            sum_score += lists_score[ci.id]
          end
          if st.child_ids.size > not_exam_list
            lists_score[st.id] = sum_score/(st.child_ids.size - not_exam_list)
          else
            lists_score[st.id] = sum_score
          end
        else
          lists_score[st.id] = st.get_score(current_user.id)
        end
      end
    end
    data = BaseSerializer.render_list(lists, ListCompactSerializer, BaseSerializer::MODE_MY_EXAM, {lists_score: lists_score})
    json_response(data)
  end
end
