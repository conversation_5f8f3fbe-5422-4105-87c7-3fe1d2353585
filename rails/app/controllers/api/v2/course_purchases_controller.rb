class Api::V2::CoursePurchasesController < Api::V2::ApplicationController
  before_action :authenticate_user!
  before_action :get_course, only: [:create, :show_by_course, :get_coupon]

  before_action :set_course_purchase, only: [:show, :update]

  def show
    json_response({
      course_purchase: CoursePurchaseSerializer.new(@course_purchase).render,
      course_purchase_invoices: CoursePurchaseInvoice.where(course_purchase_id: @course_purchase.id).order("paied_at DESC"),
    })
  end

  def update
    @course_purchase.change_token(
      payment_params[:email],
      payment_params[:token]
    )

    json_response(CoursePurchaseSerializer.new(@course_purchase).render)
  end

  def create
    discount_price = 0
    has_discount = false

    course_price = @course.get_purchase_price
    if @course.coupon && @course.product? && params["coupon_code"].present?
      coupon = @course.get_coupon(@current_user, params["coupon_code"])
      return unless coupon

      if coupon.cash?
        discount_price = coupon.discount_price
      elsif coupon.rate?
        discount_price = course_price * coupon.discount_rate/100
      end
      has_discount = true
      course_price = course_price - discount_price.round(0) if course_price > discount_price
    end

    installment_count_total = params[:installment_count].present? ? params[:installment_count].to_i : 1
    if installment_count_total > 1 && installment_count_total >  @course.installment_count
      raise ActionController::BadRequest.new('installment_count invalid')
    end

    customer = Stripe::Customer.create(
      :email => current_user.email,
      :source  => params[:stripe_token]
    )

    course_purchase = CoursePurchase.new(
      user: current_user, 
      course: @course, 
      price: course_price, 
      stripe_customer_id: customer.id,
      paied_price: 0, 
      school: @course.school,
      installment_count_total: params[:installment_count].present? ? params[:installment_count].to_i : 1,
      installment_count_current: 0,
      status: CoursePurchase::STATUS_PENDING,
      start_at: Time.now()
    )

    course_purchase.save
    
    if has_discount
      UserCoupon.create(user_id: @current_user.id, coupon_id: @course.coupon.id, 
        target_id: @course.id, discount_before_price: @course.get_purchase_price,
        discount_after_price: course_price,
        target_type: @course.class.name,
        school_id: @course.school_id
      )
    end

    # お知らはい実行
    course_purchase.update_installment_status

    json_response({}, status: StatusCodeApi::SUCCESS, message: "Success")
  end

  def show_by_course
    course_purchase = CoursePurchase.find_by(user_id: @current_user.id, course_id: @course.id)
    if course_purchase.present?
      json_response(CoursePurchaseSerializer.new(course_purchase).render)
    else
      json_response({}, status: StatusCodeApi::SUCCESS, message: not_exists_message("course_purchase"))
    end
  end

  def get_coupon
    result = GetCouponService.call(@course, @current_user, params["coupon_code"])
    if result["success"]
      json_response(result, status: StatusCodeApi::SUCCESS, message: "Success")
    else
      json_response({}, status: StatusCodeApi::GET_ERROR, message: "Failed")
    end
  end

  private 
  def get_course
    @course = Course.find(params[:course_id])
  end

  def set_course_purchase
    @course_purchase = SchoolManagerUser.course_purchases.find_by_id(params[:id])
    json_response({}, status: StatusCodeApi::NOT_EXISTS, message: not_exists_message("course_purchase")) unless @course_purchase
  end

  def payment_params
    params.permit(:token, :email)
  end
end
