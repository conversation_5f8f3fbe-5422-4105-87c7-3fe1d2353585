class Api::V2::SchoolSettingsController < Api::V2::ApplicationController
  before_action :authenticate_user!

  def index
    school = SchoolManager.main_school

    design_main_page = school.design_main_page.slice("nav_color", "nav_text_color", "hamburger_color")
    classroom_menus = school.classroom_design_menu&.dig("list")

    json_response({
      name: school.name,
      image_url: school.image&.url,
      allow_user_edit_group: school.allow_user_edit_group,
      use_premium_service: school.use_premium_service,
      use_course_lesson: school.use_course_lesson,
      use_user_question: school.use_user_question,
      use_blog: school.use_blog,
      use_user_dashboard: school.use_user_dashboard,
      use_goal: school.use_goal,
      use_exam: school.use_exam,
      use_dashboard: school.use_dashboard,
      use_mail: school.use_mail,
      use_skill: school.use_skill,
      use_user_graph: school.use_user_graph,
      use_exam_stock: school.use_exam_stock,
      use_exam_recommendation: school.use_exam_recommendation,
      use_banner: school.use_banner,
      use_meeting: school.use_meeting,
      nav_color: design_main_page["nav_color"],
      nav_text_color: design_main_page["nav_text_color"],
      hamburger_color: design_main_page["hamburger_color"],
      use_ai_chat_lesson: school.use_ai_chat_lesson,
      use_ai_exam_generation: school.use_ai_exam_generation,
      use_ai_chat_goal: school.use_ai_chat_goal,
      use_ai_chat_exam: school.use_ai_chat_exam,
      use_ai_grading: school.use_ai_grading,
      using_deep_seek: school.using_deepseek?,
      use_evaluate_lesson: school.use_evaluate_lesson?,
      classroom_menus: classroom_menus,
    })
  end
end
