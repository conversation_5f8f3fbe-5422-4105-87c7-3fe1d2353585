class Api::V2::AiTutorAgentsController < Api::V2::ApplicationController
  before_action :authenticate_user!

  def index
    school = get_school_from_targetable
    return json_response([], status: 404, message: "School not found") unless school

    agents = school.ai_tutor_agents.enabled.where(agent_category: ['lesson', 'custom'])
    course_default_agent = get_course_default_agent
    sorted_agents = sort_agents_by_priority(agents, course_default_agent)

    json_response(sorted_agents.map { |agent| agent_data(agent, course_default_agent) })
  end

  def show
    school = get_school_from_targetable
    return json_response({}, status: 404, message: "School not found") unless school

    agent = school.ai_tutor_agents.enabled.find_by(id: params[:id])
    return json_response({}, status: 404, message: "Agent not found") unless agent

    json_response(agent_data(agent))
  end

  private

  def get_school_from_targetable
    targetable_type = params[:targetable_type]
    targetable_id = params[:targetable_id]

    case targetable_type
    when 'Lesson'
      lesson = Lesson.find_by(id: targetable_id)
      lesson&.school
    when 'Question'
      question = Q.find_by(id: targetable_id)
      question&.exams&.first&.school
    when 'Exam'
      exam = Exam.find_by(id: targetable_id)
      exam&.school
    else
      nil
    end
  end

  def get_course_default_agent
    return nil unless params[:targetable_type] == 'Lesson'

    lesson = Lesson.find_by(id: params[:targetable_id])
    lesson&.course&.default_ai_tutor_agent
  end

  def sort_agents_by_priority(agents, course_default_agent)
    agents_array = agents.to_a

    if course_default_agent && agents_array.include?(course_default_agent)
      other_agents = agents_array.reject { |agent| agent.id == course_default_agent.id }
      other_agents_sorted = other_agents.sort_by { |agent| [agent.is_default? ? 0 : 1, agent.name] }
      [course_default_agent] + other_agents_sorted
    else
      agents_array.sort_by { |agent| [agent.is_default? ? 0 : 1, agent.name] }
    end
  end

  def agent_data(agent, course_default_agent = nil)
    {
      id: agent.id,
      name: agent.name,
      description: agent.description,
      agent_type: agent.agent_type,
      agent_category: agent.agent_category,
      is_default: agent.is_default?,
      is_course_default: course_default_agent&.id == agent.id,
      image_url: agent.image.present? ? agent.image.url : nil,
      avatar_url: agent.avatar_url
    }
  end
end
