class Api::V2::SubscriptionsController < Api::V2::ApplicationController
  before_action :authenticate_user!
  before_action :set_subscription, only: [:update, :destroy, :show, :change_plan_list, :get_target_premium_service]

  def show
    stripe_invoices = @subscription.get_stripe_invoices(with_refunds: true)
    json_response({
      subscription: SubscriptionSerializer.new(@subscription, 'default', full_attribute: true).render,
      subscription_invoices: SubscriptionInvoice.where(subscription_id: @subscription.id).order("paied_at DESC"),
      stripe_upcoming_invoice: @subscription.get_stripe_upcoming_invoice,
      stripe_invoices: stripe_invoices.empty? ? [] : stripe_invoices["data"],
    })
  end

  def update
    @subscription.change_token(
      payment_params[:email],
      payment_params[:token]
    )

    json_response(SubscriptionSerializer.new(@subscription, request_mode).render)
  end

  def destroy
    SubscriptionCancellation.create_cancellation(@subscription, current_user, stop_reason: params[:stop_reason])
    json_response(SubscriptionSerializer.new(@subscription, request_mode).render)
  end

  def change_plan_list
    @subscription = SchoolManagerUser.subscriptions.find_by_id(params[:id])
    @current_premium_service = @subscription.premium_service
    @premium_services = PremiumService.published.each_month.where(school_id: @subscription.school_id).where.not(id: @current_premium_service.id)

    if @premium_services.empty?
      json_response({}, status: StatusCodeApi::NOT_EXISTS, message: not_exists_message("premium_services"))
    else
      json_response(BaseSerializer.render_list(@premium_services, PremiumServiceSerializer))
    end
  end

  def check_subscription_status
    @subscription = SchoolManagerUser.subscriptions.find_by_id(params[:id])

    @subscription.update_subscription_data(params[:subscription_id])

    json_response({}, status: StatusCodeApi::SUCCESS, message: "Subscription status updated")
  end

  def get_target_premium_service
    @premium_service = PremiumService.published.where(school_id: @subscription.school_id).find_by_id(params[:target_premium_service_id])

    if @premium_service.nil?
      json_response({}, status: StatusCodeApi::NOT_EXISTS, message: not_exists_message("premium_service"))
    else
      json_response(
        PremiumServiceSerializer.new(@premium_service).render_with_subscription_compare(@subscription)
      )
    end
  end

  private

  def set_subscription
    @subscription = SchoolManagerUser.subscriptions.find_by_id(params[:id])
    json_response({}, status: StatusCodeApi::NOT_EXISTS, message: not_exists_message("subscription")) unless @subscription
  end

  def payment_params
    params.permit(:token, :email)
  end
end
