class ExamsController < ApplicationController
  before_action :set_school
  layout "home_subschool"
  include RenderTeacher
  include RenderCourse

  def index
    if (params.keys - ["controller","action"]).any?
      @list = List.find params[:list_id] if params[:list_id]
      if @list.present?
        @exams = @list.allExams
      else
        @exams = SchoolManager.exams
      end

      list_search = params[:list_search] || ''
      if list_search.present?
        @exams = @exams.joins(:lists).where(lists: { id: list_search.split(",") }).distinct
      end


      @exams = @exams.distinct.published_exams.published.order(created_at: :desc)

      search = (params[:search] || '').to_s
      if search.present?
        @exams = @exams.where("exams.name like ?", "%#{search}%")
      end

      free = (params[:free] || '').to_s
      if free == '1'
        @exams = @exams.where("exams.price = 0")
      elsif free == '0'
        @exams = @exams.where("exams.price > 0")
      end

      difficult = (params[:difficult] || '').to_s
      if difficult.present?
        @exams = @exams.where("exams.difficult_rate >= ?", "#{difficult}")
      end

      @exams = @exams.page(params[:page]).per(10)

      @user_exams = current_user.user_exams.actual.joins(:exam).where.not("exams.published_at": nil).where("exams.exam_root_id": nil).pluck("exams.id") if user_signed_in?
      @course_tags = Course.all.tag_counts_on(:skills)
      @lists = List.where(school_id: SchoolManager.school_ids).roots
      @exam_type = Exam::TYPE_NORMAL_EXAM

      liquid_data = {
        "list" => list_render(@list),
        "exams" => LiquidRender::RenderModel.render_data_list(@exams) do | item |
          render_exam(item)
        end,
        "user_exams" => @user_exams,
        "course_tags" => LiquidRender::RenderModel.render_data_list(@course_tags),
        # Liquid Dropはたまに深いmethod使えない場合、直接にrenderにしました。
        "lists" => @lists.select{ | item | item.exams_count > 0 }.map do | item |
          list_render(item)
        end,
        "exam_type" => @exam_type,
        "search" => search,
        "difficult" => difficult,
        "free" => free,
        "inquiry_form" => render_form_inquiry,
        "one_week_later" => Date.today.end_of_week.strftime("%-m月%-d日"),
        "page_query" => 
          if @list
            "&list_id=#{@list.id}&type=#{@exam_type}&search=#{search}&free=#{free}"
          else
            "&type=#{@exam_type}&search=#{search}&free=#{free}"
          end
      }

      @page_custom_css = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_css(:exam_list, liquid_data, params[:revision_id])
      @page_custom_header = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_header(:exam_list, liquid_data, params[:revision_id])
      @page_custom_footer = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_footer(liquid_data, params[:revision_id])
      @page_custom_html = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_html(:exam_list, liquid_data, @school, user_signed_in?, params[:revision_id])
    else
      @exams = SchoolManager.exams
      @exams = @exams.distinct.published_exams.published.order(created_at: :desc)
      @exams = @exams.page(params[:page]).per(10)
      @user_exams = current_user.user_exams.actual.joins(:exam).where.not("exams.published_at": nil).where("exams.exam_root_id": nil).pluck("exams.id") if user_signed_in?
      @course_tags = Course.all.tag_counts_on(:skills)
      @lists = List.where(school_id: SchoolManager.school_ids).roots
      @exam_type = Exam::TYPE_NORMAL_EXAM
      cache_data = Rails.cache.read("exams-index-#{@school&.id}-html")
      if cache_data && params["preview_code"].nil?
        @page_custom_html = cache_data
        @page_custom_css = Rails.cache.read("exams-index-#{@school&.id}-css")
        @page_custom_header = Rails.cache.read("exams-index-#{@school&.id}-header")
        @page_custom_footer = Rails.cache.read("exams-index-#{@school&.id}-footer")
      else
        search = ""
        free = ""
        liquid_data = {
          "exams" => LiquidRender::RenderModel.render_data_list(@exams) do | item |
            render_exam(item)
          end,
          "user_exams" => @user_exams,
          "course_tags" => LiquidRender::RenderModel.render_data_list(@course_tags),
          # Liquid Dropはたまに深いmethod使えない場合、直接にrenderにしました。
          "lists" => @lists.select{ | item | item.exams_count > 0 }.map do | item |
            list_render(item)
          end,
          "exam_type" => @exam_type,
          "search" => search,
          "free" => free,
          "one_week_later" => Date.today.end_of_week.strftime("%-m月%-d日"),
          "inquiry_form" => render_form_inquiry,
          "page_query" => "&type=#{@exam_type}&search=#{search}&free=#{free}"
        }

        @page_custom_css = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_css(:exam_list, liquid_data, params[:revision_id])
        @page_custom_header = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_header(:exam_list, liquid_data, params[:revision_id])
        @page_custom_footer = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_footer(liquid_data, params[:revision_id])
        @page_custom_html = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_html(:exam_list, liquid_data, @school, user_signed_in?, params[:revision_id])
        if params["preview_code"].nil?
          Rails.cache.write("exams-index-#{@school&.id}-html", @page_custom_html)
          Rails.cache.write("exams-index-#{@school&.id}-css", @page_custom_css)
          Rails.cache.write("exams-index-#{@school&.id}-header", @page_custom_header)
          Rails.cache.write("exams-index-#{@school&.id}-footer", @page_custom_footer)
        end
      end
    end
  end

  def show
    @exam = SchoolManager.exams.find(params[:id])
    set_affiliate(@exam, Affiliate.action_names["clicked"], 0)
    @exam_average = @exam.average @user_ids
    @user_exams = @exam.user_exams
                        .includes([:user, :exam])
                        .finished
                        .actual
                        .order(score: :desc)
    @user_exams_count = @user_exams.size
    @courses = @exam.courses
    cache_data = Rails.cache.read("exams-#{@exam.id}")
    liquid_data = {}

    if cache_data && params["preview_code"].nil?
      liquid_data = cache_data
      liquid_data["inquiry_form"] = render_form_inquiry
      liquid_data["can_guest_test"] = !current_user.present? && SchoolManager.main_school&.lazy_sign_up?
    else
      @qs = @exam.qs.includes(:skill_items, answer_items: :users)
      @user_exam = current_user.user_exams.actual.joins(:exam).where(exams: {id: params[:id]}).first if user_signed_in?

      liquid_data = {
        "exam" => LiquidRender::RenderModel.render_data_one(@exam, [:pass_rate, :average]),
        "director" => LiquidRender::RenderModel.render_data_one(@exam.director),
        "user_exams" => LiquidRender::RenderModel.render_data_list(@user_exams),
        "exam_average" => @exam_average,
        "user_exams_count" => @user_exams_count,
        "user_exams_passed_count" => @exam.passed_user_exams_count,
        "qs" => LiquidRender::RenderModel.render_data_list(@qs),
        "courses" => LiquidRender::RenderModel.render_data_list(@courses),
        "teacher" => render_teacher(@exam.teacher),
        "one_week_later" => Date.today.end_of_week.strftime("%-m月%-d日"),
        "inquiry_form" => render_form_inquiry,
        # なぜかdrop_dataは効かないため、直接にデータをtsくりました。
        "exam_skills" => @exam.real_exam_skills.map do |exam_skill|
          {
            "skill" => {
              "name" => exam_skill.skill.name,
              "skill_items" => @exam.skill_items.where(skill_id: exam_skill.skill.id).distinct.map do | skill_item |
                {
                  "name" => skill_item.name
                }
              end
            }
          }
        end,
        "summaries" => @exam.summaries.nil? ? [] : (@exam.summaries["list"] || []).map do | item |
          {
            "title" => item["title"],
            "content" => @exam.summary_content(item["content"])
          }
        end,
        "exam_body" => UtilityHelper.markdown_to_html(@exam.active_body),
        "exam_btn" => exam_btn(@exam),
        "can_guest_test" => !current_user.present? && SchoolManager.main_school&.lazy_sign_up?
      }
      Rails.cache.write("exams-#{@exam.id}", liquid_data)
    end
    @page_custom_css = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_css(:exam_show, liquid_data, params[:revision_id])
    @page_custom_header = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_header(:exam_show, liquid_data, params[:revision_id])
    @page_custom_footer = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_footer(liquid_data, params[:revision_id])
    @page_custom_html = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_html(:exam_show, liquid_data, @school, user_signed_in?, params[:revision_id])
  end

  def preview
    raise CanCan::AccessDenied.new("Not authorized!") unless current_user.present?

    SchoolManagerUser.init current_user
    raise CanCan::AccessDenied.new("Not authorized!") unless SchoolManagerUser.admin_or_exam_manager?

    @exam = SchoolManager.exams.find params[:id]
    @user_exams = current_user.user_exams.where(exam: @exam, test_mode: false)
    @user_exams.destroy_all if @user_exams.present?

    redirect_to "/classroom/exams/#{@exam.id}"
  end

  private

  def render_exam(exam)
    LiquidRender::RenderModel.render_data_one(exam, [:pass_rate, :average]).merge({
      "exam_skills" => exam.real_exam_skills.map do | exam_skill |
        {
          "skill" => {
            "name" => exam_skill.skill.name,
            # パフォーマンスのため、一旦一覧画面にskill_items許可しません
            # "skill_items" => exam.skill_items.where(skill_id: exam_skill.skill.id).distinct.map do | skill_item |
            #   {
            #     "name" => skill_item.name
            #   }
            # end
          }
        }
      end,
      "director" => LiquidRender::RenderModel.render_data_one(exam.director),
      "lists" => exam.lists.map do |list|
        list_render(list)
      end
    })
  end
  def render_list(list)
    LiquidRender::RenderModel.render_data_one(list, [:children, :exam_count])
  end

  def list_render(list)
    return {} if list.nil?
    {
      "name" => list.name,
      "description" => list.description,
      "id" => list.id,
      "exams_count" => list.exams_count,
      "children" => list.children.select{ | child | child.exams_count > 0 }.map do | child |
        list_render(child)
      end
    }
  end

  def exam_btn(exam)
    unless current_user.present?
      if exam.user_exam_type == 'include_guest_and_notify' || exam.user_exam_type == 'include_guest'
        return {
          "name" => "テストを受けてみる",
          "link" => guests_path(exam_id: exam.id)
        }
      else
        return {
          "name" => "ログインしてテストを受ける",
          "link" => classroom_exam_path(exam)
        }
      end
    end

    user_exam = exam.get_user_exam(current_user)
    if user_exam.present?
      if user_exam.finished?
        return {
          "name" => "WEBテストの結果をみる",
          "link" => classroom_exam_path(exam)
        }
      else
        return {
          "name" => "WEBテストを受け続ける",
          "link" => classroom_exam_path(exam)
        }
      end
    else
      if exam.free?
        return {
          "name" => "今すぐWEBテストを受ける",
          "link" => classroom_exam_path(exam)
        }
      else
        return {
          "name" => "#{exam.price}円でWEBテストを受ける",
          "link" => new_exam_exam_purchase_path(exam)
        }
      end
    end
  end
end
