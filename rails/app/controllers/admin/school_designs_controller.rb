class Admin::SchoolDesignsController < Admin::ApplicationController
  load_and_authorize_resource :school
  before_action :ensure_school_design_general_enabled, only: [:general, :save_general]
  before_action :ensure_school_designs_code_editor_enabled, only: [:page_main]
  before_action :school_onboarding_detect
  load_and_authorize_resource :except => [:create]
  before_action :set_mode
  layout :admin_layout
  skip_before_action :verify_authenticity_token

  add_breadcrumb 'スクールデザイン', only: [:index, :update, :menu, :header, :design_images]

  def index
    @search = params[:q]
    @school_designs = @school.school_designs.where("school_designs.name like ?", "%#{@search}%")
    @school_design = @school.school_design
  end

  def search
    index
  end

  def create
    school_design_params = params.require(:school_design).permit(:name, :description, :image)
    # SchoolDesign.new_default(school_design_params[:name], @school.id)
    @school.school_designs.create(school_design_params.merge(SchoolDesign::DEFAUL_VALUE))
    @school.complete_onboarding_school_design
    @school.save!
    redirect_to action: 'index'
  end

  def edit
  end

  def update_basic
    school_design_params = params.require(:school_design).permit(:name, :description, :image)
    @school_design = @school.school_designs.find(params[:id])
    @school_design.update(school_design_params)
    redirect_to action: 'index'
  end

  # ページDesign
  def page_main
    @page_name = :main_page_custom
    @pickup_resources = @school.pickup_resources.where(objectionable_type: "Course", controller_name: "home", action_name: "index")
    render_page_custom
  end

  def save_page_main
    @page_name = :main_page_custom
    update_page_custom
    render 'save_page_custom'
  end

  def page_exam_list
    @page_name = :exam_list
    render_page_custom
  end

  def save_page_exam_list
    @page_name = :exam_list
    update_page_custom
    render 'save_page_custom'
  end

  def page_exam_show
    @page_name = :exam_show
    render_page_custom
  end

  def save_page_exam_show
    @page_name = :exam_show
    update_page_custom
    render 'save_page_custom'
  end

  def page_course_list
    @page_name = :course_list
    render_page_custom
  end

  def save_page_course_list
    @page_name = :course_list
    update_page_custom
    render 'save_page_custom'
  end

  def page_purchase_cart
    @page_name = :purchase_cart
    render_page_custom
  end

  def save_page_purchase_cart
    @page_name = :purchase_cart
    update_page_custom
    render 'save_page_custom'
  end

  def page_purchase_checkout
    @page_name = :purchase_checkout
    render_page_custom
  end

  def save_page_purchase_checkout
    @page_name = :purchase_checkout
    update_page_custom
    render 'save_page_custom'
  end

  def page_purchase_thank
    @page_name = :purchase_thank
    render_page_custom
  end

  def save_page_purchase_thank
    @page_name = :purchase_thank
    update_page_custom
    render 'save_page_custom'
  end

  def page_course_show
    @page_name = :course_show
    render_page_custom
  end

  def save_page_course_show
    @page_name = :course_show
    update_page_custom
    render 'save_page_custom'
  end

  def page_lesson_show
    @page_name = :lesson_show
    render_page_custom
  end

  def save_page_lesson_show
    @page_name = :lesson_show
    update_page_custom
    render 'save_page_custom'
  end

  def page_blog_list
    @page_name = :blog_list
    render_page_custom
  end

  def save_page_blog_list
    @page_name = :blog_list
    update_page_custom
    render 'save_page_custom'
  end

  def page_blog_show
    @page_name = :blog_show
    render_page_custom
  end

  def save_page_blog_show
    @page_name = :blog_show
    update_page_custom
    render 'save_page_custom'
  end

  def page_premium_list
    @page_name = :premium_list
    render_page_custom
  end

  def save_page_premium_list
    @page_name = :premium_list
    update_page_custom
    render 'save_page_custom'
  end

  def page_premium_show
    @page_name = :premium_show
    render_page_custom
  end

  def save_page_premium_show
    @page_name = :premium_show
    update_page_custom
    render 'save_page_custom'
  end

  def page_inquiry
    @page_name = :inquiry
    render_page_custom
  end

  def save_page_inquiry
    @page_name = :inquiry
    update_page_custom
    render 'save_page_custom'
  end

  def page_inquiry_thank
    @page_name = :inquiry_thank
    render_page_custom
  end

  def save_page_inquiry_thank
    @page_name = :inquiry_thank
    update_page_custom
    render 'save_page_custom'
  end

  def page_review_show
    @page_name = :review_show
    render_page_custom
  end

  def save_page_review_show
    @page_name = :review_show
    update_page_custom
    render 'save_page_custom'
  end

  def page_teacher_list
    @page_name = :teacher_list
    render_page_custom
  end

  def save_page_teacher_list
    @page_name = :teacher_list
    update_page_custom
    render 'save_page_custom'
  end

  def page_teacher_show
    @page_name = :teacher_show
    render_page_custom
  end

  def save_page_teacher_show
    @page_name = :teacher_show
    update_page_custom
    render 'save_page_custom'
  end

  def page_question_list
    @page_name = :question_list
    render_page_custom
  end

  def save_page_question_list
    @page_name = :question_list
    update_page_custom
    render 'save_page_custom'
  end

  def page_question_show
    @page_name = :question_show
    render_page_custom
  end

  def save_page_question_show
    @page_name = :question_show
    update_page_custom
    render 'save_page_custom'
  end

  def page_school_plan_show
    @page_name = :school_plan_show
    render_page_custom
  end

  def page_school_plan_list
    @page_name = :school_plan_list
    render_page_custom
  end

  def save_page_school_plan_list
    @page_name = :school_plan_list
    update_page_custom
    render 'save_page_custom'
  end

  def page_custom_page_list
    @page_name = :custom_page_list
    render_page_custom
  end

  def save_page_custom_page_list
    @page_name = :custom_page_list
    update_page_custom
    render 'save_page_custom'
  end

  def save_page_school_plan_show
    @page_name = :school_plan_show
    update_page_custom
    render 'save_page_custom'
  end

  def page_default_page_template
    @page_name = :default_page_template
    render_page_custom
  end

  def save_page_default_page_template
    @page_name = :default_page_template
    update_page_custom
    render 'save_page_custom'
  end

  def page_default_css
    @page_name = :default_css
    render_page_custom
  end

  def save_page_default_css
    @page_name = :default_css
    update_page_custom
    render 'save_page_custom'
  end

  def page_default_footer
    @page_name = :default_footer
    render_page_custom
  end

  def save_page_default_footer
    @page_name = :default_footer
    update_page_custom
    render 'save_page_custom'
  end

  def page_default_header
    @page_name = :default_header
    render_page_custom
  end

  def save_page_default_header
    @page_name = :default_header
    update_page_custom
    render 'save_page_custom'
  end

  def page_custom_page_sign_up
    @page_name = :custom_page_sign_up
    render_page_custom
  end

  def save_page_custom_page_sign_up
    @page_name = :custom_page_sign_up
    update_page_custom
    render 'save_page_custom'
  end

  def page_custom_page_sign_in
    @page_name = :custom_page_sign_in
    render_page_custom
  end

  def save_page_custom_page_sign_in
    @page_name = :custom_page_sign_in
    update_page_custom
    render 'save_page_custom'
  end

  def page_custom_page_subscriptions_new
    @page_name = :custom_page_subscriptions_new
    render_page_custom
  end

  def save_page_custom_page_subscriptions_new
    @page_name = :custom_page_subscriptions_new
    update_page_custom
    render 'save_page_custom'
  end

  def page_custom_page_course_purchases_new
    @page_name = :custom_page_course_purchases_new
    render_page_custom
  end

  def save_page_custom_page_course_purchases_new
    @page_name = :custom_page_course_purchases_new
    update_page_custom
    render 'save_page_custom'
  end

  def design_images
    if @school_design
      @imagable = @school_design
      render 'design_images'
    else
      @imagable = @school
      render 'design_images_global'
    end
  end

  def update_page_custom
    school_design_params = params.permit(
      :name,
      :school_id,
      :id,
      page_custom: [
        :page_description,
        :page_description_css,
        :page_description_header,
      ]
    )

    # revision_idがある場合、revisionから使う
    if params[:revision_id]
      use_revision = @school_design.revisions.find(params[:revision_id])
      school_design_params[:page_custom] = use_revision.body_json
    end

    @school_design.send("#{@page_name.to_s}=", school_design_params[:page_custom])
    @school_design.name = school_design_params[:name] unless school_design_params[:name].empty?
    @school_design.save

    # ヒストリーを作成します。
    revision = Revision.new
    revision.target = @school_design
    revision.school = @school
    revision.user = current_user
    revision.target_key = @page_name.to_s
    revision.body_json = school_design_params[:page_custom]
    revision.save
  end

  def menu
    @school_design = @school.school_design
  end

  def save_menu
    design_params = params.require(:school).permit(
      design_menu: [
        list: [
          :name,
          :link,
          :view_name,
          :visibility,
          :classroom_visibility,
          :kbn,
          :page_custom_id,
          :sub_menu_key,
          :class_name
        ]
      ]
    )

    @school.update(design_params)
  end

  def header
    @school_design = @school.school_design
  end

  def save_header
    design_params = params.permit(
      design_main_page: [
        :page_logo,
        :favicon,
        :title,
        :description,
        :nav_color,
        :nav_scrolled_color,
        :nav_text_color,
        :hamburger_color,
      ]
    )

    begin
      if design_params[:design_main_page][:page_logo].present?
        design_params[:design_main_page][:page_logo] = upload_image_design(design_params[:design_main_page][:page_logo])
      else
        design_params[:design_main_page][:page_logo] = @school.design_main_page["page_logo"]
      end

      if design_params[:design_main_page][:favicon].present?
        design_params[:design_main_page][:favicon] = upload_image_design(design_params[:design_main_page][:favicon])
      else
        design_params[:design_main_page][:favicon] = @school.design_main_page["favicon"]
      end
      @school.update(design_params)
      @error_message = ""
    rescue CarrierWave::ProcessingError => e
      p e
      @error_message = "アップロードに失敗しました。アップロードできるのはjpg, pngのみです。"
    rescue => e
      p e
      @error_message = e.message
    end
  end

  def general
    @school_design = @school.school_design
  end

  def save_general
    design_params = params.permit(
      :design_logo,
      design_general: [
        :favicon,
        :meta_description,
        :school_title,
        :fb_page,
        :google_analytics_code,
        :eye_catching_image,
        :use_manual_global_header,
        :use_manual_global_footer,
        :fixed_header_positon,
      ],
    )

    if design_params[:design_general][:favicon].present?
      design_params[:design_general][:favicon] = upload_image_design(design_params[:design_general][:favicon], favicon: true)
    end

    if design_params[:design_general][:eye_catching_image].present?
      design_params[:design_general][:eye_catching_image] = upload_image_design(design_params[:design_general][:eye_catching_image])
    else
      design_params[:design_general][:eye_catching_image] = @school.design_general["eye_catching_image"]
    end
    @school.update(design_params)
    @school.complete_onboarding_school_design_general
    @school.save!
  end

  def new
    @school_design = SchoolDesign.new
  end

  def update
    school_design_params = params.require(:school_design).permit(
      :school_id, :id,
      main_page_custom: [
        :page_description, :page_description_css
      ])
    respond_to do |format|
      @school_design.update(school_design_params)
      format.html { redirect_to admin_school_school_designs_path(@school), notice: t('message.update') }
      format.js { }
    end
  end

  def active
    @school.school_designs.update_all(actived: false)
    @school_design.update!(
      actived: true,
      actived_at: Time.now
    )
    redirect_to admin_school_school_designs_path(@school), notice: t('message.update')
  end

  def copy
    ActiveRecord::Base.transaction do
      new_school_design = @school_design.dup
      new_school_design.name = "#{@school_design.name} Copy"
      new_school_design.actived = false
      new_school_design.actived_at = nil
      new_school_design.preview_code = rand(36**32).to_s(36)
      new_school_design.default_page_template = @school_design.default_page_template
      new_school_design.default_css = @school_design.default_css
      new_school_design.default_footer = @school_design.default_footer
      new_school_design.default_header = @school_design.default_header
      new_school_design.save

      # SchoolDesign画像コピーする
      images = Image.where(imagable: @school_design)
      images.each do |image|
        new_image = image.dup
        new_image.imagable = new_school_design
        new_image.save
      end

      # テンプレートコピー
      @school_design.page_templates.each do |page_template|
        new_page_template = page_template.dup
        new_page_template.school_design = new_school_design
        new_page_template.save
      end
    end

    redirect_to admin_school_school_designs_path(@school), notice: t('message.update')
  end

  def destroy
    @school_design.destroy unless @school_design.actived
    redirect_to admin_school_school_designs_path(@school), notice: "削除しました"
  end

  private

  def upload_image_design(uploaded_io, favicon: false)
    uploader = favicon ? FaviconUploader.new : DesignUploader.new
    uploader.store!(uploaded_io)
    uploader.retrieve_from_store!(uploaded_io.original_filename)
    uploader.url
  end

  def set_mode
    @page_custom_page = false
    # mode: ui or code
    @mode = (params[:mode] || 'ui').to_sym
  end

  def render_page_custom
    @page_custom_page = true
    session[:current_school_design_id] = @school_design.id

    # revision_id処理
    if params[:revision_id]
      revision = @school_design.revisions.find(params[:revision_id])
      @school_design.send("#{@page_name.to_s}=", revision.body_json)
    end

    if @mode == :ui
      render 'page_custom_ui'
    else
      render 'page_custom'
    end
  end

  def admin_layout
    "admin_material"
  end

  def ensure_school_design_general_enabled
    unless @school.use_school_designs_general?
      render plain: "このページにアクセスする権限がありません。" and return
    end
  end

  def ensure_school_designs_code_editor_enabled
    return unless params[:mode] == "code"

    unless @school.use_school_designs_code_editor?
      render plain: "このページにアクセスする権限がありません。" and return
    end
  end
end
