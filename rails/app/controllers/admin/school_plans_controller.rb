class Admin::SchoolPlansController < Admin::ApplicationController
  # before_action :set_school_plan, only: [:show, :edit, :update, :destroy]
  load_and_authorize_resource :school_plan
  before_action :admin_only, unless: -> { @school.present? }
  layout 'admin'

  def index
    @school_plans = SchoolPlan.rank(:row_order).all
  end

  def show; end

  def new
    @school_plan = SchoolPlan.new
  end

  def edit; end

  def create
    @school_plan = SchoolPlan.new(school_plan_params)

    respond_to do |format|
      if @school_plan.save
        format.html { redirect_to [:admin, @school_plan], notice: 'School plan was successfully created.' }
        format.json { render :show, status: :created, location: @school_plan }
      else
        format.html { render :new }
        format.json { render json: @school_plan.errors, status: :unprocessable_entity }
      end
    end
  end

  def update
    @school_plan.update!(school_plan_params)
    # respond_to do |format|
    #   if @school_plan.update(school_plan_params)
    #     format.html { redirect_to [:admin, @school_plan], notice: 'School plan was successfully updated.' }
    #     format.json { render :show, status: :ok, location: @school_plan }
    #     format.js { render :update, status: :ok, location: @school_plan}
    #   else
    #     format.html { render :edit }
    #     format.json { render json: @school_plan.errors, status: :unprocessable_entity }
    #   end
    # end
  end

  def destroy
    @school_plan.destroy
    respond_to do |format|
      format.html { redirect_to admin_school_plans_url, notice: 'School plan was successfully destroyed.' }
      format.json { head :no_content }
    end
  end

  def sort
    school_plan = SchoolPlan.find params[:id]
    school_plan.update(params.require(:school_plan).permit(:row_order_position))
  end

  def add_school
    school = School.find(params[:school_id])
    SchoolPlanSubscription.add_by_handle(school, current_user, @school_plan)
    redirect_back(fallback_location: root_path, notice: '追加しました。')
  end

  private

  # Use callbacks to share common setup or constraints between actions.
  def set_school_plan
    @school_plan = SchoolPlan.find(params[:id])
  end

  # Never trust parameters from the scary internet, only allow the white list through.
  def school_plan_params
    params.require(:school_plan).permit(
      :title,
      :body,
      :description,
      :image,
      :monthly,
      :annually,
      :commision_rate,
      :card_commision_rate,
      :published,
      :published_at,
      :use_premium_service,
      :use_course_lesson,
      :use_user_question,
      :use_blog,
      :use_dashboard,
      :use_goal,
      :use_mail,
      :use_skill,
      :use_user_graph,
      :use_exam_stock,
      :use_exam_recommendation,
      :use_banner,
      :use_user_dashboard,
      :use_exam,
      :use_embed,
      :allow_user_edit_group,
      :registerable,
      :lazy_sign_up,
      :target_school_id,
      :term_id,
      :limit_manager_count,
      :limit_vimeo_count,
      :limit_ai_message,
      :use_vector_button,
      :use_meeting,
      :use_meeting_admin,
      :use_evaluate_lesson,
      :use_school_onboarding,
      :use_user_list,
      :use_user_blocks,
      :use_teacher_list,
      :use_iams,
      :use_affiliates,
      :use_catalog,
      :use_exam_prompt,
      :use_question_list,
      :use_course_question,
      :use_user_group,
      :use_signup_email_template,
      :use_step_email,
      :use_coupon,
      :use_access_report,
      :use_school_designs_code_editor,
      :use_school_designs_general,
      :use_lists,
      :use_terms,
      :use_activities,
      :use_school_plan,
      :use_custom_texts,
      :use_ai_chat,
      :use_ai_chat_question,
      :use_ai_chat_lesson,
      :use_ai_exam_generation,
      :use_ai_chat_goal,
      :use_ai_chat_exam,
      :use_ai_grading
    )
  end
end
