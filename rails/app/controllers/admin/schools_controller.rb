class Admin::SchoolsController < Admin::ApplicationController
  include SchoolStatisticable
  load_resource
  authorize_resource
  before_action :set_duration, only: :show
  before_action :school_onboarding_detect, only: [:show, :update, :edit]
  layout :admin_layout

  add_breadcrumb -> { "スクール編集" }, only: :edit
  add_breadcrumb -> { "ダッシュボード" }, only: :show

  def index
    @search = params[:q]
    @schools = @schools.where("schools.name like ?", "%#{@search}%").includes(:courses, :goals, :users).page(params[:page]).per(Settings.paginations.default_perpage)
  end


  def search
    index
  end

  def show
    return unless @school.use_dashboard?
    @goals = @school.goals.includes([managers: :user]).accessible_by(Ability.new(current_user))
    @projects = @school.projects.includes([managers: :user]).accessible_by(Ability.new(current_user))
    @type = SchoolSummary::DEFAULT
    @type = params[:type].to_i if params[:type].present?
    @school_info = {}

    if params[:flag] == "summary"
      @school_info[:flag] = "summary"
      @summaries = @school.school_summaries.where(SchoolSummary.date_query_range(@duration[0], @duration[1], "school_summaries.")).order(date: :desc).page(params[:page]).per(Settings.paginations.default_perpage)
    elsif params[:flag] == "download-summary"
      @school_info[:flag] = "download-summary"
      @summaries = @school.school_summaries.where(SchoolSummary.date_query_range(@duration[0], @duration[1], "school_summaries.")).order(date: :desc)
    else
      @summaries = @school.school_summaries.where(SchoolSummary.date_query_range(@duration[0], @duration[1], "school_summaries.")).order(date: :desc).page(params[:page]).per(Settings.paginations.default_perpage)
      @school_info[:flag] = "dashboard"
      @school_info[:learning_data] = get_learning_info
      @school_info[:lessons_data] = get_lessons_info
      @school_info[:activities_data] = get_activity_info
      @school_info[:inquiries_data] = get_inquiry_info
      @school_info[:subscription_data] = get_price_info
      # @school_info[:mail_data] = get_mail_info
      # @school_info[:impression] = get_impression_info
      @school_info[:visitor] = get_visitor_info
      @school_info[:course] = get_course_info
      @school_info[:user_exam] = get_exam_info
      @school_info[:block_email] = get_block_email_info
    end

    @top_coursers = Course.top_course(@school)

    respond_to do |format|
      format.js {@school_info}
      format.html
      format.csv do
        send_data render_to_string, filename: "#{@school.name}_#{Time.zone.now.strftime("%Y-%m-%d")}.csv", type: :csv
      end
    end
  end

  def new
    @plans = Plan.master
    @using_master_plans = Array.new
  end

  def edit
    @plans = Plan.master
    @using_master_plans = Array.new
  end

  def create
    respond_to do |format|
      if @school.save
        format.html { redirect_to process_polymophic(admin_school_path(@school)), notice: t("message.create")}
        format.json { render :show, status: :created, location: @school }
      else
        @plans = Plan.master
        @using_master_plans = Array.new
        format.html { render :new }
        format.json { render json: @school.errors, status: :unprocessable_entity }
      end
    end
  end

  def update
    @school.attributes = school_params
    @school.copy_plans ||= [] if request.format.symbol == :html

    respond_to do |format|
      if @school.save
        format.html { redirect_to process_polymophic(admin_school_path(@school)), notice: t("message.update")}
        format.json { render :show, status: :ok, location: @school }
      else
        @plans = Plan.master
        @using_master_plans = Array.new
        format.html { render :edit }
        format.json { render json: @school.errors, status: :unprocessable_entity }
      end
    end
  end

  def destroy
    if params[:soft_delete].presence ? @school.soft_destroy : @school.destroy
      respond_to do |format|
        format.html { redirect_back fallback_location: admin_schools_url, notice: t(params[:soft_delete].presence ? "message.soft_delete" : "message.destroy")}
        format.json { head :no_content }
      end
    else
      redirect_to admin_schools_url, notice: @school.errors.full_messages.join("\n")
    end
  end

  def restore
    @school.restore
    respond_to do |format|
      format.html { redirect_back fallback_location: admin_schools_url, notice: t("message.restore")}
    end
  end

  def survey
    admin_only
    @view_only = true
  end

  def list_courses
    @school = School.find(params[:school_id])
    respond_to do |format|
      format.js { render partial: 'admin/schools/list_courses', locals: { school: @school } }
    end
  end

  def list_exams
    @school = School.find(params[:school_id])
    @exams = @school.exams.where(id: params[:exam_ids])
    respond_to do |format|
      format.js { render partial: 'admin/schools/list_exams', locals: { school: @school, exams: @exams } }
    end
  end

  private

  def school_params
    params.require(:school).permit(
      :name, :description, :body, :image, :duration, :duration_start_at, :subscription_type, :registerable,
      :domain, :subdomain, :lazy_sign_up, :allow_user_edit_group, :use_premium_service, :use_goal,
      :use_course_lesson, :use_user_question, :use_blog, :use_dashboard, :use_banner, :use_user_dashboard, :use_vector_button,
      :use_mail, :use_skill, :use_user_graph, :use_exam_stock, :use_exam_recommendation, :from_mail, :sender_name,
      :use_exam, :use_ai_chat_lesson, :use_ai_exam_generation, :use_ai_chat_goal, :use_ai_chat_exam, :use_ai_grading,
      :use_exam, :vimeo_account_id, :use_meeting, :use_meeting_admin, :use_evaluate_lesson,
      :use_school_onboarding,
      :use_user_list, :use_user_blocks, :use_teacher_list, :use_iams, :use_affiliates, #user menu options
      :use_catalog, # course
      :use_exam_prompt, # exam
      :use_question_list, :use_course_question, # question
      :use_user_group, :use_signup_email_template, :use_step_email, :use_coupon, :use_access_report, # marketing
      :use_school_designs_code_editor, :use_school_designs_general, :use_lists, :use_terms, :use_activities, :use_school_plan, :use_custom_texts, # school menu
      :use_ai_chat, :use_ai_chat_question, :use_ai_model_api, # ai menu
      whitelisted_ips: [], copy_plans: [],
      limitation_attributes: [:id, :users, :evaluation, :goals, :exams, :chat, :announcement_mails]
    )
  end

  def admin_layout
    case action_name
    when "index", "create", "new"
      "admin"
    else
      "admin_material"
    end
  end
end
