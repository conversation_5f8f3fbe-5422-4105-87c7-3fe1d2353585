class Admin::Schools::SalesManagementsController < Admin::ApplicationController
  include SchoolStatisticable
  load_resource :school
  load_and_authorize_resource :sale_management, through: :school
  before_action :set_duration
  before_action :school_onboarding_detect
  layout "admin_material"

  add_breadcrumb -> { "売上管理" }, path: [:admin, :@school, :sale_managements]

  def index
    @school_info = {}
    @school_info[:sale_managements] = get_sale_management_info params
    @filter_type = params[:filter_type] || "all"

    @sale_managements = @sale_managements.query_by_duration(@duration[0], @duration[1]).order_created_at_desc
    @course_purchase_list = @sale_managements.is_course
    @exam_purchase_list = @sale_managements.is_exam
    @meeting_ticket_purchase_list = @sale_managements.is_meeting_ticket
    @subscription_purchase_list = @sale_managements.ignore_zero_price.is_premium_service

    case @filter_type
    when "premium_service"
      @filtered_sales = @sale_managements.ignore_zero_price.is_premium_service
    when "course"
      @filtered_sales = @sale_managements.is_course
    when "exam"
      @filtered_sales = @sale_managements.is_exam
    when "meeting_ticket"
      @filtered_sales = @sale_managements.is_meeting_ticket
    else
      @filtered_sales = @sale_managements
    end

    @paginated_sales = @filtered_sales.page(params[:page]).per(params[:per_page])

    respond_to do |format|
      format.js { @school_info }
      format.html do
        @sale_managements = @paginated_sales
      end
      format.csv do
        @export_sales = @filtered_sales.filter_by_query(params[:search])

        filter_indicator = (@filter_type == "all") ? "" : "_#{@filter_type}"
        timestamp = Time.zone.now.strftime("%Y%m%d_%H%M%S")
        date_range = "#{@duration[0].strftime("%Y%m%d")}_to_#{@duration[1].strftime("%Y%m%d")}"

        csv_filename = "#{@school.name}_sales#{filter_indicator}_#{date_range}_#{timestamp}.csv"
        send_data render_to_string, filename: csv_filename, type: :csv
      end
    end
  end
end
