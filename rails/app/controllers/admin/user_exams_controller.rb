class Admin::UserExamsController < Admin::ApplicationController
  include UserSkillHeader
  load_resource :school
  before_action :school_onboarding_detect
  authorize_resource :school, if: -> {@school.present?}
  load_and_authorize_resource
  load_resource :user
  before_action :set_resources, except: :destroy
  before_action :get_user_info_header, only: :index

  add_breadcrumb -> { 'ユーザー一覧' }, path: [:admin, :@school, :users], if: ->{ @user.present? }
  add_breadcrumb -> { @user.name }, path: [:admin, :@school, :@user], if: ->{ @user.present? }
  add_breadcrumb -> { @user_exam.exam.name }, path: [:admin, :@school, :@user, :@user_exam], if: ->{ @user.present? && @user_exam.present? }

  layout :layout_by_scope

  def set_resources
    return if params[:user_goal_id].blank?
    @user_goal = UserGoal.find params[:user_goal_id]
    @user = @user_goal.user
    @goal = @user_goal.goal
    @project = @goal.project
    @school = @goal.school
  end
  # GET /user_exams
  # GET /user_exams.json
  def index
    @lists = @school.lists.roots
    @lists_score = get_lists_score
    set_user_exams
    @actual_user_exams = @user.actual_user_exams(@school).finished
    if params[:list_id].present?
      list = List.find params[:list_id]
      user_exam_id = list.get_exams(@user.id)
      @actual_user_exams = @actual_user_exams.where(exam_id: user_exam_id)
    end
    if @school.present?
      @user_exams = @user_exams.where(exam_id: @school.exam_ids)
      return render "index"
    else
      return render "index", layout: "admin"
    end
  end

  # GET /user_exams/1
  # GET /user_exams/1.json
  def show
    set_user_exams
    @exam = @user_exam.exam
    @as = @user_exam.as.includes([q: :skill_items], [answer_item: :as]).group_by {|a| a.q }
    @qs = Q.with_order(@user_exam.freezerank_sorted)
    @assessments = @user_exam.assessments
    @comments = Comment.where(commentable: @user_exam)
    @assessment_group = @user_exam.assessments_group_by_skill
    return render "user_exam_result"
  end

  # GET /user_exams/new
  def new
    @user_exam = UserExam.new
  end

  # GET /user_exams/1/edit
  def edit
  end

  # POST /user_exams
  # POST /user_exams.json
  def create
    @user_exam = UserExam.new(user_exam_params)

    respond_to do |format|
      if @user_exam.save
        format.html { redirect_to @user_exam, notice: t('message.create')}
        format.json { render :show, status: :created, location: @user_exam }
      else
        format.html { render :new }
        format.json { render json: @user_exam.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /user_exams/1
  # PATCH/PUT /user_exams/1.json
  def update
    respond_to do |format|
      if @user_exam.update(user_exam_params)
        format.html { redirect_to @user_exam, notice: t('message.update')}
        format.json { render :show, status: :ok, location: @user_exam }
      else
        format.html { render :edit }
        format.json { render json: @user_exam.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /user_exams/1
  # DELETE /user_exams/1.json
  def destroy
    # @course = Course.find params[:course_id]
    @user_exam.destroy
    respond_to do |format|
      # format.html { redirect_to admin_school_course_exam_examinees_url(@course.school, @course, @user_exam.exam), notice: t('message.destroy')}
      format.html { redirect_back(fallback_location: root_path, notice: t('message.destroy')) }
      format.json { head :no_content }
    end
  end

  private
    def set_user_exams
      @user_exams = @user.user_exams.includes(:exam).published.finished.actual
      @user_exams = @user_exams.where(exam: @goal.exams) if @goal.present?
    end
    # Use callbacks to share common setup or constraints between actions.
    def set_user_exam
      @user_exam = UserExam.find(params[:id])
    end

    # Never trust parameters from the scary internet, only allow the white list through.
    def user_exam_params
      params.require(:user_exam).permit(:user_id, :exam_id, :corrects, :score, :finished_at, :enrollment_id, :school_enrollment_id)
    end

    def get_lists_score
      lists = List.where(school_id: @school.id).roots
      lists_score = {}
      lists.each do |l|
        subtree = List.sort_by_ancestry(l.subtree).reverse
        subtree.each do |st|
          if st.has_children?
            sum_score = 0
            not_exam_list = 0
            st.children.each do |ci|
              if ci.get_exams(@user.id).count == 0
                not_exam_list += 1
              end
              sum_score += lists_score[ci.id]
            end
            if st.child_ids.size > not_exam_list
              lists_score[st.id] = sum_score/(st.child_ids.size - not_exam_list)
            else
              lists_score[st.id] = sum_score
            end
          else
            lists_score[st.id] = st.get_score(@user.id)
          end
        end
      end
      return lists_score
    end
end
