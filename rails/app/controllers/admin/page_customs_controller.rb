class Admin::PageCustomsController < Admin::ApplicationController
  load_and_authorize_resource :school
  before_action :school_onboarding_detect
  load_and_authorize_resource through: :school
  load_and_authorize_resource :except => [:create]
  layout 'admin_material'

  add_breadcrumb 'ページ', only: [:index, :update, :edit]

  def index
    if params[:name].present?
      @page_customs = @page_customs.filter_by_query(params[:name])
    end
    @page_customs = @page_customs.order("created_at desc").page(params[:page])
  end

  def edit
  end

  def new
    page_custom = @school.page_customs.create({title: "Empty", design_content: ""})
    redirect_to url_for([:edit, :admin, @school, page_custom]), notice: t("message.create")
  end

  def update
    update_params = page_custom_params
    if update_params[:published_at] != "0" && @page_custom.published_at.nil?
      update_params[:published_at] = Time.now
    elsif update_params[:published_at] == "0" && @page_custom.published_at
      update_params[:published_at] = nil
    else
      update_params[:published_at] = @page_custom.published_at
    end

    update_params[:parent_id] = nil if update_params[:parent_id].empty? || update_params[:parent_id] == "0"

    ActiveRecord::Base.transaction do
      @page_custom.update(update_params)
      page_template_id = params.permit(:page_template_id)[:page_template_id]
      # テンプレートリセット
      @page_custom.page_custom_page_templates.where(school_design_id: @school.school_design.id).destroy_all

      # Update new template
      if page_template_id.to_i > 0
        @page_custom.page_custom_page_templates.create({
          page_template_id: page_template_id,
          school_design_id: @school.school_design.id
        })
      end
    end

  end

  def destroy
    @page_custom.destroy
    respond_to do |format|
      format.html { redirect_to [:admin, @school, :page_customs], notice: t("message.destroy")}
      format.json { head :no_content }
    end
  end

  private
  def page_custom_params
    params.require(:page_custom).permit(
      :title,
      :slag_name,
      :published_at,
      :school_id,
      :image,
      :parent_id,
      :design_content,
      :is_thanks_page,
      :body_type,
      :ck_body,
      :description
    )
  end
end
