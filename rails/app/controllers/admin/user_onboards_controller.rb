class Admin::UserOnboardsController < Admin::ApplicationController
  load_and_authorize_resource :school
  load_and_authorize_resource through: :school
  before_action :school_onboarding_detect
  layout :layout_by_scope

  add_breadcrumb -> { '規約一覧' }, path: [:admin, :@school, :user_onboards]

  def index
  end

  def new
    @user_onboard = @school.user_onboards.new
  end

  def edit
    @step = params[:step] || "basic"
    @contact_form = @user_onboard.contact_form
  end

  def show
    if @user_onboard.contact_form.blank?
      redirect_to admin_school_user_onboards_url(@school), notice: 'オンボーディング設定がまだ完了していません'
    else
      @contact_form = @user_onboard.contact_form
      @user_contacts = @contact_form.user_contacts.not_draft.includes(:user_contact_values).order(created_at: :desc).page(params[:page])
      @input_kinds = @contact_form.input_kinds.where.not(kind: "button").pluck(:name).uniq
    end
  end

  def create
    @user_onboard = @school.user_onboards.build(user_onboard_params)
    @user_onboard.preview_code = rand(36**32).to_s(36)

    respond_to do |format|
      if @user_onboard.save

        contact_form = @school.contact_forms.new({
          name: "オンボーディング専用_#{@user_onboard.id}",
          process: {
            "form": false,
            "confirmation": false,
            "thank": false,
            "user_mail": false, 
            "manager_mail": false
          }
        })
        contact_form.target = @user_onboard
        contact_form.save!

        format.html { redirect_to edit_admin_school_user_onboard_path(@school, @user_onboard), notice: t('message.create')}
        format.json { render :edit, status: :created, location: @user_onboard }
      else
        format.html { render :new }
        format.json { render json: @user_onboard.errors, status: :unprocessable_entity }
      end
    end
  end

  def update
    respond_to do |format|
      if @user_onboard.update(user_onboard_params)
        format.html { redirect_to admin_school_user_onboard_path(@school, @user_onboard), notice: t('message.update')}
        format.json { render :edit, status: :ok, location: @user_onboard }
        format.js { render :update, status: :ok}
      else
        format.html { render :edit }
        format.json { render json: @user_onboard.errors, status: :unprocessable_entity }
      end
    end
  end

  def destroy
    @user_onboard.destroy
    respond_to do |format|
      format.html { redirect_to admin_school_user_onboards_url(@school), notice: t('message.destroy')}
      format.json { head :no_content }
    end
  end

  
  def active
    # Activedなら、Deactiveにします。
    if @user_onboard.actived
      @school.user_onboards.update_all(actived: false)
    else
      @school.user_onboards.update_all(actived: false)
      @user_onboard.update!(
        actived: true,
        actived_at: Time.now
      )
    end
    redirect_to admin_school_user_onboards_path(@school), notice: t('message.update')
  end

  def preview
    hash_name = case params[:step]
    when "questionnaire"
      'contact_form'
    when "course"
      'course'
    else
      'welcome'
    end
    redirect_to "#{classroom_path}/on-boarding?preview_code=#{@user_onboard.preview_code}##{hash_name}"
  end

  private

  def user_onboard_params
    params.require(:user_onboard).permit(:title, :welcome_body, :school_id)
  end
end
