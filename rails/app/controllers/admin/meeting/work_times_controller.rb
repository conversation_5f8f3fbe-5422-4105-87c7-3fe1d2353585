module Admin
  module Meeting
    class WorkTimesController < ApplicationController
      def index
        @month = params[:month].present? ? Date.parse(params[:month]) : Date.today
        @work_times = @teacher.meeting_work_times.from_today.order(created_at: :desc)
        @schedule = @teacher.meeting_schedule(@month, only_work_times: true)

        @work_time_circle = ::Meeting::WorkTime.circle.new
        @work_time_one_time = ::Meeting::WorkTime.one_time.new
      end

      def create
        @work_time = @teacher.meeting_work_times.new(work_time_params)
        if @work_time.save
          redirect_to admin_school_teacher_meeting_work_times_path, notice: 'Attendance was successfully created.'
        end
      end

      def update
        @work_time = @teacher.meeting_work_times.find(params[:id])
        if @work_time.update(work_time_params)
          redirect_to admin_school_teacher_meeting_work_times_path, notice: "Attendance update successfully"
        end
      end

      def destroy
        @work_time = @teacher.meeting_work_times.find_by(id: params[:id])
        @work_time&.destroy
        redirect_to admin_school_teacher_meeting_work_times_path, notice: "Attendance destroy successfully"
      end

      private

      def work_time_params
        params.require(:meeting_work_time)
              .permit(:work_type, :date, :start_time, :end_time, circle_wdays: [])
      end
    end
  end
end
