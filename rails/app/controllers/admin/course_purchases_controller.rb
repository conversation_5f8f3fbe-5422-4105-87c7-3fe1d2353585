class Admin::CoursePurchasesController < Admin::ApplicationController
  # :nocov:
  load_resource :school
  before_action :school_onboarding_detect
  authorize_resource :school, if: -> {@school.present?}
  load_and_authorize_resource through: :school, if: -> {@school.present?}
  load_resource :course
  load_resource :goal
  authorize_resource :goal, if: -> {@goal.present?}
  load_resource if: -> {current_user.is_admin? && @school.blank?}
  load_resource :user
  load_resource :catalog
  load_resource :premium_service
  layout "admin_material"

  add_breadcrumb -> { "講座一覧" }, path: [:admin, :@school, :courses]

  def index
    @purchases = CoursePurchase.where(course_id: @course.id, school_id: @school.id).order(created_at: :desc)
  end

  def show
  end

  def update
    respond_to do |format|
      if update_params[:status].to_i == CoursePurchase::STATUS_STOPED
        @course_purchase.delete_payment
      end
      if @course_purchase.update(update_params)
        format.html { redirect_back(fallback_location: root_path, notice: t('message.update'))}
        format.json { render :show, status: :ok, location: @course_purchase }
      else
        format.html { render :edit }
        format.json { render json: @course_purchase.errors, status: :unprocessable_entity }
      end
    end
  end

  def destroy
    @course_purchase.destroy
    redirect_back(fallback_location: root_path, notice: t('message.destroy'))
  end
  
  private

  def update_params
    params.require(:course_purchase).permit(:status)
  end
end
