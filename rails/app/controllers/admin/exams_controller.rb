require "nokogiri"

class Admin::ExamsController < Admin::ApplicationController
  include Examable

  load_resource_for_exams
  load_and_authorize_resource through: EXAM_SCOPES

  before_action :build_goal_exam, only: [:new], if: ->{ @goal.present? }
  before_action :build_lesson_exam, only: [:new], if: ->{ @lesson.present? }
  before_action :build_master_milestone_exam, only: [:new], if: ->{ @master_milestone.present? }
  before_action :load_list, only: [:new, :edit, :show, :create]

  add_breadcrumb_for_exams
  add_breadcrumb "編集", only: [:edit, :update]
  add_breadcrumb "新規テスト", only: [:new, :create]

  layout :admin_layout

  def index
    @managers = @school.school_exam.managers.includes(:user)
    per_page = params[:per_page] || Settings.paginations.default_perpage_new
    @search = params[:search]

    @list = List.find params[:list_id] if params[:list_id]
    if @list.present?
      @exams = @list.allExams(exam_type).includes(:reexams).order(created_at: :desc).page(params[:page]).per(per_page)
    else
      @exams = @exams.root.ransack({ name_or_description_cont: @search }).result(distinct: true)
      .where(target_user_id: nil)
      .where(exam_type: exam_type)
      .includes(:reexams).order(created_at: :desc).page(params[:page]).per(per_page)
    end

    # This version of MySQL doesn"t yet support "LIMIT & IN/ALL/ANY/SOME subquery"
    # So we have to pluck id
    @reexams_count = Exam.where(exam_root_id: @exams.map(&:id)).group(:exam_root_id).count
    @lists = @school.lists.roots

    # Get Exam Partner
    user_ids = @school.partner_roles["Exam"]
    @partners = @school.users.where(id: user_ids)

    respond_to do |format|
      format.html
      format.js
    end
  end

  def show
    @user_exams = @exam.user_exams.includes([:user, :exam]).finished.actual.order(score: :desc)
    @user_blocks = @school.user_blocks.distinct.joins(:user_block_users).where("user_block_users.user_id": @user_exams.map(&:user_id))
    @managers = @exam.managers
    @current_user_block_ids = current_user_block_ids
    unless @current_user_block_ids.blank?
      @user_ids = UserBlock.get_user_ids_by_ids(@current_user_block_ids)
      @user_exams = @user_exams.where(user_id: @user_ids)
    end

    @exam_average = @exam.average @user_ids
    @user_exams_count = @user_exams.size
    @qs = @exam.qs.includes(:skill_items, answer_items: :users)
    @total_answers = @exam.answer_items.includes(:users, :q, as: {user_exam: :user}).each_with_object({}) do |answer_item, detail|
      detail[answer_item.id] = answer_item.answers.select{ |a|
        !a.user_exam&.test_mode && a.user_exam&.finished_at.present? && (@user_ids.nil? || @user_ids.include?(a.user_exam.user_id))
      }
      .map{ |a| a.user_exam.user }
      .uniq
    end

    @question_points = @qs.map { |q|
      {
        point: q.correct_rate(@user_exams_count, @user_ids),
        question: q
      }
    }
    .sort_by {|q|
      [q[:point], q[:question].id]
    }

    @question_convert = @qs.map { |q|
      {
        question: q,
        textLine: q.header_title
      }
    }

    @user_ranks = @user_exams.limit(5)
    respond_to do |format|
      format.html
      format.xlsx do
        @qs = @exam.qs
        render "show", locals: { sheet_name: @exam.name }
      end
    end
  end

  def purchases
    @purchases = ExamPurchase.where(exam_id: @exam.id, school_id: @school.id).order(created_at: :desc)
  end
  
  def new
    # Examタイプ設定
    @exam.exam_type = exam_type
    @exam.time_limit = 20
    @coupons = @school.coupons.get_available_coupons
  end

  def edit
    @coupons = @school.coupons.get_available_coupons
    @exam_evaluations = @exam.auto_diagnosis? ? @exam.exam_evaluations.auto_diagnosis : @exam.exam_evaluations.normal
  end

  def comments
    @user_exams = @exam.user_exams
      .select("user_exams.id, user_exams.user_id, COUNT(comments.id) AS comment_count")
      .joins(:comments)
      .group("user_exams.id, user_exams.user_id")
      .having("comment_count > 0")

  end

  def create
    @exam.school_id = @school.id
    respond_to do |format|
      if @exam.save && @exam.add_manager(current_user)
        ExamEvaluation.create_default(@exam)

        @school.complete_onboarding_exam
        @school.save!
        format.html do
          if exam_params[:duplicate].present? || exam_params[:create_reexam].present?
            redirect_to({ action: :index }, { notice: "テストを複製しました" })
          else
            redirect_to({ controller: "qs", exam_id: @exam.id, action: :edit2 }, notice: t("message.create"))
          end
        end
        format.json { render :show, status: :created, location: @exam }
      else
        format.html { render :new }
        format.json { render json: {errors: @exam.errors.map{|field, errors| [field, @exam.errors.full_messages_for(field)]}.to_h}, status: :unprocessable_entity }
      end
    end
  end

  def update
    if exam_params[:list_exams_attributes].present?
      @exam.list_exams.destroy_all
    end

    update_params = exam_params.merge({
      schedule_published_at: schedule_published_at
    })

    @exam.attributes = update_params
    @exam.school_id = @school.id

    respond_to do |format|
      if @exam.save
        if params[:exam][:courses].present?
          params[:exam][:courses].each do |course|
            if course[:_destroy]&.to_i == 0
              @exam.exam_courses.find_or_create_by course_id: course[:id]
            elsif course[:_destroy]&.to_i == 1
              exam_course = @exam.exam_courses.find_by course_id: course[:id]
              exam_course.destroy if exam_course.present?
            end
          end
        end

        format.html { redirect_to({action: :show}, notice: t("message.update")) }
        format.js { }
        format.json { render json: @exam.to_json, status: :ok }
      else
        format.html { render :edit }
        format.js { render :update_mail_failed }
        format.json { render json: {errors: @exam.errors.map{|field, errors| [field, @exam.errors.full_messages_for(field)]}.to_h}, status: :unprocessable_entity }
      end
    end
  end

  def destroy
    if params[:destroy_all]
      @exam.destroy
    else
      @context.remove_exam @exam
    end
    respond_to do |format|
      format.html { redirect_back fallback_location: {action: :index}, notice: t("message.destroy") }
      format.json { head :no_content }
    end
  end

  def update_published
    published_at = @exam.published? ? nil : Time.zone.now
    @exam.update(published_at: published_at)
    respond_to :js
  end

  def reexams
    @exams = @exam.reexams.order(:depth)
    respond_to :js
  end

  private
  def load_list
    @lists = @school.lists.roots || []
  end

  # Never trust parameters from the scary internet, only allow the white list through.
  def exam_params
    params.require(:exam).permit(:lesson_id, :course_id, :name, :body, :teacher_id, :feature_body, :time_limit, :image, :image_cache, :public_type, :user_exam_type, :price, :director_id, :term_id,
      :master_milestone_id, :published, :detect_cheating, :can_retest, :can_recommendation, :hide_result, :duplicate_goal_id, :description, :ck_body, :body_type,
      :disable_setting_average_score, :average_score_title, :done_score_title, :mail_title, :mail_body, :send_mail_target, :coupon_id,
      :difficult_rate, :pass_score, :duplicate, :reexam_id, :create_reexam, :auto_init_reexam, :check_result_option, :exam_type, :use_embed, :use_exam_evaluation,
      :auto_diagnosis_enabled,
      lesson_exams_attributes: [:id, :lesson_id], master_milestone_exams_attributes: [:id, :master_milestone_id], 
      goal_exams_attributes: [:id, :goal_id], list_exams_attributes: [:id, :list_id], summaries: [list: [:title, :content]]
    )
  end

  def build_goal_exam
    @exam.goal_exams.build goal_id: @goal.id unless @exam.goal_exams.where(goal_id: @goal.id).exists?
  end

  def build_lesson_exam
    @exam.lesson_exams.build lesson_id: @lesson.id
  end

  def build_master_milestone_exam
    @exam.master_milestone_exams.build master_milestone_id: @master_milestone.id
  end

  def exam_type
    @exam_type = params[:type] || Exam::TYPE_NORMAL_EXAM
  end

  def schedule_published_at
    schedule_published = params.permit(:schedule_published_date, :schedule_published_time)
    return nil unless schedule_published[:schedule_published_date].present? && schedule_published[:schedule_published_time].present?

    Time.zone.parse("#{schedule_published[:schedule_published_date]} #{schedule_published[:schedule_published_time]}")
  end

  def admin_layout
    case action_name
    when "index", "new"
      "admin_material"
    else
      "admin_exam_material"
    end
  end

  def current_user_block_ids
    result = params[:current_user_blocks]
    return [] if result.nil?
    return [result] if !result.is_a? Array
    result
  end
end
