class Admin::ListsController < Admin::ApplicationController
  load_resource :school
  before_action :ensure_list_enabled
  before_action :school_onboarding_detect
  load_and_authorize_resource through: :school

  add_breadcrumb "テストカテゴリ", only: [:index]

  def index
    @lists = @school.lists.roots.ordered_by_position
  end

  def create
    @list = @school.lists.build(list_params)
    respond_to do |format|
      if @list.save
        @lists = @school.lists.roots.ordered_by_position
        format.json { render :create, status: :ok, location: @list }
        format.js { render :create, status: :ok }
      else
        format.json { render json: @list.errors, status: :unprocessable_entity }
        format.js { render :create_error, status: :unprocessable_entity }
      end
    end
  end

  def update
    respond_to do |format|
      if @list.update(list_params)
        @lists = @school.lists.roots.ordered_by_position
        format.json { render :update, status: :ok, location: @list }
        format.js { render :update, status: :ok }
      else
        format.json { render json: @list.errors, status: :unprocessable_entity }
        format.js { render :update_error, status: :unprocessable_entity }
      end
    end
  end

  def edit
  end

  def destroy
    notice = @list.destroy ? I18n.t("lists.deleted") : @list.errors.full_messages.join("\n")
    redirect_to [:admin, @school, :lists], notice: notice
  end

  def reorder
    parent_id = params[:parent_id]
    ordered_ids = params[:ordered_ids] # => array of IDs in new order
  
    ActiveRecord::Base.transaction do
      ordered_ids.each_with_index do |id, index|
        list = List.find(id)
        list.update!(position: index + 1)
      end
    end
  
    render json: { success: true }
  rescue => e
    render json: { error: e.message }, status: :unprocessable_entity
  end

  private

  def list_params
    params.require(:list).permit(:id, :name, :description, :parent_id)
  end

  def ensure_list_enabled
    unless @school.use_lists?
      render plain: "このページにアクセスする権限がありません。" and return
    end
  end
end
