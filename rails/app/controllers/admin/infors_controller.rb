class Admin::InforsController < Admin::ApplicationController
  load_and_authorize_resource :school
  before_action :school_onboarding_detect
  load_and_authorize_resource through: :school
  layout "admin_material"

  add_breadcrumb -> { "お知らせ一覧" }, path: [:admin, :@school, :infors]
  add_breadcrumb -> { @infor.title }, only: [:edit, :update, :show]

  def index
    @infors = @school.infors.order(created_at: "desc")
  end

  def show
  end

  def new
  end

  def edit
  end

  def create
    @infor.save!
    redirect_to [:admin, @school, @infor], notice: t('message.create')
  end

  def update
    @infor.disabled_notify = true if params[:commit] == "いいえ"

    if params[:infor][:image_cache] == "delete"
      @infor.remove_image!
    end
    respond_to do |format|
      if @infor.update(infor_params)
        if infor_params[:show_dashboard]
          # 他のお知らせはのダッシュボードの表示はfalseにします。
          @school.infors.where.not(id: @infor.id).update_all(show_dashboard: false)
        end
        
        if params["infor"]["is_upload_file"] == "true"
          format.html { render :edit }
        else
          format.html { redirect_to [:admin, @school, :infors], notice: t('message.update')}
          format.json { render :edit, status: :updated, location: @infor }
          format.js
        end
      else
        format.html { render :edit }
        format.json { render json: @infor.errors, status: :unprocessable_entity }
        format.js
      end
    end
  end

  def destroy
    @infor.destroy
    respond_to do |format|
      format.html { redirect_to [:admin, @school, :infors], notice: t('message.destroy')}
      format.json { head :no_content }
    end
  end

  private
  def infor_params
    params.require(:infor).permit(:title, :content, :published_at, :published, :image, :show_dashboard)
  end
end
