class SubscriptionsController < ApplicationController
  include ActionView::Helpers::NumberHelper
  include SubscriptionsHelper
  include Response

  before_action :set_school
  before_action :set_subscription, only: [:show, :edit, :update, :destroy]
  before_action :set_premium_service
  before_action :set_secret_cached, only: [:show]
  before_action :check_private_access, only: :new
  # before_action :authenticate_user!
  before_action :redirect_to_register_if_not_logged_in
  skip_before_action :verify_authenticity_token

  layout "home_subschool"

  def index
    @subscriptions = Subscription.all
  end

  def show
  end

  def new
    return redirect_to "/classroom" if @premium_service.each_month? && current_user.check_active_premium_service(@premium_service)
    @subscription = Subscription.new
    @school = @premium_service.school
    if @premium_service.life_time? && @premium_service.price_plans.present?
      @price_plan = @premium_service.price_plans.first
      @premium_service.price = @price_plan.price
    end

    liquid_data = {}
    @page_custom_css = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_css(:premium_list, liquid_data, params[:revision_id])
    @page_custom_header = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_header(:premium_list, liquid_data, params[:revision_id])
    @page_custom_footer = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_footer(liquid_data, params[:revision_id])

    liquid_subscription_data = {
      "premium_service" => LiquidRender::RenderModel.render_data_one(@premium_service),
      "ps" => {
        "name" => @premium_service.name,
        "type" => @premium_service.purchase_type_name,
        "trial_charge_price_without_tax" => "<span class='trial-charge-price-without-tax'>#{get_trial_price(@premium_service)}</span>",
        "trial_charge_tax" => "<span class='trial-charge-tax'>#{get_trial_tax(@premium_service)}</span>",
        "trial_charge_price_with_tax" => "<span class='trial-charge-price-with-tax'>#{get_trial_price_with_tax(@premium_service)}</span>",
        "charge_price_without_tax" => "<span class='charge-price-without-tax'>#{extract_number(get_purchase_price(@premium_service))}</span>",
        "charge_tax" => "<span class='charge-tax'>#{get_tax(@premium_service)}</span>",
        "charge_price_with_tax" => "<span class='charge-price-with-tax'>#{extract_number(get_price_with_tax(@premium_service))}</span>",
        "form" => render_to_string(partial: 'subscriptions/templates/form_template', locals: { premium_service: @premium_service, school: @school, subscription: @subscription, price_plan: @price_plan }),
        "stripe" => render_to_string(partial: 'subscriptions/templates/stripe_js', locals: { premium_service: @premium_service, school: @school, subscription: @subscription, price_plan: @price_plan }),
        "membership_fee_with_tax" => "<span class='charge-membership-fee-with-tax'>#{@premium_service.get_membership_fee_with_tax}</span>",
        "membership_fee_with_tax_val" => @premium_service.get_membership_fee_with_tax,
        "membership_fee" => "<span class='charge-membership-fee'>#{@premium_service.get_membership_fee}</span>",
      },
    }

    @page_custom_subscription_html = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_html(:custom_page_subscriptions_new, liquid_subscription_data, user_signed_in?, params[:revision_id])
    @page_custom_subscription_css = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_css(:custom_page_subscriptions_new, liquid_subscription_data, params[:revision_id])
    @page_custom_subscription_header = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_header(:custom_page_subscriptions_new, liquid_subscription_data, params[:revision_id])
    @new_default = render_to_string(partial: 'subscriptions/templates/new_default', locals: { premium_service: @premium_service, school: @school, subscription: @subscription, price_plan: @price_plan })
    if @page_custom_subscription_html.blank?
      @page_custom_subscription_html = @new_default
      @default_html = true
    end
  end

  def edit
  end

  def create
    discount_price = 0
    has_discount = false

    price_plan_id = params[:price_plan_id]
    if @premium_service.life_time? && price_plan_id.nil?
      throw "premium_service is life_time but not set price_plan_id"
    end

    if @premium_service.each_month?
      purchase_price = @premium_service.get_purchase_price
      installment_count_total = 1
    else
      purchase_price, _type = @premium_service.get_purchase_price_with_price_plan(price_plan_id)
      if @premium_service.get_coupon(@current_user, params["coupon_code_value"])
        if @premium_service.coupon.cash?
          discount_price = @premium_service.coupon.discount_price
        elsif @premium_service.coupon.rate?
          discount_price = purchase_price * @premium_service.coupon.discount_rate/100
        end
        has_discount = true
        purchase_price = purchase_price - discount_price
      end

      installment_count_total = params[:installment_count_value].present? ? params[:installment_count_value].to_i : 1
      if installment_count_total > 1 && installment_count_total >  @premium_service.installment_count
        raise ActionController::BadRequest.new('installment_count invalid')
      end
      purchase_price = purchase_price / installment_count_total
    end

    @subscription = if params[:action_type] == "change_card"
                      Subscription.find(params[:change_from_subscription_id])
                    else
                      @premium_service.subscriptions.build(
                        subscription_params.merge(
                          purchase_price: purchase_price,
                          installment_count_total: installment_count_total,
                          user: current_user,
                          school_id: @premium_service.school_id
                        )
                      )
                    end

    if @premium_service.life_time?
      price_plan = @premium_service.price_plans.find(price_plan_id)
      expired_at = DateTime.now + price_plan.duration
      @subscription.expired_at = expired_at
    end

    respond_to do |format|
      if @subscription.save
        # サブスクリプション作成する
        if has_discount
          UserCoupon.create(user_id: @current_user.id, coupon_id: @premium_service.coupon.id,
                            target_id: @premium_service.id, discount_before_price: purchase_price + discount_price,
                            discount_after_price: purchase_price,
                            target_type: @premium_service.class.name,
                            school_id: @premium_service.school_id
          )
        end
        set_affiliate(@premium_service, Affiliate.action_names["purchased"], purchase_price)

        payment_intent_result = if params[:action_type] == "change_card"
          payment_method_result = set_payment_method(params, @subscription)

          if payment_method_result["error"].present?
            format.json { render json: { error: payment_method_result["error"] }, status: :unprocessable_entity }
            next
          end

          payment_method_result
        else
          # Normal payment flow
          @subscription.create_payment(
            params[:premium_service_id],
            params[:stripeEmail],
            params[:payment_method_id],
            params[:action_type],
            purchase_price,
            @subscription.name,
            price_plan_id,
            change_from_subscription_id: params[:change_from_subscription_id]
          )
        end

        format.json { render json: {
          client_secret: payment_intent_result["client_secret"],
          intent_type: payment_intent_result["intent_type"],
          subscription_id: @subscription.id,
          data: {
            payment_status: payment_intent_result["payment_status"],
            client_secret: payment_intent_result["client_secret"],
            payment_method_id: payment_intent_result["payment_method_val"],
            intent_type: payment_intent_result["intent_type"],
            subscription_stripe: payment_intent_result["subscription_stripe"]
          },
          status: StatusCodeApi::SUCCESS
        }}
      else
        format.html { render :new }
        format.json {
          json_response({}, status: StatusCodeApi::GET_ERROR)
        }
      end
    end
  end

  def update
    respond_to do |format|
      if @subscription.update(subscription_params)
        format.html { redirect_to @subscription, notice: t('message.update') }
        format.json { render :show, status: :ok, location: @subscription }
      else
        format.html { render :edit }
        format.json { render json: @subscription.errors, status: :unprocessable_entity }
      end
    end
  end

  def destroy
    @subscription.destroy
    respond_to do |format|
      format.html { redirect_to subscriptions_url, notice: t('message.destroy') }
      format.json { head :no_content }
    end
  end

  def get_coupon
    result = {
      "success": false
    }

    premium_service = PremiumService.find_by(id: params["premium_service_id"])
    current_price = 0
    if params["plan_id"].to_i > 0
      current_price = premium_service.get_purchase_price_with_price_plan(params["plan_id"].to_i)[0]
    else
      current_price = premium_service.get_purchase_price
    end
    if premium_service.life_time? && premium_service.get_coupon(@current_user, params["coupon_code"])
      result["success"] = true
      result["discount_type"] = premium_service.coupon.discount_type
      result["discount_price"] = premium_service.coupon.discount_price
      result["discount_rate"] = premium_service.coupon.discount_rate
      result["previous_price"] = current_price
    end
    return render json: result
  end

  def set_payment_method(params, subscription)
    begin
      subscription_before = params[:change_from_subscription_id] ? @current_user.subscriptions.find(params[:change_from_subscription_id]) : nil
      subscription.update(
        stripe_customer_id: subscription_before.stripe_customer_id,
        stripe_subscription_id: subscription_before.stripe_subscription_id,
      )

      subscription.save

      begin
        Stripe::PaymentMethod.attach(
          params[:payment_method_id],
          { customer: subscription_before.stripe_customer_id }
        )
      rescue Stripe::InvalidRequestError => e
        raise unless e.message.include?('already been attached')
      end

      # Logic set default payment method
      customer = Stripe::Customer.retrieve(subscription_before.stripe_customer_id)
      customer.invoice_settings = {
        default_payment_method: params[:payment_method_id]
      }
      customer.save

      setup_intent = Stripe::SetupIntent.create({
        customer: subscription_before.stripe_customer_id,
        payment_method: params[:payment_method_id],
        payment_method_types: ['card'],
        usage: 'off_session',
        confirm: false
      })

      subscription_before.get_stripe_card_information # reload before card information

      return {
        "client_secret" => setup_intent.client_secret,
        "intent_type" => "setup",
        "subscription_id" => subscription.id,
      }
    rescue => e
      Rails.logger.error("Error in set_payment_method: #{e.message}")
      Rails.logger.error(e.backtrace.join("\n"))

      return {
        "error" => e.message,
        "error_type" => "payment_method_error"
      }
    end
  end

  def save_payment_method
    @premium_service = PremiumService.find(params[:premium_service_id])

    if params[:subscription_id].present?
      @subscription = @premium_service.subscriptions.find_by(id: params[:subscription_id], user_id: @current_user.id)
    else
      @subscription = @premium_service.subscriptions.where(status: Subscription::STATUS_STOPED).find_by(user_id: @current_user.id).last
    end

    return render json: { "success": false, "error": "Subscription not found" } if @subscription.blank?

    @subscription_stripe = Stripe::Subscription.retrieve(@subscription.stripe_subscription_id)
    @subscription_stripe.default_payment_method = params[:payment_method]
    # @subscription_stripe.payment_settings.save_default_payment_method = "on_subscription"
    @subscription_stripe.save

    @customer = Stripe::Customer.retrieve(@subscription.stripe_customer_id)
    @customer.invoice_settings = {
      default_payment_method: params[:payment_method]
    }
    @customer.save

    render json: { "success": true }
  rescue => e
    Rails.logger.error("save_payment_method error: #{e.message}")
    return render json: { "success": false, "error": e.message }
  end

  def calculate_purchase_total
    return render json: { "success": false, "error": "Invalid premium service id" } if params[:premium_service_id_verify].to_s != @premium_service.id.to_s

    purchase_total = @premium_service.get_purchase_price
    purchase_total = @premium_service.get_purchase_price_with_price_plan(params[:price_plan_id].to_i)[0] if params[:price_plan_id].present?
    purchase_total = purchase_total / params[:installment_count_value].to_i if params[:installment_count_value].present?

    if @premium_service.each_month? && @premium_service.trial_flag?
      if @premium_service.trial_days? || (@premium_service.trial_end_date? && (@premium_service.trial_end_date < Date.current))
        purchase_total = @premium_service.get_purchase_trial_price
      end
    else
      if params[:coupon_code_value].present?
        coupon = @premium_service.get_coupon(@current_user, params[:coupon_code_value])
        if coupon
          if coupon.cash?
            discount_price = coupon.discount_price
          elsif coupon.rate?
            discount_price = purchase_total * coupon.discount_rate/100
          end
          purchase_total = purchase_total - discount_price
        end
      end
    end

    # add membership_fee if premium_service didnt set trial
    if @premium_service.get_membership_fee_with_tax > 0 && !@premium_service.trial_flag?
      purchase_total += @premium_service.get_membership_fee_with_tax
    end

    result = {
      "success": true,
      "purchase_total": purchase_total,
      "premium_service_id": @premium_service.id
    }

    return render json: result
  rescue => e
    return render json: { "success": false, "error": e.message }
  end

  def check_private_access
    return render_404 unless @premium_service
    return if @premium_service.published?
    return if params[:preview_code] # todo: remove this line

    secrets = params[:secret].present? ? [params[:secret]] : []
    secrets_cached = cookies[:premium_service_secrets] ? cookies[:premium_service_secrets].split(",") : []

    secrets = secrets | secrets_cached

    return if secrets.include?(@premium_service.access_token)

    render_404
  end

  def render_404
    respond_to do |format|
      format.html { render file: "#{Rails.root}/public/404.html", status: :not_found, layout: false }
    end
  end

  private

  def set_premium_service
    @premium_service = PremiumService.find params[:premium_service_id]
  end

  def set_subscription
    @subscription = Subscription.find(params[:id])
  end

  def subscription_params
    params.require(:subscription).permit(:user_id, :premium_service_id, :name, :purchase_price, :goal_id)
  end

  def set_secret_cached
    return if params[:secret].blank?

    data = (cookies[:premium_service_secrets] || "").split(",") | [params[:secret]]

    cookies[:premium_service_secrets] = data.join(",")
  end

  def extract_number(text)
    text.gsub(/[^\d,]/, "")
  end
end
