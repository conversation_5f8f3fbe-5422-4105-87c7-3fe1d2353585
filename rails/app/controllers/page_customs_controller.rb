class PageCustomsController < ApplicationController
  include <PERSON><PERSON>elper
  before_action :set_school
  before_action :set_page_custom, only: :show
  impressionist action: [:show]
  layout "home_subschool"

  def index
    @pages = @school.page_customs.published.order(created_at: :desc).page(params[:page]).per(10)


    @search = (params[:search] || '').to_s
    if @search.present?
      @pages = @pages.where("page_customs.title like ? or page_customs.design_content like ? or page_customs.ck_body like ?", "%#{@search}%", "%#{@search}%", "%#{@search}%")
    end

    liquid_data = {
      "pages" => LiquidRender::RenderModel.render_data_list(@pages) do |page|
          LiquidRender::RenderModel.render_data_one(page).merge(
            {
              "image" => page.image.present? ? page.image.url.to_s : nil,
              "title" => page.title,
              "description" => page.description
            }
          )
        end,
      "search" => @search,
      "page_query" => "&search=#{@search}"
    }

    @page_custom_css = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_css(:custom_page_list, liquid_data, params[:revision_id])
    @page_custom_header = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_header(:custom_page_list, liquid_data, params[:revision_id])
    @page_custom_footer = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_footer(liquid_data, params[:revision_id])
    @page_custom_html = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_html(:custom_page_list, liquid_data, @school, user_signed_in?, params[:revision_id])
  end

  def show
    page_template = @page_custom.page_template(SchoolManager.main_school.school_design)
    design_content = page_template ? page_template.design_content : SchoolManager.main_school.school_design.default_page_template
    if (params.keys - ["controller","action","id"]).any? || @page_custom.is_thanks_page
      unless params["preview"] == "true"
        return redirect_to root_path unless @page_custom.published_at.present?
        if @page_custom.is_thanks_page
          return redirect_to root_path unless params[:user_contact_uuid]
          uc = UserContact.find_by uuid: params["user_contact_uuid"]
          return redirect_to root_path unless uc
        end
      end
      body = ""
      if params["preview"].to_s == "true"
        if @page_custom.confirm_contact_id
          body = UtilityHelper.contact_page_preview(@page_custom.contact_form.confirmation_content).html_safe
        elsif @page_custom.preview_contact_form_page_id
          body = UtilityHelper.markdown_to_html(@page_custom.active_design_content).html_safe
        else
          body = UtilityHelper.contact_page_preview(@page_custom.active_design_content).html_safe
        end
      else
        SchoolManager.update_impression
        body =UtilityHelper.markdown_to_html(@page_custom.active_design_content, user_contact_uuid: params["user_contact_uuid"]).html_safe
      end
      liquid_data = get_liquid_data body

      @page_custom_css = PageTemplate.get_page_custom_css(design_content, SchoolManager.main_school.school_design, liquid_data)
      @page_custom_header = PageTemplate.get_page_custom_header(design_content, SchoolManager.main_school.school_design, liquid_data)
      @page_custom_footer = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_footer(liquid_data)
      @page_custom_html = PageTemplate.get_page_custom_html(design_content, SchoolManager.main_school.school_design, liquid_data)
    else
      SchoolManager.update_impression
      cache_data = Rails.cache.read("pages-#{@page_custom.id}-html")
      if cache_data && params["preview_code"].nil?
        @page_custom_html = cache_data
        @page_custom_css = Rails.cache.read("pages-#{@page_custom.id}-css")
        @page_custom_header = Rails.cache.read("pages-#{@page_custom.id}-header")
        @page_custom_footer = Rails.cache.read("pages-#{@page_custom.id}-footer")
      else
        body = UtilityHelper.markdown_to_html(@page_custom.active_design_content).html_safe
        liquid_data = get_liquid_data body
        @page_custom_css = PageTemplate.get_page_custom_css(design_content, SchoolManager.main_school.school_design, liquid_data)
        @page_custom_header = PageTemplate.get_page_custom_header(design_content, SchoolManager.main_school.school_design, liquid_data)
        @page_custom_footer = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_footer(liquid_data)
        @page_custom_html = PageTemplate.get_page_custom_html(design_content, SchoolManager.main_school.school_design, liquid_data)
        if params["preview_code"].nil?
          Rails.cache.write("pages-#{@page_custom.id}-html", @page_custom_html)
          Rails.cache.write("pages-#{@page_custom.id}-css", @page_custom_css)
          Rails.cache.write("pages-#{@page_custom.id}-header", @page_custom_header)
          Rails.cache.write("pages-#{@page_custom.id}-footer", @page_custom_footer)
        end
      end
    end
  end

  def set_page_custom
    @page_custom = SchoolManager.page_customs.find_with_slag(params[:id]).first
    not_found if @page_custom.nil?
  end

  def get_liquid_data body
    {
      "title" => @page_custom.title,
      "description" => @page_custom.description,
      "inquiry_form" => render_form_inquiry,
      "image" => {
        "url" => @page_custom.image&.url
      },
      "body" => body,
      "link" => page_link(@page_custom),
      "children" => @page_custom.children.published.map do | item |
        {
          "title" => item.title,
          "link" => page_link(item)
        }
      end,
      "siblings" => @page_custom.siblings.published.map do | item |
        {
          "title" => item.title,
          "link" => page_link(item)
        }
      end,
      "parent" => {
          "title" => @page_custom.parent ? @page_custom.parent.title : "",
          "link" => @page_custom.parent ? page_link(@page_custom.parent) : ""
        }
    }
  end
end
