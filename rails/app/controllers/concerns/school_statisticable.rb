module SchoolStatisticable
  extend ActiveSupport::Concern

  def set_duration
    @duration = [Time.zone.now - 30.days, Time.zone.now]
    @duration = params[:duration].split(" - ").map{ | item | Time.zone.parse(item) } if params[:duration].present?
    if params[:compare_duration] && params[:compare_duration] == "previous_period"
      @duration = [@duration[0] - 30.days, @duration[1] - 30.days]
    elsif params[:compare_duration] && params[:compare_duration] == "last_year"
      @duration = [@duration[0] - 1.year, @duration[1] - 1.year]
    end
  end

  def get_learning_info
    return @learning_info_d if @learning_info_d.present?

    school_statistics = {}
    filter_condition = "#{UtilityHelper.convert_tz_sql("created_at")} >= '#{@duration[0].strftime('%Y-%m-%d')}' AND #{UtilityHelper.convert_tz_sql("created_at")} <= '#{@duration[1].strftime('%Y-%m-%d')}'"
    filter_condition += " AND school_id = #{@school.id}"
    school_statistics_in_range = get_learning_info_by_filter(filter_condition)
    school_statistics[:learned_times_series_in_range] = school_statistics_in_range[:learned_times_series] 

    count = 0
    for item in school_statistics_in_range[:learned_times_series] do
      count += item[1]
    end

    school_statistics[:learned_times_count] = count.to_f.round(1)
    
    @learning_info_d = school_statistics
  end

  def get_learning_info_by_filter(filter_condition, is_get_times_series = true)
    school_statistics = {}
    temporary_table_name = "tl_#{Time.zone.now.to_i}_#{Random.rand(100)}"
    connection = ActiveRecord::Base.connection    
    begin
      connection.execute "DROP TEMPORARY TABLE IF EXISTS `#{temporary_table_name}`"
      connection.execute <<-eos
        CREATE TEMPORARY TABLE `#{temporary_table_name}` (`created_at` DATETIME, `recorded_value` INT DEFAULT 0);
      eos
      connection.execute <<-eos
        INSERT INTO `#{temporary_table_name}`( created_at, recorded_value)
        SELECT #{UtilityHelper.convert_tz_sql("created_at")} as created_at, SUM(recorded_value) as recorded_value
        FROM (
          SELECT created_at, recorded_value
          FROM tracking_exams
          WHERE #{filter_condition}
          UNION ALL
          SELECT created_at, recorded_value
          FROM tracking_lessons
          WHERE #{filter_condition}
        ) t
        GROUP BY #{UtilityHelper.convert_tz_sql("created_at")}
      eos

      if is_get_times_series
        school_statistics[:learned_times_series] = connection.select_rows("SELECT created_at, CAST(recorded_value AS decimal)/3600 AS recorded_value FROM #{temporary_table_name} ORDER BY created_at")
        school_statistics[:learned_times_series].map! do |time, value|
          [time.to_date, (value / user_login_in_day(time.to_date)).to_f.round(2)]
        end
      end
    ensure
      connection.execute("DROP TEMPORARY TABLE IF EXISTS #{temporary_table_name}")
    end
    school_statistics
  end

  def get_lessons_info
    return @lessons_info_d if @lessons_info_d.present?

    result = {}
    enrollment_lessons = EnrollmentLesson.where('lesson_id IN (?)', lesson_ids).where("#{UtilityHelper.convert_tz_sql("created_at")} >= '#{@duration[0].strftime('%Y-%m-%d')}' AND #{UtilityHelper.convert_tz_sql("created_at")} <= '#{@duration[1].strftime('%Y-%m-%d')}'")

    result[:finished_lessons_series] = enrollment_lessons.group("#{UtilityHelper.convert_tz_sql("created_at")}").count.to_a.map! do |time, value|
      [time.to_date, (value.to_f / user_login_in_day(time.to_date)).round(2)]
    end

    total_finished_lessons = 0
    for item in result[:finished_lessons_series] do
      total_finished_lessons += item[1]
    end
    
    result[:finished_lessons_count] = total_finished_lessons.round(2)

    @lessons_info_d = result
  end

  def get_activity_info
    return @activity_info_d if @activity_info_d.present?

    result = {}

    activities = Activity.where('school_id = ?', @school.id).where("#{UtilityHelper.convert_tz_sql("created_at")} >= '#{@duration[0].strftime('%Y-%m-%d')}' AND #{UtilityHelper.convert_tz_sql("created_at")} <= '#{@duration[1].strftime('%Y-%m-%d')}'")
    
    result[:total_in_range] = activities.count
    result[:activities_series] = activities.group("#{UtilityHelper.convert_tz_sql("created_at")}").count.to_a.map! do |time, value|
      [time.to_date, value]
    end
    login_data = activities.where(action: "login")
    result[:user_login_count] = login_data.count
    result[:user_login_series] = login_data.group("#{UtilityHelper.convert_tz_sql("created_at")}").select("COUNT(user_id) as user_count, #{UtilityHelper.convert_tz_sql("created_at")} AS created_at_tz").map do |item|
      [item.created_at_tz.to_date, item.user_count]
    end

    school_users = SchoolUser.where('school_id = ?', @school.id).where("#{UtilityHelper.convert_tz_sql("created_at")} >= '#{@duration[0].strftime('%Y-%m-%d')}' AND #{UtilityHelper.convert_tz_sql("created_at")} <= '#{@duration[1].strftime('%Y-%m-%d')}'")
    result[:user_signup_count] = school_users.count

    result[:user_signup_series] = school_users.group("#{UtilityHelper.convert_tz_sql("created_at")}").count.to_a.map! do |time, value|
      [time.to_date, value]
    end

    # result[:activity_in_day] = (result[:total_in_range].to_f/day_number_in_range).round(1)
    # result[:user_login_in_day] = (result[:user_login_count].to_f/day_number_in_range).round(1)
    # result[:user_signup_in_day] = (result[:user_signup_count].to_f/day_number_in_range).round(1)

    @activity_info_d = result
  end

  def user_login_in_day(date)
    user_login_series = get_activity_info[:user_login_series]

    for user_login_serie in user_login_series do
      return user_login_serie[1] if user_login_serie[0] == date
    end

    return 1
  end

  def get_inquiry_info
    return @inquiry_info_d if @inquiry_info_d.present?
    result = {}

    inquiries = @school.inquiries.where("#{UtilityHelper.convert_tz_sql("created_at")} >= '#{@duration[0].strftime('%Y-%m-%d')}' AND #{UtilityHelper.convert_tz_sql("created_at")} <= '#{@duration[1].strftime('%Y-%m-%d')}' AND is_spam = 0")
    user_contacts = @school.user_contacts.where("#{UtilityHelper.convert_tz_sql("created_at")} >= '#{@duration[0].strftime('%Y-%m-%d')}' AND #{UtilityHelper.convert_tz_sql("created_at")} <= '#{@duration[1].strftime('%Y-%m-%d')}' AND is_spam = 0")

    result[:total_in_range] = inquiries.count + user_contacts.count
    inquiries_series = inquiries.group("#{UtilityHelper.convert_tz_sql("created_at")}").count.to_a.map! do |time, value|
      [time.to_date, value]
    end

    user_contacts_series = user_contacts.group("#{UtilityHelper.convert_tz_sql("created_at")}").count.to_a.map! do |time, value|
      [time.to_date, value]
    end

    result[:inquiries_series] = (inquiries_series + user_contacts_series).group_by(&:first).map { |k, v| [k, v.map(&:last).inject(:+)] }

    @inquiry_info_d = result
  end

  def get_subscription_info
    return @subscription_info_d if @subscription_info_d.present?
    result = {}

    @subscriptions = @school.subscriptions.where('premium_service_id IN (?)', premium_service_ids).where("#{UtilityHelper.convert_tz_sql("created_at")} >= '#{@duration[0].strftime('%Y-%m-%d')}' AND #{UtilityHelper.convert_tz_sql("created_at")} <= '#{@duration[1].strftime('%Y-%m-%d')}'")

    result[:total_in_range] = @subscriptions.count
    result[:price_in_range] = @subscriptions.sum(:purchase_price)

    result[:price_service_series] = @subscriptions.group(:premium_service_id).select("premium_service_id, SUM(purchase_price) as price, count(purchase_price) as size").map do |item|
      [PremiumService.find(item.premium_service_id).name, item.price, item.size]
    end
    
    result[:price_series] = @subscriptions.group("#{UtilityHelper.convert_tz_sql("created_at")}").select("SUM(purchase_price) as price, #{UtilityHelper.convert_tz_sql("created_at")} AS created_at_tz").map do |item|
      [item.created_at_tz.to_date, item.price]
    end

    @subscription_info_d = result
  end

  def get_course_purchase_info
    return @course_purchase_info if @course_purchase_info.present?
    result = {}

    @course_purchase = @school.course_purchases.where("#{UtilityHelper.convert_tz_sql("created_at")} >= '#{@duration[0].strftime('%Y-%m-%d')}' AND #{UtilityHelper.convert_tz_sql("created_at")} <= '#{@duration[1].strftime('%Y-%m-%d')}'")

    result[:total_in_range] = @course_purchase.count
    result[:price_in_range] = @course_purchase.sum(:price)
    
    result[:price_series] = @course_purchase.group("#{UtilityHelper.convert_tz_sql("created_at")}").select("SUM(price) as price, #{UtilityHelper.convert_tz_sql("created_at")} AS created_at_tz").map do |item|
      [item.created_at_tz.to_date, item.price]
    end
    
    @course_purchase_info = result
  end

  def get_exam_purchase_info
    return @exam_purchase_info if @exam_purchase_info.present?
    result = {}

    @exam_purchase = @school.exam_purchases.where("#{UtilityHelper.convert_tz_sql("created_at")} >= '#{@duration[0].strftime('%Y-%m-%d')}' AND #{UtilityHelper.convert_tz_sql("created_at")} <= '#{@duration[1].strftime('%Y-%m-%d')} 23:59:00'")

    result[:total_in_range] = @exam_purchase.count
    result[:price_in_range] = @exam_purchase.sum(:price)
    
    result[:price_series] = @exam_purchase.group("#{UtilityHelper.convert_tz_sql("created_at")}").select("SUM(price) as price, #{UtilityHelper.convert_tz_sql("created_at")} AS created_at_tz").map do |item|
      [item.created_at_tz.to_date, item.price]
    end

    @exam_purchase_info = result
  end

  def get_sale_management_info params = nil
    return @sale_management_info if @sale_management_info.present?
    result = {}

    sale_managements = @school.present? ? @school.sale_managements : SaleManagement.all

    sale_managements = sale_managements.where("sale_managements.kind <> 'SchoolPlan'")
    
    if params[:search]
      @sale_managements = sale_managements.filter_by_query(params[:search])
    else   
      @sale_managements = sale_managements.where("#{UtilityHelper.convert_tz_sql("created_at")} >= '#{@duration[0].strftime('%Y-%m-%d')}' AND #{UtilityHelper.convert_tz_sql("created_at")} <= '#{@duration[1].strftime('%Y-%m-%d')} 23:59:00'")
    end
    
    result[:price_in_range] = @sale_managements.sum(:price)

    group_data = @sale_managements.group("#{UtilityHelper.convert_tz_sql("created_at")}")
      .select("SUM(price) as price, SUM(application_fee_amount) as application_fee_amount, #{UtilityHelper.convert_tz_sql("created_at")} AS created_at_tz")

    result[:price_series] = group_data.map do |item|
      [
        item.created_at_tz.to_date, 
        item.price,
      ]
    end
    result[:application_fee_amount_series] = group_data.map do |item|
      [
        item.created_at_tz.to_date, 
        item.application_fee_amount
      ]
    end

    @sale_management_info = result
  end

  def get_price_info
    return @price_info_d if @price_info_d.present?
    result = {}

    sale_managements = @school.present? ? @school.sale_managements : SaleManagement.all
    sale_managements = sale_managements.where("sale_managements.kind <> 'SchoolPlan'")
    sale_managements = sale_managements.where("#{UtilityHelper.convert_tz_sql("sale_managements.created_at")} >= '#{@duration[0].strftime('%Y-%m-%d')}' AND #{UtilityHelper.convert_tz_sql("sale_managements.created_at")} <= '#{@duration[1].strftime('%Y-%m-%d')} 23:59:00'")

    result[:total_in_range] = sale_managements.count
    result[:price_in_range] = sale_managements.sum(:price)

    result[:price_service_series] = sale_managements
      .joins("JOIN subscriptions on (subscriptions.id = sale_managements.subscription_id AND sale_managements.kind = 'PremiumService')")
      .joins("JOIN premium_services on subscriptions.premium_service_id = premium_services.id")
      .group("premium_services.id")
      .select("premium_services.id as premium_service_id, SUM(sale_managements.price) as price, count(sale_managements.price) as size").map do |item|
        [PremiumService.find(item.premium_service_id).name, item.price, item.size]
      end
    
    result[:subscription_series] = sale_managements.group("#{UtilityHelper.convert_tz_sql("created_at")}").count.to_a.map! do |time, value|
      [time.to_date, value]
    end

    result[:price_series] = sale_managements.group("#{UtilityHelper.convert_tz_sql("created_at")}").select("SUM(price) as price, #{UtilityHelper.convert_tz_sql("created_at")} AS created_at_tz").map do |item|
      [item.created_at_tz.to_date, item.price]
    end

    @price_info_d = result
  end

  def get_mail_info
    return @mail_info_d if @mail_info_d.present?
    result = {}

    announcement_mails = AnnouncementMail.get_by_school(@school)
    announcement_mails_ids = announcement_mails.select(:id).map{|h| h.id }

    user_opened_tracking_mails = UserOpenedTrackingMail.where('tracking_mail_id IN (?)', announcement_mails_ids).where("#{UtilityHelper.convert_tz_sql("created_at")} >= '#{@duration[0].strftime('%Y-%m-%d')}' AND #{UtilityHelper.convert_tz_sql("created_at")} <= '#{@duration[1].strftime('%Y-%m-%d')}'")
    delivered_tracking_mails = MailDeliveredTrackingMail.where('tracking_mail_id IN (?)', announcement_mails_ids).where("#{UtilityHelper.convert_tz_sql("created_at")} >= '#{@duration[0].strftime('%Y-%m-%d')}' AND #{UtilityHelper.convert_tz_sql("created_at")} <= '#{@duration[1].strftime('%Y-%m-%d')}'")
    user_visited_tracking_mails = UserVisitedTrackingMail.where('tracking_mail_id IN (?)', announcement_mails_ids).where("#{UtilityHelper.convert_tz_sql("created_at")} >= '#{@duration[0].strftime('%Y-%m-%d')}' AND #{UtilityHelper.convert_tz_sql("created_at")} <= '#{@duration[1].strftime('%Y-%m-%d')}'")

    result[:user_opened_tracking_mails_count] = user_opened_tracking_mails.count
    result[:delivered_tracking_mails_count] = delivered_tracking_mails.count
    result[:user_visited_tracking_mails_count] = user_visited_tracking_mails.count

    result[:user_opened_tracking_mails_rate] = (result[:user_opened_tracking_mails_count].to_f/user_count * 100).round(1)

    if result[:delivered_tracking_mails_count] == 0
      result[:complete_tracking_mails_rate] = 0
    else
      result[:complete_tracking_mails_rate] = (result[:user_visited_tracking_mails_count].to_f/result[:delivered_tracking_mails_count] * 100).round(1)
    end

    @mail_info_d = result
  end

  def get_impression_info
    return @impression_info_d if @impression_info_d.present?
    result = {}

    impressions = Impression.where('school_id = ?', @school.id).where("user_id is NULL").where("#{UtilityHelper.convert_tz_sql("created_at")} >= '#{@duration[0].strftime('%Y-%m-%d')}' AND #{UtilityHelper.convert_tz_sql("created_at")} <= '#{@duration[1].strftime('%Y-%m-%d')}'")
    result[:impressions_count] = impressions.count

    result[:impression_series] = impressions.group("#{UtilityHelper.convert_tz_sql("created_at")}").count.to_a.map! do |time, value|
      [time.to_date, value]
    end
    # result[:impressions_count_in_day] = (result[:impressions_count].to_f/day_number_in_range).round(1)

    @impression_info_d = result
  end

  def get_visitor_info
    return @visitor_info if @visitor_info.present?
    result = {}

    visitors = Ahoy::Visit.where('school_id = ?', @school.id).where("#{UtilityHelper.convert_tz_sql("started_at")} >= '#{@duration[0].strftime('%Y-%m-%d')}' AND #{UtilityHelper.convert_tz_sql("started_at")} <= '#{@duration[1].strftime('%Y-%m-%d')}'")
    result[:visitors_count] = visitors.count
    result[:uniq_visitors_count] = visitors.distinct.count(:ip)

    result[:visitor_series] = visitors.group("#{UtilityHelper.convert_tz_sql("started_at")}").count.to_a.map! do |time, value|
      [time.to_date, value]
    end

    arr = visitors.group("#{UtilityHelper.convert_tz_sql("started_at")}", :ip).count.to_a
    r = Hash.new(0)
    arr.each do |entry|
      date = entry[0][0]
      if r.key?(date)
        r[date] = r[date] + 1
      else
        r[date] = 1
      end
    end

    result[:uniq_visitors_series] = r.map { |date, count| [date, count] }

    # result[:uniq_visitors_series] = visitors.group("#{UtilityHelper.convert_tz_sql("started_at")}", :ip).count.to_a.map! do |time, value|
    #   [time[0].to_date, value]
    # end
    # result[:return_visitors_count] = 0
    # if visitors.count > 0
    #   visitors.each do |visitor| 
    #     result[:return_visitors_count] += 1 if visitor.events.pluck(:session_id).uniq.count > 1
    #   end
    # end
    @visitor_info = result
  end

  def get_course_info
    return @course_info_d if @course_info_d.present?
    result = {}

    enrollments = @school.enrollments.where("#{UtilityHelper.convert_tz_sql("enrollments.created_at")} >= '#{@duration[0].strftime('%Y-%m-%d')}' AND #{UtilityHelper.convert_tz_sql("enrollments.created_at")} <= '#{@duration[1].strftime('%Y-%m-%d')}'")
    result[:courses_count] = enrollments.count

    result[:courses_series] = enrollments.group("#{UtilityHelper.convert_tz_sql("enrollments.created_at")}").count.to_a.map! do |time, value|
      [time.to_date, value]
    end

    # result[:courses_count_in_day] = (result[:courses_count].to_f/day_number_in_range).round(1)

    # if get_activity_info[:user_login_in_day] > 0
    #   result[:courses_count_in_day] = (result[:courses_count_in_day].to_f/get_activity_info[:user_login_in_day]).round(1)
    # end

    @course_info_d = result
  end

  def get_exam_info
    return @exam_info if @exam_info.present?
    result = {}
    exam_ids = @school.exams.ids
    user_exams = UserExam.where(exam_id: exam_ids).where(("#{UtilityHelper.convert_tz_sql("created_at")} >= '#{@duration[0].strftime('%Y-%m-%d')}' AND #{UtilityHelper.convert_tz_sql("created_at")} <= '#{@duration[1].strftime('%Y-%m-%d')}'"))
    result[:user_exams_count] = user_exams.count

    result[:user_exams_series] = user_exams.group("#{UtilityHelper.convert_tz_sql("created_at")}").count.to_a.map! do |time, value|
      [time.to_date, value]
    end
    @exam_info = result
  end
  
  def get_banner_info
    return @banner_infor if @banner_infor.present?
    result = {}
    banner_ids = @school.banners.ids
    click_banners = BannerTracking.where("created_at >= '#{@duration[0].to_time.getutc.strftime('%Y-%m-%d')}' AND created_at <= '#{@duration[1].to_time.getutc.strftime('%Y-%m-%d')}'").where(banner_id: banner_ids)
    result[:banner_series] = click_banners.group("#{UtilityHelper.convert_tz_sql("created_at")}").count.to_a.map! do |time, value|
      [time.to_date, value]
    end
    result[:click_count] = click_banners.count

    @banner_infor = result
  end

  def get_block_email_info
    return @block_email_d if @block_email_d.present?
    result = {}

    block_emails = @school.block_emails.where("created_at >= '#{@duration[0].getutc.strftime('%Y-%m-%d')}' AND created_at <= '#{@duration[1].getutc.strftime('%Y-%m-%d')}'")
    result[:user_count] = block_emails.count

    @block_email_d = result
  end

  def user_count
    return 1 if @school.users.count == 0

    @school.users.count
  end

  def user_ids
    @school.users.map{|h| h.id }
  end

  def enrollment_ids
    @school.enrollments.map{|h| h.id }
  end

  def lesson_ids
    @school.lessons.map{|h| h.id }
  end

  def premium_service_ids
    @school.premium_services.map{|h| h.id }
  end

  def day_number_in_range
    number_range = ((@duration[1].to_date - @duration[0].to_date).to_i + 1).to_f
    return 1 if number_range == 0
    number_range
  end

  def list_purchase
    list = []
    
    @course_purchase.each do |cp|
      course = [cp.created_at, cp.user.name, cp.course.name, "買取", "", cp.price, ((Time.now - cp.user.created_at)/60/60/24).round(0), ((Time.now - cp.user.inquiries.last.created_at)/60/60/24).round(0), ((Time.now - cp.created_at)/60/60/24).round(0), Course.name]
      list << course
    end

    @exam_purchase.each do |ep|
      exam = [ep.created_at, ep.user.name, ep.exam.name, "買取", "", ep.price, ((Time.now - ep.user.created_at)/60/60/24).round(0), ((Time.now - ep.user.inquiries.last.created_at)/60/60/24).round(0), ((Time.now - ep.created_at)/60/60/24).round(0), Exam.name]
      list << exam
    end

    @subscriptions.each do |s|
      sub = [s.created_at, s.user.name, s.premium_service&.name, s.premium_service&.purchase_type, s.price_plan.name, s.purchase_price, ((Time.now - s.user.created_at)/60/60/24).round(0), ((Time.now - s.user.inquiries.last.created_at)/60/60/24).round(0), ((Time.now - s.created_at)/60/60/24).round(0), PremiumService.name]
      list << sub
    end
    list.sort! { |a, b|  a[0] <=> b[0] }
  end
end
