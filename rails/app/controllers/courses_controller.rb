class CoursesController < ApplicationController
  before_action :set_school
  before_action :set_course, only: :show
  impressionist action: [:index, :show]
  layout "home_subschool"
  include RenderTeacher
  include RenderCourse

  def index
    SchoolManager.update_impression
    @courses = SchoolManager.courses.published.page(params[:page])

    liquid_data = {}
    # In the index method, change this line:

    @catalog = Catalog.find params[:catalog_id] if params[:catalog_id]
    if @catalog.present?
      @courses = @catalog.courses.published
    else
      @courses = SchoolManager.courses.published
    end

    # search by list_ids. Ex: list_search=21,21,23
    if params[:list_search].present?
      matching_ids = get_courses_id_from_list(@courses)
      @courses = @courses.where(id: matching_ids)
      @avariable_list_ids = get_available_list(@courses).join(",")
    end

    @search = (params[:search] || '').to_s
    if @search.present?
      @courses = @courses.where("courses.name like ?", "%#{@search}%")
    end

    @course_tags = @courses.tag_counts_on(:tags)
    if params[:tag_name]
      @tag_name = params[:tag_name]
      @courses = @courses.tagged_with(params[:tag_name])
    end

    @type = params[:type]&.split(",")
    if @type.present?
      @courses = @courses.where(type_is: @type)
    end

    @rank = params[:rank]
    if @rank
      course_ids = @courses.select{|c| c.review_average_rank >= @rank.to_i}.pluck(:id)
      @courses = @courses.where(id: course_ids)
    end
    @courses_count = @courses.order(created_at: :desc).size
    @courses = @courses.order(type_is: :asc).page(params[:page]).per(params[:per_page] || 12)
    @tag_name = params[:tag_name]
    if @tag_name
      @course_tags = SchoolManager.courses.published.tag_counts_on(:tags)
    end

    @user_courses = current_user.courses if user_signed_in?
    @teachers = SchoolManager.teachers.published.order(averages_rate: :desc).first(4)
    @catalogs = @school.catalogs.published.order(order: :asc, created_at: :desc)

    user_ids = SchoolManager.main_school.partner_roles["Course"]
    @manager = SchoolManager.main_school.users.where(id: user_ids)&.first

    liquid_data = get_liquid_data.merge({
      "class" => default_class_for_course
    })

    @page_custom_css = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_css(:course_list, liquid_data, params[:revision_id])
    @page_custom_header = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_header(:course_list, liquid_data, params[:revision_id])
    @page_custom_footer = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_footer(liquid_data, params[:revision_id])
    @page_custom_html = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_html(:course_list, liquid_data, @school, user_signed_in?, params[:revision_id])
  end

  def filter_count
    courses = SchoolManager.courses.published

    if params[:catalog_id].present?
      catalog = Catalog.find_by(id: params[:catalog_id])
      courses = catalog&.courses&.published || courses
    end

    if params[:list_search].present?
      matching_ids = get_courses_id_from_list(courses)
      courses = courses.where(id: matching_ids)
    end

    if params[:search].present?
      keyword = params[:search].to_s
      courses = courses.where("courses.name LIKE ?", "%#{keyword}%")
    end

    if params[:tag_name].present?
      courses = courses.tagged_with(params[:tag_name])
    end

    if params[:type].present?
      courses = courses.where(type_is: params[:type]&.split(","))
    end

    if params[:rank].present?
      rank = params[:rank].to_i
      courses = courses.select { |c| c.review_average_rank >= rank }
    end
    
    avariable_list_ids = get_available_list courses
    render json: { count: courses.count, avariable_list_ids: avariable_list_ids.uniq }
  end

  def show
    set_affiliate @course, Affiliate.action_names["clicked"], 0
    SchoolManager.update_impression
    @course_lessons = @course.course_lessons.includes(:lesson).where.not(lessons: { published_at: nil }).rank(:row_order)
    @enrollment = Enrollment.find_by(user: current_user, course: @course) if user_signed_in?
    cache_data = Rails.cache.read("courses-#{@course.id}-html")

    if cache_data && params["preview_code"].nil?
      @page_custom_html = cache_data
      @page_custom_css = Rails.cache.read("courses-#{@course.id}-css")
      @page_custom_header = Rails.cache.read("courses-#{@course.id}-header")
      @page_custom_footer = Rails.cache.read("courses-#{@course.id}-footer")
    else
      ## get chapters
      @chapters = @course.chapters.order(:row_order)
      @reviews = @course.reviews.order(created_at: :desc)
      first_lesson = @course.lessons.published.where(is_previewable: true, lesson_type: :video).first

      liquid_data = {
        "course_lessons" => LiquidRender::RenderModel.render_data_list(@course_lessons),
        "chapters" => LiquidRender::RenderModel.render_data_list(@chapters),
        "reviews" => @reviews.map do | item |
          review_render(item)
        end,
        "learns" => LiquidRender::RenderModel.render_data_list(@course.learns),
        "targets" => LiquidRender::RenderModel.render_data_list(@course.targets),
        "prerequisits" => LiquidRender::RenderModel.render_data_list(@course.prerequisits),
        "review_average_rank" => @course.review_average_rank,
        "review_rank_distributions" => @course.review_rank_distributions,
        "chapter_list" => LiquidRender::RenderModel.render_data_list(@chapters) do | chapter |
          render_chapter(chapter)
        end,
        "enrollment" => LiquidRender::RenderModel.render_data_one(@enrollment),
        "course" => LiquidRender::RenderModel.render_data_one(@course, [:lessons_count, :level_name, :duration_text, :cut_price]).merge({
          "body" => (UtilityHelper.markdown_to_html @course.active_body).html_safe,
        }),
        "lessons" => LiquidRender::RenderModel.render_data_list(@course.lessons.published.order("course_lessons.row_order")) do | lesson |
          render_lesson(lesson)
        end,
        "previewable_lessons" => LiquidRender::RenderModel.render_data_list(@course.lessons.published.where(is_previewable: true, lesson_type: :video).order("course_lessons.row_order")) do | lesson |
          render_lesson(lesson)
        end,
        "first_lesson" => first_lesson ? render_lesson(first_lesson) : nil,
        "course_btn" => course_btn(@course, @enrollment),
        "teacher" => render_teacher(@course.teachers_with_rank.first),
        "teachers" => @course.teachers_with_rank.map do | teacher |
          render_teacher(teacher)
        end,
        "inquiry_form" => render_form_inquiry,
        "one_week_later" => Date.today.end_of_week.strftime("%-m月%-d日")
      }

      @page_custom_css = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_css(:course_show, liquid_data, params[:revision_id])
      @page_custom_header = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_header(:course_show, liquid_data, params[:revision_id])
      @page_custom_footer = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_footer(liquid_data, params[:revision_id])
      @page_custom_html = SchoolManager.main_school.school_design(params[:preview_code]).get_page_custom_html(:course_show, liquid_data, @school, user_signed_in?, params[:revision_id])
      if params["preview_code"].nil?
        Rails.cache.write("courses-#{@course.id}-html", @page_custom_html)
        Rails.cache.write("courses-#{@course.id}-css", @page_custom_css)
        Rails.cache.write("courses-#{@course.id}-header", @page_custom_header)
        Rails.cache.write("courses-#{@course.id}-footer", @page_custom_footer)
      end
    end
  end

  def get_recommendations
    @course = Course.find(params[:id])

    recommended_courses = ProductRecommend
                            .where(product_id: @course&.product&.id)
                            .map { |pr| Product.find_by(id: pr.recommended_product_id)&.productable }
                            .compact

    # Get user's purchased course IDs
    purchased_course_ids = current_user ? current_user.courses.pluck(:id) : []

    # Get cart product IDs
    cart_product_ids = []
    if @cart&.cart_items.present?
      cart_products = @cart.cart_items.includes(:product).map(&:product)
      cart_product_ids = cart_products
        .select { |p| p.productable_type == 'Course' }
        .map { |p| p.productable_id }
    end

    # Filter personalized recommendations:
    # 1. Remove current course
    # 2. Remove courses already in cart
    # 3. Remove courses already purchased
    if @course.is_display_product_recommend?
      filtered_recommended_courses = recommended_courses
        .uniq { |p| p.id }
        .reject { |p| p.id == @course.id || cart_product_ids.include?(p.id) || purchased_course_ids.include?(p.id) }
        .first(3)
    else
      filtered_recommended_courses = []
    end

    # If we have fewer than 3 recommendations, supplement with top products
    if filtered_recommended_courses.length < 3 && @course.use_product_recommend_user_and_system? && @course.is_display_product_recommend?
      needed_count = 3 - filtered_recommended_courses.length

      # Get IDs of courses we already have to exclude them
      existing_ids = filtered_recommended_courses.map(&:id)

      # Get top products, excluding current course, purchased courses, cart items, and already selected recommendations
      top_product_ids = OrderItem
        .group(:product_id)
        .order('SUM(quantity) DESC')
        .limit(10)
        .sum(:quantity)
        .keys

      # Get the course products from the top products
      additional_courses = Product
        .where(id: top_product_ids, productable_type: 'Course')
        .includes(:productable)
        .map(&:productable)
        .compact
        .uniq { |p| p.id }
        .reject do |p|
          p.id == @course.id ||
          cart_product_ids.include?(p.id) ||
          purchased_course_ids.include?(p.id) ||
          existing_ids.include?(p.id)
        end
        .first(needed_count)

      # Combine personalized and top product recommendations
      filtered_recommended_courses = filtered_recommended_courses + additional_courses
    end

    # Format the recommendations
    recommended_courses_data = filtered_recommended_courses.map do |c|
      {
        id: c.id,
        name: c.name,
        image: c.image_url,
        price: c.price,
        type: 'Course',
        teacher: c.teachers&.map { |t| "#{t&.name} | #{t&.catch}" }&.join(', ')
      }
    end

    respond_to do |format|
      format.json { render json: recommended_courses_data }
    end
  end

  private

  def set_course
    @course = SchoolManager.courses.find_with_id_slug(params[:id]).first

    raise ActiveRecord::RecordNotFound if @course.blank?
  end

  def get_liquid_data
    @lists = @school.lists.roots || []

    page_query = ""
    if @catalog.present?
      page_query += "&catalog_id=#{@catalog.id}"
    end

    if @type.present?
      page_query += "&type=#{@type}"
    end

    if @search.present?
      page_query += "&search=#{@search}"
    end

    if params[:list_search].present?
      page_query += "&list_search=#{params[:list_search]}"
    end

    if params[:tag_name].present?
      page_query += "&tag_name=#{params[:tag_name]}"
    end

    if params[:type].present?
      page_query += "&type=#{params[:type]}"
    end

    page_query += "#{params[:per_page].present? ? "&per_page=#{params[:per_page]}" : ""}"

    {
      "courses_count" => @courses_count,
      "avariable_list_ids" => @avariable_list_ids,
      "courses" => LiquidRender::RenderModel.render_data_list(@courses) do | course |
        render_course(course).merge(
          {
            teacher: render_teacher_only(course&.teachers_with_rank&.first),
            teachers: course&.teachers_with_rank&.map do | teacher |
              render_teacher_only(teacher)
            end,
            "teachers_name" =>  course&.teachers&.map { |teacher| "#{teacher&.name}" }&.join(', '),
            "price_with_delimiter" => ActionController::Base.helpers.number_with_delimiter(course.price),
            "list_checked" => build_list_structure(course)
          }
        )
      end,
      "lists" => @lists.map do | item |
        list_render(item)
      end,
      "course_tags" => LiquidRender::RenderModel.render_data_list(@course_tags),
      "user_courses" => LiquidRender::RenderModel.render_data_list(@user_courses),
      "manager" => LiquidRender::RenderModel.render_data_one(@manager),
      "catalogs" => @catalogs.map do | item |
        catalog_render(item)
      end,
      "catalog" => @catalog ? catalog_render(@catalog) : nil,
      "type" => @type,
      "search" => @search,
      "tag_name" => @tag_name,
      "current_rank" => @rank,
      "is_top" =>  request.fullpath == "/courses",
      "lates_courses" => LiquidRender::RenderModel.render_data_list(SchoolManager.courses.published.order(published_at: :desc).limit(5)),
      "teachers_top" => LiquidRender::RenderModel.render_data_list(@teachers),
      "one_week_later" => Date.today.end_of_week.strftime("%-m月%-d日"),
      "page_query" => page_query
    }
  end

  def list_render(list)
    return {} if list.nil?
    {
      "name" => list.name,
      "description" => list.description,
      "id" => list.id,
      "children" => list.children.ordered_by_position.map do | child |
        list_render(child)
      end
    }
  end

  def build_list_structure(course)
    selected_lists = course.lists.index_by(&:id)

    root_lists = selected_lists.values.select { |list| list.ancestry.nil? }

    root_lists.map do |list|
      list_render_checked(list, selected_lists)
    end
  end

  def list_render_checked(list, selected_lists)
    return {} unless selected_lists.key?(list.id)

    {
      "name" => list.name,
      "description" => list.description,
      "id" => list.id,
      "children" => list.children
                         .select { |child| selected_lists.key?(child.id) }
                         .map { |child| list_render_checked(child, selected_lists) }
    }
  end

  def default_class_for_course
    {
      "course_item" => {
        "cart" => "card course-card card-with-popover",
        "image" => "card-img-top",
        "button_add_cart" => "add-to-cart-btn",
      },
      "course_modal" => {
        "popover" => "udemy-popover",
        "product_image" => "product-image",
        "product_updated_date" => "product-updated-date",
        "product_duration" => "product-duration",
        "product_level" => "product-level",
        "product_description" => "product-description",
      },
      "preview_modal" => {
        "cart_count" => "cart-count",
        "cart_total" => "cart-total",
        "continue_shopping_btn" => "continue-shopping-btn",
        "product_price" => "product-price",
        "product_title" => "product-title",
        "cart_modal" => "cartModal"
      }
    }
  end
  
  def get_courses_id_from_list(courses)
    list_ids = params[:list_search].split(",").map(&:strip).map(&:to_i)
    @roots = List.where(id: list_ids).group_by { |list| list.root.id }

    return courses.joins(:lists)
      .where(lists: { id: list_ids })
      .group('courses.id')
      .having(
        @roots.map { |root_id, lists|
          "SUM(CASE WHEN lists.id IN (#{lists.map(&:id).join(',')}) THEN 1 ELSE 0 END) > 0"
        }.join(" AND ")
      ).pluck(:id)
  end

  def get_available_list courses
    avariable_list_ids = courses.joins(:lists).distinct.pluck("lists.id")
    roots = @school.lists.roots
    avariable_list_ids = avariable_list_ids + roots.pluck(:id)
    if @roots
      @roots.each do |root|
        root_id = root[0]
        ids = List.find(root_id).descendant_ids
        avariable_list_ids = avariable_list_ids + ids
      end
    end
    return avariable_list_ids.uniq
  end
end
