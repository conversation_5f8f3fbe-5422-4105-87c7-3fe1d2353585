class Classroom::Meeting::ApplicationController < ApplicationController
  before_action :authenticate_user!
  before_action :set_school
  before_action :ensure_meeting_enabled
  before_action :available_tickets

  before_action -> { Meeting::Ticket }
  before_action -> { Meeting::Event }
  before_action -> { Meeting::PaymentHistory }

  layout 'meeting_user'

  private

  def ensure_meeting_enabled
    unless @school.use_meeting?
      render plain: "このページにアクセスする権限がありません。" and return
    end
  end

  def available_tickets
    @ticket_paid ||= current_user.payment_histories.by_school_id(@school.id).purchased
                              .select { |ph| ph.ticket&.active_for_period?(ph.created_at) }
                              .sum { |ph| ph.ticket&.ticket_number.to_i }
    @ticket_bonus ||= current_user.payment_histories.bonus.sum(:ticket_number).to_i

    # Get all events that are consuming tickets (not canceled or rejected)
    # This ensures tickets are "refunded" when events are canceled or rejected
    @used_tickets ||= current_user.meeting_events
                                  .by_school_id(@school.id)
                                  .where.not(status: [:canceled, :rejected])
                                  .pluck(:ticket_number)
                                  .compact
                                  .map(&:to_i)
                                  .sum

    @total_ticket_available ||= @ticket_bonus + @ticket_paid - @used_tickets
  end
end
