class Classroom::Meeting::TicketsController < Classroom::Meeting::ApplicationController
  before_action :find_ticket, only: [:create_payment_intent, :payment]

  def index
    @ticket_usages = current_user.ticket_usages.includes(payment_history: :ticket).order(used_at: :desc)
    @payment_tickets = current_user.payment_histories.by_school_id(@school.id)
      .includes(:ticket)
      .sort_by { |payment| payment.ticket&.expired_time(payment.created_at) || Time.zone.now + 100.years }
  end

  def validate_coupon
    coupon = @school.meeting_coupons.find_by(code: params[:coupon_code])

    if coupon_valid?(coupon)
      render json: {
        valid: true,
        discount_rate: coupon.discount_rate,
        type_discount: coupon.type_discount,
        message: coupon_message(coupon)
      }
    else
      render json: { valid: false, message: "無効なクーポンコードです" }
    end
  end

  def create_payment_intent
    price, applied_coupon = calculate_price_with_coupon(@ticket.price, params[:coupon_code])

    # Handle free tickets (price = 0)
    if price == 0
      # Create payment history with nil payment_intent_id for free tickets
      payment_history = create_payment_history(@ticket, price, applied_coupon, nil)
      create_sale_management(@ticket, price)
      render json: { success: true, free: true, payment_id: payment_history.id }
    else
      # Create Stripe payment intent for paid tickets
      payment_intent = create_stripe_payment_intent(price, @ticket, params[:coupon_code])
      render json: { client_secret: payment_intent.client_secret }
    end
  rescue => e
    handle_error("Payment intent creation error", e)
  end

  def payment
    price, applied_coupon = calculate_price_with_coupon(@ticket.price, params[:coupon_code])
    payment_intent = Stripe::PaymentIntent.retrieve(params[:payment_intent_id])

    if payment_intent.status == 'succeeded'
      payment_history = create_payment_history(@ticket, price, applied_coupon, payment_intent.id)
      create_sale_management(@ticket, price)
      render json: { success: true, payment_id: payment_history.id }
    else
      render json: { success: false, error: "Payment was not successful" }, status: 400
    end
  rescue => e
    handle_error("Payment error", e)
  end

  private

  def find_ticket
    @ticket = @school.tickets.find(params[:ticket_id])
  end

  def coupon_valid?(coupon)
    coupon &&
    coupon.expired_at.to_date >= Time.zone.today &&
    coupon.deleted_at.nil?
  end

  def calculate_price_with_coupon(price, coupon_code)
    return [price, nil] unless coupon_code.present?

    coupon = @school.meeting_coupons.find_by(code: coupon_code)
    return [price, nil] unless coupon_valid?(coupon)

    discount_amount = calculate_discount(price, coupon)
    [price - discount_amount, coupon]
  end

  def calculate_discount(price, coupon)
    discount = if coupon.percentage?
      (price * coupon.discount_rate / 100.0).round
    elsif coupon.fixed_amount?
      coupon.discount_rate
    else
      0
    end

    [discount, price].min
  end

  def create_stripe_payment_intent(price, ticket, coupon_code)
    Stripe::PaymentIntent.create(
      {
        amount: price,
        currency: 'jpy',
        metadata: {
          user_id: current_user.id,
          ticket_id: ticket.id,
          coupon_code: coupon_code
        },
        receipt_email: current_user.email,
        payment_method_options: {
          card: { request_three_d_secure: 'automatic' }
        }
      }.merge(
        @school.get_transfer_data_for_charge(price)
      )
    )
  rescue Stripe::StripeError => e
    raise "Stripe PaymentIntent creation failed: #{e.message}"
  end

  def create_payment_history(ticket, price, applied_coupon, stripe_payment_id)
    ticket.payment_histories.create!(
      user: current_user,
      price: price,
      ticket_number: ticket.ticket_number,
      voucher: applied_coupon,
      stripe_payment_id: stripe_payment_id,
      school_id: ticket.school_id
    )
  end

  def create_sale_management(ticket, price)
    sale_management = SaleManagement.create!(
      price: price,
      school_id: @school.id,
      user_id: current_user.id,
      name: ticket.name,
      purchase_type: "買取",
      kind: "MeetingTicket",
      username: current_user.name,
      commision_rate: @school.actived_school_plan_subscription.blank? ? 100 : @school.actived_school_plan_subscription.school_plan.commision_rate,
      application_fee_amount: @school.actived_school_plan_subscription_fee(price),
      reference_id: ticket.id,
      reference_type: ticket.class.name
    )
  end

  def coupon_message(coupon)
    if coupon.percentage?
      "クーポン「#{coupon.code}」は有効です (#{coupon.discount_rate}%OFF)"
    else
      "クーポン「#{coupon.code}」は有効です (¥#{coupon.discount_rate} OFF)"
    end
  end

  def handle_error(log_message, exception)
    Rails.logger.error("#{log_message}: #{exception.message}")
    render json: { error: exception.message }, status: 400
  end
end
