class Classroom::Meeting::Teachers::Bookings<PERSON>ontroller < Classroom::Meeting::ApplicationController
  before_action :set_teacher
  before_action :initialize_dependencies, only: [:new, :create]

  def show
  end

  def new
    # Initialize with params if they exist
    @booking = Meeting::Event.new(
      teacher_id: @teacher.id,
      category_id: params[:category_id],
      content_detail: params[:content_detail]
    )

    if @month.beginning_of_month > 1.year.from_now.beginning_of_month
      @month = 1.year.from_now.to_date
    end

    @ticket_payment_by_teacher = current_user.payment_histories.by_teacher(@teacher)
    @booking = current_user.meeting_events.new
    @limit_cancel_period_hours = @teacher.meeting_setting&.limit_cancel_period_hours || 0
  end

  def create
    @booking = current_user.meeting_events.new(booking_params.merge(teacher: @teacher).merge(school_id: @school.id))
    @limit_cancel_period_hours = @teacher.meeting_setting&.limit_cancel_period_hours || 0

    if parse_date_and_time
      if @booking.save
        create_ticket_usage
        notify_teacher
        redirect_to classroom_meeting_events_path, notice: "予約が正常に作成されました！"
      else
        handle_booking_error("予約の保存中にエラーが発生しました")
      end
    else
      handle_booking_error("日付または時間が選択されていません")
    end
  end

  def notify_teacher
    # Notify teachers when students register for meetings
    return unless @teacher.user.present?

    # noti by web
    @teacher.user.notifications.create(
      action: "create_booking",
      message: "#{current_user.name}さんが面談を予約しました。",
      target: @booking,
      school_id: SchoolManager.main_school.id
    )

    # Send email notification to the teacher
    MeetingMailer.new_booking_notification_to_teacher(@booking).deliver_now
  end

  private

  def set_teacher
    @teacher = SchoolManager.main_school&.teachers.find(params[:id])
  end

  def booking_params
    params.require(:meeting_event).permit(:start_at, :end_at, :category_id, :content_detail, :ticket_number)
  end

  def initialize_dependencies
    @month = params[:month].present? ? Date.parse(params[:month]) : Date.today
    @work_times = @teacher.meeting_work_times.from_today.order(created_at: :desc)
    @schedule = @teacher.meeting_schedule(@month, split_minutes: @school.meeting_base_minutes)
    @ticket_payment_by_teacher = current_user.payment_histories.by_teacher(@teacher)
  end

  def parse_date_and_time
    return false unless params[:meeting_event][:selected_date].present? && params[:meeting_event][:selected_time].present?

    date_str = params[:meeting_event][:selected_date]
    time_str = params[:meeting_event][:selected_time]

    month, day = date_str.split('/').map(&:to_i)
    year = determine_year(month, day)

    start_time, end_time = time_str.split(' - ')
    start_hour, start_minute = start_time.split(':').map(&:to_i)
    end_hour, end_minute = end_time.split(':').map(&:to_i)

    @booking.start_at = Time.zone.local(year, month, day, start_hour, start_minute)
    @booking.end_at = Time.zone.local(year, month, day, end_hour, end_minute)
    true
  end

  def determine_year(month, day)
    year = Date.today.year
    if month < Date.today.month || (month == Date.today.month && day < Date.today.day)
      year += 1
    end
    year
  end

  def handle_booking_error(message)
    flash.now[:error] = @booking.errors.full_messages.join("\n")

    render :new, status: :unprocessable_entity
  end

  def create_ticket_usage
    return unless @booking.ticket_number.present? && @booking.ticket_number > 0
    used_payment_history_ids = current_user.ticket_usages.select(:payment_history_id)

    ticket_bonus = current_user.payment_histories.bonus.where.not(id: used_payment_history_ids)
    purchased_tickets = current_user.payment_histories.purchased.includes(:ticket)
      .where.not(id: used_payment_history_ids)
      .select { |payment| payment.ticket&.active_for_period?(payment.created_at) }
      .sort_by { |payment| payment.ticket&.expired_time(payment.created_at) }

    available_payment_tickets = purchased_tickets + ticket_bonus

    selected_payment = available_payment_tickets.first
    return unless selected_payment.present?

    ticket_usage = @booking.build_ticket_usage(
      user: current_user,
      payment_history_id: selected_payment.id,
      used_ticket_count: @booking.ticket_number,
      used_at: Time.zone.now
    )

    ticket_usage.save
  end
end
