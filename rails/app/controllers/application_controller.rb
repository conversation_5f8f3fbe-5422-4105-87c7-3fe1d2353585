class ApplicationController < ActionController::Base
  class SchoolNotFound < StandardError; end
  rescue_from SchoolNotFound, with: :school_not_found

  include Breadcrumbable

  helper_method :impersonate?

  impersonates :user

  protect_from_forgery with: :exception
  before_action :tracking_impersonate_history, if: :impersonate?
  before_action :log_additional_data
  before_action :configure_permitted_parameters, if: :devise_controller?
  before_action :better_errors_hack, if: -> { Rails.env.development? }
  before_action :set_search
  before_action :init_school_manager
  before_action :validate_impersonate, if: :impersonate?
  before_action :store_user_location!, if: :storable_location?
  after_action :track_action
  before_action :load_cart

  # SchoolManager.create_impressionを使ってください
  def self.impressionist *options
    # impressionistライブラリーはrails 7 サポートされてないため、一旦そうします。
    # https://github.com/charlotte-ruby/impressionist/issues/305
  end

  def not_found
    raise ActionController::RoutingError.new('Not Found')
  end

  def better_errors_hack
    request.env['puma.config'].options.user_options.delete :app
  end

  def init_school_manager
    SchoolManager.init_from_request request
    SchoolManagerUser.init current_user

    if !SchoolManager.main_school ||
      (SchoolManager.main_school.deleted && (!SchoolManagerUser.current_user || !SchoolManagerUser.current_user.is_admin?))
      raise SchoolNotFound
    end
  end

  def set_school
    @school = SchoolManager.main_school
  end

  def track_action
    ahoy.track "Viewed #{controller_name}##{action_name}", request.filtered_parameters
  end

  def courses_from_params
    courses = SchoolManager.courses.published

    if params[:tag_name]
      courses = courses.tagged_with(params[:tag_name])
    end

    if params[:type]
      courses = courses.where(type_is: params[:type])
    end

    courses
  end

  def set_courses_from_params
    courses = courses_from_params
    courses.order(enrollments_count: :desc).page(params[:page]).per(10)
  end

  def self.decorate *objects
    before_action -> {
      objects.each do |object|
        origin_object = instance_variable_get("@#{object}")
        instance_variable_set(object, origin_object.decorate)
      end
    }
  end

  def self.set_and_decorate target, objects, *options
    before_action -> {
      objects = [objects] unless objects.kind_of?(Array)
      objects.each do |object|
        instance_object = instance_variable_get("@#{object}")
        if instance_object.present?
          instance_variable_set("@#{target}", instance_object.decorate)
          break
        end
      end
    }, *options
  end

  def self.set_and_decorate_and_authorize_resource target, objects, *options
    before_action -> {
      objects = [objects] unless objects.kind_of?(Array)
      objects.each do |object|
        instance_object = instance_variable_get("@#{object}")
        if instance_object.present?
          instance_variable_set("@#{target}", instance_object.decorate)
          break
        end
      end
    }, *options
    # authorize_resource :parent, *options
  end

  def admin_only
    raise CanCan::AccessDenied.new("Can not access here!") unless current_user.is_admin?
  end

  def url_for(options = {})
    url = super(options)
    return url unless request.headers[:HTTP_CUSTOMDOMAIN]
    return url if options.is_a?(Hash) && (options[:domain].present? || options[:subdomain].present?)
    return url.gsub(/\/schools\/\d+/, "")
  end

  def process_polymophic url
    return url.gsub(/\/schools\/\d+/, "") if request.headers[:HTTP_CUSTOMDOMAIN]
    url
  end

  def append_info_to_payload(payload)
    super
    payload[:current_user] = current_user.try(:email)
  end

  def render_form_inquiry
    @inquiry = Inquiry.new(school: SchoolManager.main_school)
    InquiryForm.render current_user, form_authenticity_token, request&.path, SchoolManager.main_school, inquiry: @inquiry
  end

  protected

  def configure_permitted_parameters
    devise_parameter_sanitizer.permit(:sign_up, keys: [:name, :image])
    devise_parameter_sanitizer.permit(:account_update, keys: [:name, :image])
  end

  def log_additional_data
    init_school_manager

    request.env["exception_notifier.exception_data"] = {
      params: params.to_unsafe_h.except(:password, :confirm_password),
      current_user: current_user&.email || "guest",
      user_agent: request.user_agent,
      ip: request.remote_ip,
      school_id: SchoolManager.main_school&.id || "no school",
      current_url: request.original_url
    }
  end

  def set_search
    @q = Blog.ransack(params[:q])
  end

  def redirect_to_register_if_not_logged_in
    unless user_signed_in?
      redirect_to new_user_registration_path(referrer_url: request.original_url)
    end
  end

  private
  def storable_location?
    request.get? && is_navigational_format? && !devise_controller? && !request.xhr?
  end

  def store_user_location!
  # :user is the scope we are authenticating
    store_location_for(:user, request.fullpath)
  end

  def after_sign_in_path_for(resource_or_scope)
    init_school_manager

    school_user = SchoolUser.deleted.find_by(user_id: current_user.id, school_id: SchoolManager.main_school.id)
    if school_user.present? && !current_user.is_admin?
      sign_out current_user
      flash[:alert] = "このアカウントは使用できません。"
      return "/users/sign_in"
    end

    if Rails.cache.read("school_plans_#{current_user.email}")
      id = Rails.cache.read("school_plans_#{current_user.email}")
      school_plan = SchoolPlan.find_by id
      if school_plan
        Rails.cache.delete("school_plans_#{current_user.email}")
        return "/school_plans/#{id}/school_plan_subscriptions/new"
      end
    end
    pre_link = stored_location_for(resource_or_scope) || "/"
    if pre_link.include?("subscriptions/new") || pre_link.include?("course_purchases/new") || pre_link.include?("exam_purchases/new") || pre_link.match(/\/classroom\/exams\/\d+/) || pre_link.match(/courses\/\d+/)
      pre_link
    elsif pre_link.include?("/purchase/checkout")
      pre_link
    elsif current_user.is_admin? || current_user.is_manager?(SchoolManager.main_school)
      "/admin"
    else
      "/classroom"
    end
  end

  def set_affiliate object, action, amount
    if params["edbase_ref"].present?
      url = request.path
      reference_from = params["place"] || request.referer
      if action != Affiliate.action_names["clicked"]
        guest_info = Rails.cache.read(params["guest_uuid"])
        if guest_info.nil?
          url = ""
          reference_from = ""
        else
          url = guest_info.split("@")[0]
          reference_from = guest_info.split("@")[1]
        end
      else
        @guest_uuid = SecureRandom.uuid
        Rails.cache.write(@guest_uuid, "#{url}@#{reference_from}")
      end
      user = User.find_by(affiliate_code: params["edbase_ref"])
      if user && user != current_user
        Affiliate.create(
          user_id: user.id,
          school_id: object.school_id,
          target_type: object.class.name,
          target_id: object.id,
          action_name: action,
          url: url,
          reference_from: reference_from,
          invited_user_id: current_user&.id,
          purchased_amount: amount
        )
      end
    end
  end

  def school_not_found
    respond_to do |format|
      format.html do
        redirect_to root_url(subdomain: false, _redirect_reason: "school_not_found"), allow_other_host: true
      end
      format.json do
        render json: { error: "school_not_found" }, status: :not_found
      end
    end
  end

  def tracking_impersonate_history
    return if session[:impersonating_id].blank?

    @impersonate_history = ImpersonateHistory.find(session[:impersonating_id])
    @impersonate_history.update_column(:last_accessed_at, Time.current)
  end

  def validate_impersonate
    return unless @impersonate_history
    return unless SchoolManager.main_school

    if SchoolManager.main_school.id != @impersonate_history.school_id
      raise CanCan::AccessDenied
    end

    if current_user.is_admin?
      stop_impersonating_user
      impersontating_url = session[:impersonating_url]
      session.delete(:impersonating_url)
      redirect_to(impersontating_url.presence || admin_root_path, alert: "You do not have access as Super Admin.")
    end
  end

  def impersonate?
    current_user != true_user
  end

  def load_cart
    school_id = SchoolManager.main_school&.id
    session_key = "cart_#{school_id}_session_id"
    if current_user
      @cart = Cart.find_or_create_by(user_id: current_user.id, school_id: school_id)

      if session[session_key]
        guest_cart = Cart.find_by(session_id: session[session_key])
        if guest_cart && guest_cart != @cart
          merge_carts(guest_cart, @cart)
          guest_cart.destroy
        end
      end

      session.delete(session_key)
    else
      session[session_key] ||= SecureRandom.hex(10)
      @cart = Cart.find_or_create_by(session_id: session[session_key], school_id: school_id)
    end
  end

  def merge_carts(from_cart, to_cart)
    from_cart.cart_items.each do |item|
      existing_item = to_cart.cart_items.find_or_initialize_by(product_id: item.product_id)
      existing_item.quantity ||= 0
      existing_item.quantity += item.quantity
      existing_item.save!
    end
  end
end
