class StripeController < ActionController::Base
  skip_before_action :verify_authenticity_token

  IGNORE_EVENTS = [
    'customer.subscription.trial_will_end',
    'customer.subscription.deleted',
    'customer.subscription.paused',
  ]

  def webhook
    event = Stripe::Event.construct_from(JSON.parse(request.body.read))
    Rails.logger.info("Stripe event: #{event}")

    case event.type
    when /customer.subscription.*/
      return if IGNORE_EVENTS.include?(event.type)

      subscription = Subscription.with_deleted.find_by(stripe_subscription_id: event.data.object.id)
      status = event.data.object.status
      previous_status = event.data.try(:previous_attributes).try(:status)

      return if (subscription.blank? && ['active', 'trialing'].include?(status)) || subscription.deleted?

      return if previous_status.present? && ['active', 'trialing'].include?(previous_status) && status == 'active'

      case status
      when 'active', 'trialing'
        RecoveryEnrolmentsJob.perform_later(subscription.id) if subscription.present?
        subscription.subscription_cancellations.pending.destroy_all
        subscription.status = status == 'active' ? Subscription::STATUS_DONE : Subscription::STATUS_TRIALING
        subscription.save!
      when 'canceled', 'incomplete_expired'
        SubscriptionCancellation.create_cancellation(subscription, stop_reason: "System updated #{status}", force: true)
      else
        if subscription && previous_status == "active" && (status == "incomplete" || status == "past_due")
          NotificationMailer.subscription_incomplete(subscription).deliver_later
        end

        SubscriptionCancellation.create_cancellation(subscription, stop_reason: "System updated #{status}", force: false)
      end
    when 'invoice.paid'
      # Handle successful invoice subscription_cycle set bonus ticket monthly
      invoice = event.data.object
      return unless invoice.billing_reason == 'subscription_cycle'
      subscription = Subscription.find_by(stripe_subscription_id: invoice.subscription)

      subscription.set_bonus_ticket(true) if subscription
    end

    head :ok
  end
end
