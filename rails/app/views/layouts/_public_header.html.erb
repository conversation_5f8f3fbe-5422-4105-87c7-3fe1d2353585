<style>
  <% if SchoolManager.main_school && SchoolManager.main_school.design_main_page["nav_color"].present? %>
    .public_header_color {
      background-color: <%= SchoolManager.main_school.design_main_page["nav_color"] %>
    }
  <% else %>
    .public_header_color {
      background-color: #EAEDE2
    }
  <% end %>
  <% if SchoolManager.main_school && SchoolManager.main_school.design_main_page["nav_scrolled_color"].present? %>
    .public_header_color.scrolled {
      background-color: <%= SchoolManager.main_school.design_main_page["nav_scrolled_color"] %> !important;
    }
    .public_header_color.scrolled .public_header_color {
      background-color: <%= SchoolManager.main_school.design_main_page["nav_scrolled_color"] %> !important;
    }
  <% end %>
  
  <% if SchoolManager.main_school && SchoolManager.main_school.design_main_page["hamburger_color"].present?%>
    .header-navbar-toggler {
      background-color: <%= SchoolManager.main_school.design_main_page["hamburger_color"] %> !important;
    }
  <% end %>

  .dropdown-sub-content a:hover {opacity: 0.6;}

  .menu-header .dropdown-toggle::after {
    display: inline-block !important;
  }

  .cart-badge {
    position: absolute;
    top: -5px;
    right: -10px;
    font-size: 10px;
  }

  .icon-cart.hidden {
    display: none !important;
  }
</style>

<div class="navbar-container fixed-top">
  <% top = defined?(user_signed_in).nil? ? "top" : "" %>
  <% position = (SchoolManager.main_school && SchoolManager.main_school.design_general["fixed_header_positon"] == "true") ? "position-fixed" : "" %>
  <nav class="navbar navbar-expand-lg navbar-dark bgr-nav public_header_color <%= position %>" data-sticky="<%= top %>">
    <div class="container">
      <% if SchoolManager.main_school && SchoolManager.main_school.design_main_page["page_logo"] %>
        <%= link_to (image_tag SchoolManager.main_school.design_main_page["page_logo"], height: 30), Rails.application.routes.url_helpers.root_path, class: "navbar-brand", height: "30" %>
      <% end %>
      <% if SchoolManager.main_school && SchoolManager.main_school.design_main_page["title"] %>
        <%= link_to SchoolManager.main_school.design_main_page["title"], Rails.application.routes.url_helpers.root_path, class: "school-nav-text-color", height: "30" %>
      <% end %>

      <div class="d-flex align-items-center gap-2">
        <%# Position mobie %>
        <a href="/purchase/cart" class="icon-cart position-relative d-lg-none">
          <i class="bi bi-cart fs-5"></i>
          <span id="" class="cart-mobie badge rounded-pill bg-primary cart-badge">
            <%= @cart&.cart_items&.includes(:product)&.map(&:product)&.size.to_i %>
          </span>
          <span id="product-number" data-value="<%= @cart&.cart_items&.includes(:product)&.map(&:product)&.size.to_i %>" class="d-none"></span>
        </a>

        <button class="navbar-toggler header-navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" aria-expanded="false" aria-label="Toggle navigation">
          <img class="icon navbar-toggler-open" src="/images/icons/interface/menu.svg" alt="menu interface icon" data-inject-svg />
        </button>
      </div>

      <div class="collapse navbar-collapse justify-content-end">
        <div class="py-2 py-lg-0">
          <ul class="navbar-nav">
            <% if SchoolManager.main_school&.design_menu %>
              <% user_signed_in = defined?(user_signed_in).nil? ? user_signed_in? : user_signed_in %>
              <% SchoolManager.main_school.show_menus(user_signed_in).each do |item| %>
                <% if item["kbn"] == School::MENU_KBN_MENU %>
                  <li class="nav-item dropdown menu-header  <%= item["class_name"] %>">
                    <a class="nav-link dropdown-toggle school-nav-text-color" data-bs-toggle="dropdown" aria-expanded="false">
                      <%= item["view_name"] %>
                    </a>
                    <div class="dropdown-menu public_header_color dropdown-sub-content p-3" >
                      <% item["menu_items"].each do |menu_item| %>
                          <%= link_to menu_item["view_name"], ApplicationController.helpers.get_menu_link(menu_item, SchoolManager.main_school), class: "dropdown-item school-nav-text-color py-1" %>
                      <% end %>
                    </div>
                  </li>

                <% else %>
                  <li class="nav-item dropdown <%= item["class_name"] %>">
                    <% if ApplicationController.helpers.get_check_menu_link_blank(item, SchoolManager.main_school) %>
                      <%= link_to item["view_name"], ApplicationController.helpers.get_menu_link(item, SchoolManager.main_school), target: "_blank", class: "nav-link school-nav-text-color" %>
                      <% else %>
                      <%= link_to item["view_name"], ApplicationController.helpers.get_menu_link(item, SchoolManager.main_school), class: "nav-link school-nav-text-color" %>
                    <% end %>
                  </li>
                <% end %>
              <% end %>
            <% end %>
          </ul>
        </div>
      </div>
      <%# Position web %>
      <a href="/purchase/cart" class="icon-cart position-relative d-none d-lg-block">
        <i class="bi bi-cart fs-5"></i>
        <span id="" class="cart-web badge rounded-pill bg-primary cart-badge">
          <%= @cart&.cart_items&.includes(:product)&.map(&:product)&.size.to_i %>
        </span>
        <span id="product-number" data-value="<%= @cart&.cart_items&.includes(:product)&.map(&:product)&.size.to_i %>" class="d-none"></span>
      </a>
    </div>
  </nav>
</div>
<script type="text/javascript">
  <% if SchoolManager.main_school && SchoolManager.main_school.design_main_page["nav_text_color"].present? %>
    $(".school-nav-text-color").css("color", "<%= SchoolManager.main_school && SchoolManager.main_school.design_main_page["nav_text_color"] %>");
  <% else %>
    $(".school-nav-text-color").css("color", "#9d9d9d");
  <% end %>
  <% if SchoolManager.main_school && SchoolManager.main_school.design_general["use_manual_global_header"] != "true" %>
    $(document).scroll(function () {
      var $nav = $(".public_header_color");
      $nav.toggleClass('scrolled', $(this).scrollTop() > $nav.height());
    });
  <% end %>

  // Ensure the cart counter is updated on page load
  window.addEventListener("pageshow", function (event) {
    const navType = performance.getEntriesByType("navigation")[0]?.type;
    if (event.persisted || navType === "back_forward") {
      location.reload();
    }
  });

  // Run on page load
  document.addEventListener('DOMContentLoaded', function() {
    updateCartCounterID();

    // Initialize cart visibility based on initial count from both sources
    const productNumber = document.getElementById('product-number');
    const mobileCartIcon = document.querySelector('.icon-cart.d-lg-none');
    const desktopCartIcon = document.querySelector('.icon-cart.d-none.d-lg-block');

    // Get server-side cart count
    const serverCount = productNumber ? (parseInt(productNumber.dataset.value, 10) || 0) : 0;

    // Get client-side cart count from sessionStorage
    let clientCart;
    try {
      clientCart = JSON.parse(sessionStorage.getItem('cart') || '{"items":[], "count":0, "total":0}');
    } catch (e) {
      clientCart = {items: [], count: 0, total: 0};
    }

    const effectiveCount = Math.max(serverCount, clientCart.count);

    if (effectiveCount <= 0) {
      if (mobileCartIcon) mobileCartIcon.classList.add('hidden');
      if (desktopCartIcon) desktopCartIcon.classList.add('hidden');
    } else {
      if (mobileCartIcon) mobileCartIcon.classList.remove('hidden');
      if (desktopCartIcon) desktopCartIcon.classList.remove('hidden');
    }

    // Also run when window is resized
    window.addEventListener('resize', updateCartCounterID);

    // Initialize cart synchronization
    syncCartWithServerData();
  });

  // Function to sync cart from server with session storage
  function syncCartWithServerData() {
    const cartCounter = document.getElementById('cart-counter');
    const productNumber = document.getElementById('product-number');

    if (!productNumber || !cartCounter) return;

    // Get existing server cart count
    const serverCartCount = parseInt(productNumber.dataset.value, 10) || 0;

    // Get existing session storage cart
    let sessionCart = JSON.parse(sessionStorage.getItem('cart') || '{"items":[], "count":0, "total":0}');

    <% if @cart&.cart_items&.present? %>
      const serverCartItems = [
        <% @cart.cart_items.includes(:product).each do |item| %>
          <% product = item.product.productable %>
          <% if product %>
            {
              id: "<%= product.id %>",
              title: "<%= product.name %>",
              price: <%= product.price || 0 %>,
              type: "<%= product.class.name %>",
              imageUrl: "<%= product.respond_to?(:image) && product.image ? product.image.url : '' %>",
              quantity: <%= item.quantity || 1 %>
            },
          <% end %>
        <% end %>
      ];

      // synchronization approach:
      // Make session cart exactly match server cart items

      // 1. Update session cart to match server items
      sessionCart.items = JSON.parse(JSON.stringify(serverCartItems)); // Deep copy server items

      // 2. Recalculate count and total
      sessionCart.count = serverCartItems.length;

      let total = 0;
      serverCartItems.forEach(item => {
        total += parseInt(item.price) * (item.quantity || 1);
      });
      sessionCart.total = total;

      // 3. Save the synchronized cart to session storage
      sessionStorage.setItem('cart', JSON.stringify(sessionCart));

      // 4. Update UI
      cartCounter.textContent = sessionCart.count.toString();

      // 5. Show/hide badge based on count
      if (sessionCart.count > 0) {
        cartCounter.classList.remove('d-none');
      } else {
        cartCounter.classList.add('d-none');
      }

      // 5. Update cart icon visibility
      const mobileCartIcon = document.querySelector('.icon-cart.d-lg-none');
      const desktopCartIcon = document.querySelector('.icon-cart.d-none.d-lg-block');

      if (sessionCart.count <= 0) {
        // Hide cart icons if count is zero or negative
        if (mobileCartIcon) mobileCartIcon.classList.add('hidden');
        if (desktopCartIcon) desktopCartIcon.classList.add('hidden');
      } else {
        // Show cart icons if there are items
        if (mobileCartIcon) mobileCartIcon.classList.remove('hidden');
        if (desktopCartIcon) desktopCartIcon.classList.remove('hidden');
      }
    <% else %>
      // If server has no cart items but we still have a product-number value
      if (serverCartCount === 0 && sessionCart.count > 0) {
        // Clear client-side cart to match server (which is empty)
        sessionCart = {items: [], count: 0, total: 0};
        sessionStorage.setItem('cart', JSON.stringify(sessionCart));

        // Update UI
        cartCounter.textContent = '0';
        cartCounter.classList.add('d-none');

        // Hide cart icons
        const mobileCartIcon = document.querySelector('.icon-cart.d-lg-none');
        const desktopCartIcon = document.querySelector('.icon-cart.d-none.d-lg-block');

        if (mobileCartIcon) mobileCartIcon.classList.add('hidden');
        if (desktopCartIcon) desktopCartIcon.classList.add('hidden');
      }
    <% end %>
  }

  // Update cart counter to show total items
  function updateCartCounter() {
    const cartCounter = document.getElementById('cart-counter');
    if (!cartCounter) return;

    // Get cart data from session storage
    const sessionCart = JSON.parse(sessionStorage.getItem('cart') || '{"items":[], "count":0, "total":0}');

    // Update counter with session storage count
    cartCounter.textContent = sessionCart.count.toString();

    // Hide/show the cart counter badge
    if (sessionCart.count > 0) {
      cartCounter.classList.remove('d-none');
    } else {
      cartCounter.classList.add('d-none');
    }

    // Hide/show the entire cart icon based on count
    const mobileCartIcon = document.querySelector('.icon-cart.d-lg-none');
    const desktopCartIcon = document.querySelector('.icon-cart.d-none.d-lg-block');

    if (sessionCart.count <= 0) {
      // Hide cart icons if count is zero or negative
      if (mobileCartIcon) mobileCartIcon.classList.add('hidden');
      if (desktopCartIcon) desktopCartIcon.classList.add('hidden');
    } else {
      // Show cart icons if there are items
      if (mobileCartIcon) mobileCartIcon.classList.remove('hidden');
      if (desktopCartIcon) desktopCartIcon.classList.remove('hidden');
    }
  }

  // Update cart counter ID based on viewport width
  function updateCartCounterID() {
    // Remove existing cart-counter ID (if any)
    const existingCounter = document.getElementById('cart-counter');
    if (existingCounter) {
      existingCounter.removeAttribute('id');
    }

    // Check if we're in mobile or desktop view
    const isMobileView = window.innerWidth < 992; // Bootstrap lg breakpoint is 992px

    // Select the appropriate element to receive the cart-counter ID
    if (isMobileView) {
      // Mobile view - use the cart-mobie element
      const mobileCartBadge = document.querySelector('.cart-mobie');
      if (mobileCartBadge) {
        mobileCartBadge.id = 'cart-counter';
      }
    } else {
      // Desktop view - use the cart-web element
      const desktopCartBadge = document.querySelector('.cart-web');
      if (desktopCartBadge) {
        desktopCartBadge.id = 'cart-counter';
      }
    }

    // After updating the ID, also update the cart counter
    updateCartCounter();
  }
</script>
