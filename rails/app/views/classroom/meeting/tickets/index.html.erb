<%# TODO: FIX HERE %>
<head>
  <%= csrf_meta_tags %>
  <%= csp_meta_tag %>
  <!-- other head elements -->
</head>

<div class="p-3">
  <div class="row">
    <div class="col-md-4">
      <!-- チケット残数 -->
      <div class="card mb-4">
        <div class="card-header bg-light">
          <h6 class="mb-0">チケット残数</h6>
        </div>
        <div class="card-body text-center">
          <i class="fas fa-ticket-alt text-primary" style="font-size: 48px;"></i>
          <h2 class="mt-3 mb-2"><%= @total_ticket_available %>枚</h2>
          <p class="text-muted mb-0">使用可能なチケット</p>
        </div>
        <!-- Ticket used -->
        <div class="mb-2 mb-md-0">
          <div class="card-body p-2">
            <% if @ticket_usages.present? %>
              <div class="table-responsive ticket-usage-table">
                <table class="table table-hover table-sm mb-0">
                  <thead class="table-light sticky-top">
                    <tr>
                      <th>有効期限</th>
                      <th>枚数</th>
                    </tr>
                  </thead>
                  <tbody>
                    <% @ticket_usages.each do |ticket_usage| %>
                      <tr>
                        <td>
                          <% if ticket_usage.payment_history && ticket_usage.payment_history.ticket %>
                            <%= ticket_usage.payment_history.ticket&.expired_time(ticket_usage.payment_history.created_at) %>
                          <% else %>
                            贈られたチケット
                          <% end %>
                        </td>
                        <td><%= ticket_usage.used_ticket_count %> 枚</td>
                      </tr>
                    <% end %>
                  </tbody>
                </table>
              </div>
            <% else %>
              <div class="p-3 text-center text-muted">
                使用履歴がありません
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- クーポン適用 -->
      <div class="card mb-4">
        <div class="card-header bg-light">
          <h6 class="mb-0">クーポン適用</h6>
        </div>
        <div class="card-body">
          <form id="coupon-form">
            <div class="mb-3">
              <label class="form-label">クーポンコード</label>
              <div class="input-group">
                <input type="text" class="form-control" id="coupon-code" placeholder="クーポンコードを入力">
                <button class="btn btn-primary" type="button" onclick="applyCoupon()">適用</button>
              </div>
            </div>
            <div id="coupon-success" class="alert alert-success d-none" role="alert">
              <i class="fas fa-check-circle me-2"></i><span id="coupon-message"></span>
            </div>
            <div id="coupon-error" class="alert alert-danger d-none" role="alert">
              <i class="fas fa-exclamation-circle me-2"></i>無効なクーポンコードです
            </div>
          </form>
        </div>
      </div>
      <!-- 購入履歴
      <div class="card mb-2 mb-md-0">
        <div class="card-header bg-light">
          <h6 class="mb-0">購入履歴</h6>
        </div>
        <div class="card-body p-0">
          <ul class="list-group list-group-flush ticket-list">
            <% @payment_tickets.each do |payment| %>
              <% if payment.ticket && payment.purchased? %>
                <li class="list-group-item purchase-history-item">
                  <div class="d-flex justify-content-between">
                    <span><i class="fas fa-ticket-alt me-2"></i><%= payment.ticket&.name %></span>
                    <% if payment.ticket&.active_for_period?(payment.created_at) %>
                      <span class="badge bg-success">完了</span>
                    <% else %>
                      <span class="badge bg-danger">期限切れ</span>
                    <% end %>
                  </div>
                  <div class="d-flex justify-content-between mt-1">
                    <small>チケット番号:<%= payment.ticket_number %></small>
                    <small>¥<%= payment.price %><%= "（クーポン適用）" if payment.voucher %></small>
                  </div>
                  <div class="d-flex justify-content-between mt-1">
                    <small class="text-muted"><%= payment.created_at.strftime("%Y/%m/%d") %></small>
                    <small class="text-muted">(使用期限: <%= payment.ticket&.expired_time(payment.created_at) %>)</small>
                  </div>
                </li>
              <% else %>
                <li class="list-group-item purchase-history-item">
                  <div class="d-flex justify-content-between">
                    <span><i class="fas fa-ticket-alt me-2"></i>贈られたチケット</span>
                    <span class="badge bg-success">完了</span>
                  </div>
                  <div class="d-flex justify-content-between mt-1">
                    <small>チケット番号:<%= payment.ticket_number %></small>
                  </div>
                  <div class="d-flex justify-content-between mt-1">
                    <small class="text-muted"><%= payment.created_at.strftime("%Y/%m/%d") %></small>
                  </div>
                </li>
              <% end %>
            <% end %>
          </ul>
        </div>
      </div>
      -->
    </div>
    <!-- チケット購入 -->
    <div class="col-md-8 mt-2 mt-md-0">
      <div class="card mb-4">
        <div class="card-header bg-light">
          <h6 class="mb-0">チケットを購入</h6>
        </div>
        <div class="card-body">
          <p>面談チケットを購入して、先生との面談予約に使用できます。</p>
          <div class="row row-cols-1 row-cols-md-2 row-cols-xl-3 g-4 mb-4">
            <% @school.tickets.each do |ticket| %>
              <div class="col">
                <div class="card h-100 plan-card position-relative" onclick="selectPlan(this, <%= ticket.id %>)">
                  <% if ticket.recommended? %>
                    <span class="badge bg-primary position-absolute top-0 start-50 translate-middle">おすすめ</span>
                  <% end %>
                  <div class="card-body text-center">
                    <h5 class="card-title mb-3"><%= ticket.name %></h5>
                    <h3 class="mb-3">¥<%= number_with_delimiter ticket.price %></h3>
                    <p class="mb-3 text-muted"><%= simple_format ticket.description %></p>
                    <ul class="plan-features text-start small">
                      <li>有効期限: <%= ticket.duration_month %>ヶ月</li>
                      <li>チケット枚数: <%= ticket.ticket_number %>枚</li>
                      <!-- Add other ticket details here -->
                    </ul>
                  </div>
                </div>
              </div>
            <% end %>
          </div>

          <div id="purchase-panel" class="d-none">
            <hr>
            <h6 class="mb-3">お支払い情報</h6>
            <div class="row">
              <div class="col-md-6">
                <div id="plan-summary" class="mb-4">
                  <div class="d-flex justify-content-between mb-2">
                    <span>選択したプラン:</span>
                    <strong id="selected-plan-name">3枚セット</strong>
                  </div>
                  <div class="d-flex justify-content-between mb-2">
                    <span>通常価格:</span>
                    <span id="original-price">¥3,900</span>
                  </div>
                  <div id="discount-row" class="d-flex justify-content-between mb-2 text-success d-none">
                    <span>割引:</span>
                    <span id="discount-amount">-¥1,950</span>
                  </div>
                  <div class="d-flex justify-content-between mb-2">
                    <span>お支払い金額:</span>
                    <strong id="final-price">¥3,900</strong>
                  </div>
                  <div id="coupon-applied" class="mt-2 mb-3 text-success d-none">
                    <i class="fas fa-check-circle me-1"></i><span id="applied-coupon-message">クーポン「GW2024」が適用されました (50%OFF)</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="mt-3 d-flex justify-content-end">
              <button class="btn btn-secondary me-2" onclick="cancelPurchase()">キャンセル</button>
              <button class="btn btn-primary" onclick="openStripeModal()">Stripeで支払う</button>
            </div>
          </div>

          <div id="success-message" class="alert alert-success mt-3 d-none" role="alert">
            <h5><i class="fas fa-check-circle me-2"></i>購入が完了しました！</h5>
            <p class="mb-1">チケットがアカウントに追加されました。</p>
            <p class="mb-0">すぐに面談の予約をしてみましょう。</p>
            <div class="mt-3">
              <a href="teachers.html" class="btn btn-primary">先生を探す</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Stripe Modal -->
<div class="modal fade" id="stripeModal" tabindex="-1" aria-labelledby="stripeModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="stripeModalLabel">クレジットカード情報</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" onclick="closeStripeModal()"></button>
      </div>
      <div class="modal-body">
        <form id="stripe-payment-form" class="needs-validation">
          <input type="hidden" id="selected_ticket_id" name="ticket_id">
          <input type="hidden" id="applied_coupon_code" name="coupon_code">
          <input type="hidden" id="payment_price" name="price">

          <div class="mb-3">
            <label for="stripe-card-element" class="form-label">カード情報</label>
            <div id="stripe-card-element" class="form-control" style="height: 40px; padding-top: 10px;"></div>
            <div id="stripe-card-errors" class="invalid-feedback" style="display: block;"></div>
          </div>

          <div class="d-grid gap-2 mt-4">
            <button id="stripe-submit" type="submit" class="btn btn-primary">支払いを確定する</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<style>
  .plan-card {
    border: 2px solid #eee;
    border-radius: 8px;
    transition: all 0.2s ease;
    cursor: pointer;
  }

  .plan-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  }

  .plan-card.selected {
    border-color: #007BFF;
    background-color: rgba(0,123,255,0.05);
  }

  .plan-features {
    list-style-type: none;
    padding-left: 0;
  }

  .ticket-usage-table {
    max-height: 225px;
    overflow-y: auto;
    display: block;
  }

  .ticket-usage-table table {
    width: 100%;
  }

  .ticket-usage-table thead.sticky-top {
    position: sticky;
    top: 0;
    background-color: #f8f9fa;
    z-index: 1;
  }

  .ticket-usage-table::-webkit-scrollbar {
    width: 6px;
  }

  .ticket-usage-table::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  .ticket-usage-table::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
  }

  .ticket-usage-table::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
</style>

<!-- Add this before your Stripe script -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://checkout.stripe.com/checkout.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://js.stripe.com/v3/"></script>
<script>
  const stripe = Stripe('<%= Rails.configuration.stripe[:publishable_key] %>');
  const elements = stripe.elements();

  const cardElement = elements.create('card', {
    style: {
      base: {
        fontSize: '16px',
        color: '#32325d',
        fontFamily: '-apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif',
        '::placeholder': {
          color: '#aab7c4'
        }
      },
      invalid: {
        color: '#dc3545',
        iconColor: '#dc3545'
      }
    },
    hidePostalCode: true,
  });

  // Initialize card element after DOM content loaded
  document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('stripe-card-element')) {
      cardElement.mount('#stripe-card-element');
    }
  });

  // Open Stripe Modal
  function openStripeModal() {
    // Get the Bootstrap modal instance
    const myModal = new bootstrap.Modal(document.getElementById('stripeModal'));
    myModal.show();

    // Make sure selected ticket ID is set
    const selectedTicketId = document.querySelector('.plan-card.selected').getAttribute('onclick')
      .replace('selectPlan(this, ', '').replace(')', '');
    document.getElementById('selected_ticket_id').value = selectedTicketId;

    // Set price value from final-price element
    const priceValue = document.getElementById('final-price').textContent
      .replace('¥', '').replace(/,/g, '');
    document.getElementById('payment_price').value = priceValue;
  }

  // Close Stripe Modal
  function closeStripeModal() {
    const myModal = bootstrap.Modal.getInstance(document.getElementById('stripeModal'));
    myModal.hide();
  }

  // Handle form submission with 3D Secure
  const stripeForm = document.getElementById('stripe-payment-form');
  if (stripeForm) {
    stripeForm.addEventListener('submit', async function(event) {
      event.preventDefault();

      const submitButton = document.getElementById('stripe-submit');
      const cardErrorsDiv = document.getElementById('stripe-card-errors');

      // Disable the submit button to prevent repeated clicks
      submitButton.disabled = true;
      submitButton.textContent = '処理中...';

      try {
        const ticketId = document.getElementById('selected_ticket_id').value;
        const couponCode = document.getElementById('applied_coupon_code').value || '';
        const price = document.getElementById('payment_price').value;

        // Create a PaymentIntent on the server
        const response = await fetch('/classroom/meeting/tickets/create_payment_intent', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
          },
          body: JSON.stringify({
            ticket_id: ticketId,
            coupon_code: couponCode,
            price: price
          })
        });

        if (!response.ok) {
          throw new Error('サーバーエラーが発生しました。もう一度お試しください。');
        }

        const data = await response.json();

        // Check if ticket is free after coupon
        if (data.free) {
          setTimeout(() => {
            window.location.reload();
          }, 3000);

          return;
        }

        // Confirm the payment with 3D Secure when needed
        const { error, paymentIntent } = await stripe.confirmCardPayment(
          data.client_secret,
          {
            payment_method: {
              card: cardElement,
              billing_details: {
                email: '<%= current_user.email %>' // Use server-side variable instead of form field
              }
            }
          }
        );

        if (error) {
          // Show error to customer
          cardErrorsDiv.textContent = error.message;
          submitButton.disabled = false;
          submitButton.textContent = '支払いを確定する';
        } else {
          // Payment succeeded
          if (paymentIntent.status === 'succeeded') {
            // Submit the form to create server-side records
            const successResponse = await fetch('/classroom/meeting/tickets/payment', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
              },
              body: JSON.stringify({
                ticket_id: ticketId,
                coupon_code: couponCode,
                price: price,
                payment_intent_id: paymentIntent.id
              })
            });

            if (successResponse.ok) {
              // Show success message and reset the form
              closeStripeModal();
              document.getElementById('purchase-panel').classList.add('d-none');
              document.getElementById('success-message').classList.remove('d-none');

              // Scroll to success message
              document.getElementById('success-message').scrollIntoView({ behavior: 'smooth' });

              // Refresh page after 3 seconds to update the ticket count
              setTimeout(() => {
                window.location.reload();
              }, 3000);
            } else {
              cardErrorsDiv.textContent = '処理は完了しましたが、記録の保存に問題がありました。管理者にお問い合わせください。';
              submitButton.disabled = false;
              submitButton.textContent = '支払いを確定する';
            }
          }
        }
      } catch (err) {
        cardErrorsDiv.textContent = err.message;
        submitButton.disabled = false;
        submitButton.textContent = '支払いを確定する';
      }
    });
  }

  // Function for selecting a ticket plan
  function selectPlan(element, ticketId) {
    document.querySelectorAll('.plan-card.selected').forEach(el => {
      el.classList.remove('selected');
    });

    element.classList.add('selected');
    const purchasePanel = document.getElementById('purchase-panel');
    purchasePanel.classList.remove('d-none');

    const ticketName = element.querySelector('.card-title').textContent.trim();
    const ticketPrice = element.querySelector('h3').textContent.trim();

    document.getElementById('selected-plan-name').textContent = ticketName;
    document.getElementById('original-price').textContent = ticketPrice;
    document.getElementById('final-price').textContent = ticketPrice;
    document.getElementById('discount-row').classList.add('d-none');
    document.getElementById('coupon-applied').classList.add('d-none');
    document.getElementById('selected_ticket_id').value = ticketId;

    const priceValue = ticketPrice.replace('¥', '').replace(/,/g, '');
    document.getElementById('payment_price').value = priceValue;

    document.getElementById('applied_coupon_code').value = '';

    const couponCode = document.getElementById('coupon-code').value.trim();
    if (couponCode) {
      applyCoupon();
    }

    if (window.innerWidth < 768) {
      setTimeout(() => {
        purchasePanel.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }, 100);
    }
  }

  // Cancel purchase function
  function cancelPurchase() {
    document.getElementById('purchase-panel').classList.add('d-none');
    document.querySelectorAll('.plan-card.selected').forEach(el => {
      el.classList.remove('selected');
    });

    document.getElementById('coupon-success').classList.add('d-none');
    document.getElementById('coupon-error').classList.add('d-none');
    document.getElementById('coupon-applied').classList.add('d-none');
  }

  // Coupon application function
  function applyCoupon() {
    const couponCode = document.getElementById('coupon-code').value.trim();

    if (!couponCode) {
      document.getElementById('coupon-error').classList.remove('d-none');
      document.getElementById('coupon-error').textContent = 'クーポンコードを入力してください';
      document.getElementById('coupon-success').classList.add('d-none');
      return;
    }

    // Check if a plan is selected
    if (!document.querySelector('.plan-card.selected')) {
      document.getElementById('coupon-error').classList.remove('d-none');
      document.getElementById('coupon-error').textContent = '先にチケットプランを選択してください';
      document.getElementById('coupon-success').classList.add('d-none');
      return;
    }

    // Reset messages
    document.getElementById('coupon-error').classList.add('d-none');
    document.getElementById('coupon-success').classList.add('d-none');

    // Get the CSRF token from the meta tag
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    // Validate coupon via AJAX
    $.ajax({
      url: '/classroom/meeting/tickets/validate_coupon',
      type: 'POST',
      data: { coupon_code: couponCode },
      dataType: 'json',
      headers: {
        'X-CSRF-Token': csrfToken
      },
      success: function(response) {
        if (response.valid) {
          // Success - coupon is valid
          document.getElementById('coupon-success').classList.remove('d-none');
          document.getElementById('coupon-message').textContent = response.message;

          // Calculate discount
          const originalPrice = document.getElementById('original-price').textContent;
          const price = parseInt(originalPrice.replace('¥', '').replace(/,/g, ''));
          const discountRate = response.discount_rate;
          const typeDiscount = response.type_discount;

          let discountAmount = 0;
          if (typeDiscount === 'percentage') {
            discountAmount = Math.floor(price * discountRate / 100);
          } else if (typeDiscount === 'fixed_amount') {
            discountAmount = discountRate;
          }

          // Ensure discount cannot exceed the original price
          discountAmount = Math.min(discountAmount, price);

          // Calculate final price (will never be negative)
          const finalPrice = price - discountAmount;

          // Update UI with discount
          document.getElementById('discount-row').classList.remove('d-none');
          document.getElementById('discount-amount').textContent = '-¥' + discountAmount.toLocaleString();
          document.getElementById('final-price').textContent = '¥' + finalPrice.toLocaleString();

          // Show applied coupon message
          document.getElementById('coupon-applied').classList.remove('d-none');

          if (typeDiscount === 'percentage') {
            document.getElementById('applied-coupon-message').textContent = 
              'クーポン「' + couponCode + '」が適用されました (' + discountRate + '%OFF)';
          } else if (typeDiscount === 'fixed_amount') {
            document.getElementById('applied-coupon-message').textContent =
              'クーポン「' + couponCode + '」が適用されました (¥' + discountRate.toLocaleString() + ' OFF)';
          }

          // Store coupon code in hidden field
          document.getElementById('applied_coupon_code').value = couponCode;
          document.getElementById('payment_price').value = finalPrice;
        } else {
          // Error - invalid coupon
          document.getElementById('coupon-error').classList.remove('d-none');
          document.getElementById('coupon-error').textContent = response.message;
        }
      },
      error: function(xhr, status, error) {
        console.error("AJAX Error:", status, error);
        document.getElementById('coupon-error').classList.remove('d-none');
        document.getElementById('coupon-error').textContent = 'サーバーエラーが発生しました。もう一度お試しください。';
      }
    });
  }

  document.addEventListener('DOMContentLoaded', function() {
    // Prevent form submission when Enter is pressed in the coupon form
    const couponForm = document.getElementById('coupon-form');
    if (couponForm) {
      couponForm.addEventListener('submit', function(event) {
        event.preventDefault();
        applyCoupon();
      });
    }
  });
</script>
