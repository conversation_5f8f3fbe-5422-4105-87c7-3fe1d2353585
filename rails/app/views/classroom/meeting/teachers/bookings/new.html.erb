<div class="p-3">
  <% if @booking.errors.any? %>
    <div id="error_explanation" class="alert alert-danger">
      <% @booking.errors.full_messages.each do |message| %>
        <%= message %>
      <% end %>
    </div>
  <% end %>

  <h5 id="booking-teacher-name"><%= @teacher.name %>先生に予約する</h5>
  <!-- チケット確認 -->
  <div class="card mb-4">
    <div class="card-header bg-light">
      <h6 class="mb-0">チケット確認</h6>
    </div>
    <div class="card-body">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <% time_param = params[:time].to_i %>
          <% required_tickets = time_param == 60 ? 2 : 1 %>

          <p class="mb-0">この予約には<strong>面談チケット<%= required_tickets %>枚</strong>が必要です</p>
          <p class="mb-1">現在のチケット残数: <span class="badge bg-primary text-white p-2"><i class="fas fa-ticket-alt me-1"></i><%= @total_ticket_available %>枚</span></p>

          <% if @total_ticket_available < required_tickets %>
            <div class="alert alert-warning mt-2 mb-0">
              <small><i class="fas fa-exclamation-triangle me-1"></i>チケットが不足しています。予約には<%= required_tickets %>枚必要です。</small>
            </div>
          <% end %>
        </div>
        <%= link_to classroom_meeting_tickets_path, class: "btn btn-outline-primary" do %>
          <i class="fas fa-plus-circle me-1"></i>チケットを購入
        <% end %>
      </div>
    </div>
  </div>
  <!-- 予約フォーム -->
  <div class="row">
    <div class="col-md-4 mb-4">
      <div class="card">
        <div class="card-header bg-light">
          <h6 class="mb-0">予約情報</h6>
        </div>
        <div class="card-body">
          <%= form_with model: @booking, url: classroom_meeting_bookings_path, method: :post, id: "booking-form", local: true do |f| %>
            <%= f.hidden_field :teacher_id, value: @teacher.id %>
            <%= f.hidden_field :selected_date, id: "selected-date-field" %>
            <%= f.hidden_field :selected_time, id: "selected-time-field" %>
            <%= f.hidden_field :ticket_number, value: required_tickets %>

            <div class="mb-3">
              <label class="form-label">面談内容</label>
              <%= f.select :category_id,
                  options_from_collection_for_select(@teacher.meeting_categories.rank(:row_order), :id, :name, @booking.category_id),
                  { prompt: 'カテゴリを選択してください' },
                  { class: "form-select", required: true }
              %>
            </div>

            <div class="mb-3">
              <label class="form-label">詳細な内容</label>
              <%= f.text_area :content_detail, class: "form-control", rows: 4, placeholder: "面談で相談したい内容を具体的に記入してください", required: true %>
            </div>

            <div class="mb-3">
              <label class="form-label">希望日時</label>
              <div class="alert alert-info">
                <small><i class="fas fa-info-circle me-1"></i>右側のカレンダーから希望日時を選択してください</small>
              </div>
              <p class="mb-0" id="selected-date-time"><i class="fas fa-calendar me-1"></i>未選択</p>
            </div>

            <div class="mb-3">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="terms-check" required>
                <label class="form-check-label" for="terms-check">
                  <small>キャンセルポリシーに同意します</small>
                </label>
              </div>
              <% if @limit_cancel_period_hours > 0 %>
                <small class="text-muted d-block mt-1">※<%= @limit_cancel_period_hours %>時間前までキャンセル可能（チケットは返却されます）</small>
              <% end %>
            </div>

            <div class="d-grid">
              <%= f.submit "予約を確定する",
                  class: "btn btn-primary",
                  id: "booking-submit-btn",
                  disabled: true,
                  data: { 
                    has_enough_tickets: (@total_ticket_available >= required_tickets).to_s,
                    required_tickets: required_tickets
                  } %>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- 右側：カレンダーと時間枠タブ -->
    <div class="col-md-8">
      <div class="card">
        <div class="card-header bg-light">
          <ul class="nav nav-tabs card-header-tabs" id="bookingTab" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active" id="calendar-tab" data-bs-toggle="tab" data-bs-target="#calendar" type="button" role="tab" aria-controls="calendar" aria-selected="true">カレンダー</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="timeslots-tab" data-bs-toggle="tab" data-bs-target="#timeslots" type="button" role="tab" aria-controls="timeslots" aria-selected="false">時間枠</button>
            </li>
          </ul>
        </div>
        <div class="card-body">
          <div class="tab-content" id="bookingTabContent">
            <!-- Calendar Tab -->
            <div class="tab-pane fade show active" id="calendar" role="tabpanel" aria-labelledby="calendar-tab">
              <div class="row mb-3 align-items-center">
                <div class="col">
                  <h5 class="mb-0"><%= l @month, format: :short_year_and_month %></h5>
                </div>
                <div class="col-auto">
                  <div class="btn-group">
                    <a href="#" id="prev-month-btn" class="btn btn-outline-secondary btn-sm" data-month="<%= @month.prev_month %>">
                      <i class="fas fa-chevron-left"></i>
                    </a>
                    <% if @month.beginning_of_month < 1.year.from_now.beginning_of_month %>
                      <a href="#" id="next-month-btn" class="btn btn-outline-secondary btn-sm" data-month="<%= @month.next_month %>">
                        <i class="fas fa-chevron-right"></i>
                      </a>
                    <% end %>
                  </div>
                </div>
              </div>

              <table class="table table-bordered text-center" style="table-layout: fixed;">
                <thead>
                  <tr>
                    <th>月</th>
                    <th>火</th>
                    <th>水</th>
                    <th>木</th>
                    <th>金</th>
                    <th class="text-danger">土</th>
                    <th class="text-danger">日</th>
                  </tr>
                </thead>
                <tbody>
                <% @schedule.each_slice(7).each do |week| %>
                  <tr>
                    <% week.each.with_index do |(date, _, values), index| %>
                      <td class="<%= index > 4 ? 'text-danger' : '' %> <%= date < Date.today ? 'text-muted' : '' %> calendar-day <%= values.blank? || date < Date.today ? "calendar-day-disabled" : '' %>"
                          data-date="<%= l date, format: :short %>"
                          data-has-slots="<%= values.present? && date >= Date.today %>"
                          data-slots='<%= raw values.to_json %>'
                          onclick="<%= date >= Date.today ? 'selectDateFromElement(this)' : '' %>">
                        <%= date.day %>

                        <% if Meeting::Holiday.holiday?(date) %>
                          <div class="mt-1">
                            <span class="badge bg-secondary">休み</span>
                          </div>
                        <% end %>

                        <% if values.empty? && index > 4 %>
                          <div class="mt-1">
                            <span class="badge bg-danger">休み</span>
                          </div>
                        <% end %>

                        <% if values.present? && date >= Date.today %>
                          <div class="mt-1">
                            <span class="badge bg-success">空き</span>
                          </div>
                        <% end %>
                      </td>
                    <% end %>
                  </tr>
                <% end %>
                </tbody>
              </table>

              <div class="mt-3">
                <div class="d-flex align-items-center mb-1">
                  <span class="badge bg-success me-2">&nbsp;</span>
                  <span>予約可能</span>
                </div>
                <div class="d-flex align-items-center mb-1">
                  <span class="badge bg-danger me-2">&nbsp;</span>
                  <span>予約不可</span>
                </div>
                <div class="d-flex align-items-center">
                  <span class="badge bg-primary me-2">&nbsp;</span>
                  <span>選択中</span>
                </div>
              </div>
            </div>

            <!-- Time Slots Tab -->
            <div class="tab-pane fade" id="timeslots" role="tabpanel" aria-labelledby="timeslots-tab">
              <h6 class="mb-3" id="time-slots-date">日付を選択してください</h6>
              <div class="row row-cols-2 row-cols-md-4 g-2 mb-4" id="time-slots-container">
                <!-- Time slots will be populated by JavaScript -->
                <div class="col-12 text-center text-muted py-4" id="no-slots-message">
                  先にカレンダーから日付を選択してください
                </div>
              </div>
              <div class="alert alert-info">
                <small><i class="fas fa-info-circle me-1"></i>希望する時間枠をクリックして選択してください</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const prevMonthBtn = document.getElementById('prev-month-btn');
    const nextMonthBtn = document.getElementById('next-month-btn');
    const bookingForm = document.getElementById('booking-form');
    const categorySelect = bookingForm?.querySelector('select[name*="category_id"]');
    const contentDetailArea = bookingForm?.querySelector('textarea[name*="content_detail"]');
    const selectedDateField = document.getElementById('selected-date-field');
    const selectedTimeField = document.getElementById('selected-time-field');
    const selectedDateTimeElement = document.getElementById('selected-date-time');

    function navigateWithFormData(month) {
      try {
        let url = '<%= new_classroom_meeting_booking_path(@teacher) %>?month=' + month + '&time=<%= params[:time] %>';
        if (categorySelect?.value) url += '&category_id=' + encodeURIComponent(categorySelect.value);
        if (contentDetailArea?.value) url += '&content_detail=' + encodeURIComponent(contentDetailArea.value);
        if (selectedDateField?.value) url += '&selected_date=' + encodeURIComponent(selectedDateField.value);
        if (selectedTimeField?.value) url += '&selected_time=' + encodeURIComponent(selectedTimeField.value);
        window.location.href = url;
      } catch (error) {
        console.error("Error navigating with form data:", error);
        window.location.href = '<%= new_classroom_meeting_booking_path(@teacher) %>?month=' + month + '&time=<%= params[:time] %>';
      }
    }

    function prefillForm() {
      try {
        <% if params[:category_id].present? %>
          if (categorySelect) categorySelect.value = <%= raw params[:category_id].to_json %>;
        <% end %>
        <% if params[:content_detail].present? %>
          if (contentDetailArea) contentDetailArea.value = <%= raw params[:content_detail].to_json %>;
        <% end %>
        <% if params[:selected_date].present? %>
          if (selectedDateField) {
            selectedDateField.value = <%= raw params[:selected_date].to_json %>;
            <% if params[:selected_time].present? %>
              if (selectedTimeField) selectedTimeField.value = <%= raw params[:selected_time].to_json %>;
              if (selectedDateTimeElement) {
                selectedDateTimeElement.innerHTML = `<i class="fas fa-calendar me-1"></i>${<%= raw params[:selected_date].to_json %>} ${<%= raw params[:selected_time].to_json %>}`;
              }
            <% end %>
          }
        <% end %>
      } catch (error) {
        console.error("Error pre-filling form:", error);
      }
    }

    prevMonthBtn?.addEventListener('click', function(e) {
      e.preventDefault();
      navigateWithFormData(this.dataset.month);
    });

    nextMonthBtn?.addEventListener('click', function(e) {
      e.preventDefault();
      navigateWithFormData(this.dataset.month);
    });

    prefillForm();
  });
</script>
