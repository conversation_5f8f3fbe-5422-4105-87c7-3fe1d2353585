<div class="mdc-card mdc-card--outlined p-3">
  <div class="school-dashboard" style="box-shadow: none">
    <div class="mdc-layout-grid w-100 py-0">
      <div class="mdc-layout-grid__inner">
        <div class="mdc-layout-grid__cell--span-8">
          <div class="time-range-select date-filters" style="max-width: 300px">
            <%= form_tag admin_school_sales_managements_path(@school), method: :get do %>
              <div class="input-group">
                <%= text_field_tag :duration, "#{@duration[0].strftime("%Y/%m/%d")} - #{@duration[1].strftime("%Y/%m/%d")}", autocomplete: 'off', class: "form-control duration-selector auto-submit-datetime" %>
                <%= hidden_field_tag :filter_type, @filter_type %>
                <div class="input-group-append">
                  <span class="input-group-text h-100" id="basic-addon2">
                    <%= icon('fas', 'calendar-alt') %>
                  </span>
                </div>
              </div>
            <% end %>
          </div>
          <div class="usage-time-graph" id="usage-time-graph-price"></div>
        </div>
        <div class="mdc-layout-grid__cell--span-4">
          <div class="usage-time-dashboard-item">
            <div class="title pt-2">売上</div>
            <div class="data pe-2">
              <span data-statistic-field="price_in_range"><%= number_with_delimiter(@school_info[:sale_managements][:price_in_range].to_i) %></span>円
            </div>
          </div>
          <div class="text-end me-2">
            <p class="premium-service-price mt-3 mb-1">
              <%= get_name_price "プレミアムサービス", @subscription_purchase_list.count, number_with_delimiter(@subscription_purchase_list.sum(&:price)) %>
            </p>
            <hr>
            <p class="purchase-course-price">
              <%= get_name_price "講座", @course_purchase_list.count, number_with_delimiter(@course_purchase_list.sum(&:price)) %>
            </p>
            <hr>
            <p class="purchase-exam-price">
              <%= get_name_price "テスト", @exam_purchase_list.count, number_with_delimiter(@exam_purchase_list.sum(&:price)) %>
            </p>
            <p class="purchase-meeting-ticket-price">
              <%= get_name_price "チケットミーティング", @meeting_ticket_purchase_list.count, number_with_delimiter(@meeting_ticket_purchase_list.sum(&:price)) %>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
