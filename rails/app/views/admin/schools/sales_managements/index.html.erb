<div class="mdc-layout-grid py-3">
  <div class="mdc-layout-grid__inner">
    <div class="mdc-layout-grid__cell--span-6">
      <label class="mdc-typography--subtitle1">売上管理</label>
    </div>
    <div class="mdc-layout-grid__cell--span-6 text-end">
      <%= link_to admin_school_sales_managements_path(@school, format: :csv, duration: "#{@duration[0].strftime("%Y/%m/%d")} - #{@duration[1].strftime("%Y/%m/%d")}", filter_type: @filter_type, search: params[:search]), class: "mdc-button mdc-button--raised" do %>
        <span class="mdc-button__ripple"></span>
        <i class="material-icons mdc-button__icon" aria-hidden="true">file_download</i>
        <span class="mdc-button__label">エクスポート</span>
      <% end %>
    </div>
  </div>
</div>

<%= render "sales_chart" %>
<%= render "tab_navigation" %>

<div class="mdc-card mdc-card--outlined w-100 p-3 mb-4">
  <%= render "search_form" %>
  <%= render "data_table" %>
</div>

<% content_for :bottom_script do %>
  <script type="text/javascript">
    var series_data_price = [
      {
        name: '売上',
        gapSize: 1,
        tooltip: {
          valueSuffix: ' 円'
        },
        data: <%= @school_info[:sale_managements][:price_series].to_json.html_safe %>
      },
    ];
    showUserActionGraph(series_data_price, ["<%= @duration[0].to_date %>", "<%= @duration[1].to_date %>"], "usage-time-graph-price", "column");
  </script>
<% end %>

<% content_for :custom_style do %>
  <style>
    .mdc-tab-bar--gray {
      background-color: #ddd;
    }
  </style>
<% end %>
