<% simple_mde_class = "simple-mde-editor-lesson" %>
<div class="mdc-layout-grid__cell--span-10">
  <div class="mdc-card mdc-card--outlined px-3 pt-4">
    <div class="d-flex justify-content-between align-items-center">
      <h5></h5>
      <% if @contact_form.present? %>
        <div class="mdc-layout-grid__cell--span-6 text-end">
          <label class="mdc-typography--headline7 save-status text-success me-2 d-none"></label>
          <button onclick="submitForm()" class="mdc-button mdc-button--raised submit_contact_form_btn" id="create-contact-form">保存する</button>
        </div>
      <% end %>
    </div>
    <div class="form-group mt-3">
      <% if @contact_form.present? %>
        <%= render "admin/contact_forms/input_modal" %>
        <%= render 'admin/contact_forms/form_body' %>
      <% else %>
        <%= link_to "アンケートフォーム作成", url_for([:admin, @school, @user_onboard, :contact_forms]), method: :post,class: "btn btn-light" %>
      <% end %>
    </div>
  </div>
</div>
<% if @contact_form.present? %>
  <script>
    function submitForm() {
      $("#contact_form_body_<%= @contact_form.id %>").val(window.simplemde.value())
      $("#edit_contact_form_<%= @contact_form.id %>").submit()
    }
  </script>
  <%= render "admin/contact_forms/common/save_js"%>
<% end %>
