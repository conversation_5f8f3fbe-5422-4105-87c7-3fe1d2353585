<ul class="onboarding-nav my-4">
  <li class="d-flex align-items-center <%= @step.nil? || @step == "basic" ? 'active' : '' %>">
    <a href="<%="/admin/schools/#{@school.id}/user_onboards/#{@user_onboard.id || ''}"%>/edit?step=basic"
      class="link-primary user_onboard_link">
      <span class="material-icons me-2 text-dark">
        <% if @user_onboard.step_basic_finished? %>
        check_circle_outline
        <% else %>
        radio_button_unchecked
        <% end %>
      </span>
      ウエルカムページ
    </a>
  </li>
  <li class="d-flex align-items-center <%= @step == "course" ? 'active' : '' %>">
    <a href="<%="/admin/schools/#{@school.id}/user_onboards/#{@user_onboard.id || ''}"%>/edit?step=course"
      class="link-primary user_onboard_link">
      <span class="material-icons me-2 text-dark">
        <% if @user_onboard.step_course_finished? %>
        check_circle_outline
        <% else %>
        radio_button_unchecked
        <% end %>
      </span>
      コース一覧ページ
    </a>
  </li>
  <li class="d-flex align-items-center <%= @step == "questionnaire" ? 'active' : '' %>">
    <a href="<%="/admin/schools/#{@school.id}/user_onboards/#{@user_onboard.id || ''}"%>/edit?step=questionnaire"
      class="link-primary user_onboard_link">
      <span class="material-icons me-2 text-dark">
        <% if @user_onboard.step_questionnaire_finished? %>
        check_circle_outline
        <% else %>
        radio_button_unchecked
        <% end %>
      </span>
      アンケートページ
    </a>
  </li>
</ul>
<%# <div class="btn btn-dark rounded-pill px-3">完了する</div> %>


<script>
$(".user_onboard_link").on('click', (e)=>{
  <% if @step == "option" || @step == "course" %>
    return true
  <% else %>
    return checkChangeFromEvent("#edit_user_onboard_<%= @user_onboard.id %>")
  <% end %>
})
</script>
