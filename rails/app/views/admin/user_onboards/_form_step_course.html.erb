<div class="mdc-layout-grid__cell--span-8">
  <div class="mdc-card mdc-card--outlined p-4">
    <div class="d-flex justify-content-between align-items-center">
      <h2 class="h5 mb-0">講座を追加する</h2>
      <div id="price_plan_setting_container">
        <a
          href="javascript:void(0)"
          data-url="<%= admin_school_courses_path %>"
          data-premium-service-id="<%= @user_onboard.id %>"
          data-title="コースの追加"
          data-placeholder="コース名を入力してください"
          class="btn btn-primary"
          onclick="showCoursesModal(this)">
          講座を追加する
        </a>
      </div>
    </div>
    <section class="form-group mt-1 table-sortable"  id="course-list">
      <%= render "courses", user_onboard_courses: @user_onboard.user_onboard_courses %>
    </section>
  </div><!-- /.mdc-card -->
</div><!-- /.mdc-layout-grid__cell--span-8 -->


<!-- Modal -->
<div class="modal fade" id="add-to-course-list" role="dialog" aria-labelledby="addCourseListModalLabel">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title" id="addCourseListModalLabel"></h4>
        <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
      </div>
      <div class="modal-body">
        <div>
          <%= form_tag "#", class: "filter-search form-inline me-2" do %>
            <%= text_field_tag :name, "", class: "filter-search-input form-control me-2", id: "filter-search-input", size: 30 %>
            <button type="button" id="search-filter-btn" class="btn btn-light">検索</button>
          <% end %>
        </div>
        <div class="filter-data mt-2"></div>
      </div>
    </div>
  </div>
</div>

<script>
  function showCoursesModal(elm) {
    var url = $(elm).data("url") + ".js?user_onboard_id=" + elm.attributes['data-premium-service-id'].value
    $.get(url, function () {
      $("#add-to-course-list").find("[name=name]").attr("placeholder", $(elm).data("placeholder"))
      $("#add-to-course-list").find("#search-filter-btn").attr("data-url", url)
      $("#add-to-course-list").find(".modal-title").text($(elm).data("title"))
      $("#add-to-course-list").modal("show")
    })
  }
</script>