<%= form_for [:admin, @school, @course], url: save_publishing_admin_school_course_path(@school, @course), method: :post, remote: true  do |f| %>
  <div class="d-flex justify-content-between w-100">
    <div>
      <div class="form-check">
        <label  class="form-check-label">
          <%= f.radio_button :published, "1", checked: @course.published_at.present?, class: "published form-check-input" %> 公開
          <% if @course.published_at.present? %>
            <span class="ms-3"><small><%= nichiji @course.published_at %></small></span>
          <% end %>
        </label>
      </div>
      <div class="form-check">
        <label  class="form-check-label">
          <%= f.radio_button :published, "0", checked: !@course.published_at.present?, class: "published form-check-input" %> 非公開
        </label>
      </div>
    </div>
    <div id="course_info_pulishing" style="display: none;">
      <label class="mdc-typography--headline7 save-status text-success me-2 d-none"></label>
      <button onclick="submitForm()" class="mdc-button mdc-button--raised" id="create-course">保存する</button>
    </div>
  </div>
<% end %>
<script>
  function submitForm() {
    $("#edit_course_<%= @course.id %>").submit()
  }
  $(document).ready(function() {
    $(document).on("change", "#edit_course_<%= @course.id %> :input", function() {
      $("#course_info_pulishing").show();
    });
  })
</script>
