<div class="mdc-layout-grid py-3">
  <div class="mdc-layout-grid__inner">
    <div class="mdc-layout-grid__cell--span-6">
      <a href="<%= url_for([:admin, @school, @course]) %>" type="button" class="mdc-button mdc-button--outlined me-2">
        <i class="material-icons">west</i>
      </a>
      <%= @course.name %>
    </div>
    <div class="mdc-layout-grid__cell--span-6 text-end">
      <label class="mdc-typography--headline7 save-status text-success me-2 d-none"></label>
      <button onclick="submitForm()" class="mdc-button mdc-button--raised" id="create-course">保存する</button>
      <%= link_to @course, class: "mdc-button mdc-button--outlined", target: "_blank" do %>
        <span class="mdc-button__ripple"></span>
        <span class="mdc-button__label">プレビュー</span>
      <% end %>

    </div>
  </div>
</div>

<div class="mdc-layout-grid pt-3">
  <div class="mdc-layout-grid__inner">
    <%= render 'sidebar', course: @course %>
    <div class="mdc-layout-grid__cell--span-8">
      <div class="mdc-card mdc-card--outlined px-3 pt-4">
        <%= form_for [:admin, @school, @course], url: save_pricing_admin_school_course_path, method: :post,remote: true  do |f| %>
          <h5>価格設定</h5>
          <hr>

          <div class="form-group mt-3">
            <h6 class="fw-bold">
              コースタイプを選択してくだい
            </h6>
            <p>
              ユーザーが一括で支払い
            </p>
            <label style="margin-right: 30px">
              <%= f.radio_button :type_is, "free", checked: @course.type_is == "free", class: "type_is" %> 無料
            </label>
            <label style="margin-right: 30px">
              <%= f.radio_button :type_is, "product", checked: @course.type_is == "product", class: "type_is" %> 有料
            </label>
            <label>
              <%= f.radio_button :type_is, "premium", checked: @course.type_is == "premium", class: "type_is" %> プレミアムサービスのみ
            </label>
          </div>
          <div class="show-detail-pricing">
            <hr>
            <div class="form-group">
              <label for="" class="fw-bold">金額</label>
              <div>
                <%= f.number_field :price, min: 0, class: "form-control min-max-validate" %>
              </div>
            </div>
            <div class="form-group">
              <label for="" class="fw-bold">割引</label>
              <div>
                <%= f.number_field :cut_rate, max: 100, min: 0, class: "form-control min-max-validate" %>
              </div>
            </div>

            <div class="form-group">
              <div class="d-flex align-items-center">
                <div class="mdc-checkbox">
                  <input class="mdc-checkbox__native-control" type="checkbox" value="1" <%= @course.installment_count > 1 ? 'checked' : '' %>
                    name="installment_count_option" id="installment_count_option">
                  <div class="mdc-checkbox__background">
                    <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                      <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59" />
                    </svg>
                    <div class="mdc-checkbox__mixedmark"></div>
                  </div>
                </div>
                分割払いを許可する
              </div>

              <div id="installment_count_setting" style="display: <%= @course.installment_count > 1 ? 'block' : 'none' %>">
                <div class="form-group ms-4 d-flex align-items-center">
                  <%= f.label "分割回数", class: "mt-2" %>
                  <div class="ms-2">
                    <%= f.number_field :installment_count, min: 0, class: "form-control min-max-validate" %>
                  </div>
                </div>
              </div>
            </div>

            <div class="form-group">
              <label for="" class="fw-bold">クーポン</label>
              <div>
                <%= f.select :coupon_id, @school.coupons.pluck(:name, :id), {include_blank: true}, {class: "form-control"} %>
              </div>
            </div>
          </div>
        <% end %>
      </div><!-- /.mdc-layout-grid__cell--span-8 -->
    </div>
  </div><!-- /.mdc-layout-grid__inner -->
</div><!-- /.mdc-layout-grid -->
<script>
  function submitForm() {
    $("#edit_course_<%= @course.id %>").submit()
  }
  $(".type_is").change(function() {
    if (this.value == "product") {
      $(".show-detail-pricing").show()
      } else {
      $(".show-detail-pricing").hide()
    }
  })
  $(document).ready(function() {
    if ($("#course_type_is_product").is(':checked')){
      $(".show-detail-pricing").show()
    } else {
      $(".show-detail-pricing").hide()
    }
    $(document).on("change", "#edit_course_<%= @course.id %> :input", function() {
      $("#edit_course_<%= @course.id %>").data("changed",true);
    });
  })

  $("#installment_count_option").change(function() {
    if($("#installment_count_option").prop( "checked" )){
      $("#installment_count_setting").show()
    } else {
      $("#installment_count_setting").hide()
      $("#course_installment_count").val(1)
    }
  })
</script>
<%= render "admin/courses/common/save_js"%>
