<div class="mdc-layout-grid py-3">
  <div class="mdc-layout-grid__inner">
    <div class="mdc-layout-grid__cell--span-6">
      <a href="<%= url_for([:admin, @school, @course]) %>" type="button" class="mdc-button mdc-button--outlined me-2">
        <i class="material-icons">west</i>
      </a>
      <%= @course.name %>
    </div>
    <div class="mdc-layout-grid__cell--span-6 text-end">
      <label class="mdc-typography--headline7 save-status text-success me-2 d-none"></label>
      <button onclick="submitForm()" class="mdc-button mdc-button--raised" id="create-course">保存する</button>
      <%= link_to @course, class: "mdc-button mdc-button--outlined", target: "_blank" do %>
        <span class="mdc-button__ripple"></span>
        <span class="mdc-button__label">プレビュー</span>
      <% end %>
    </div>
  </div>
</div>


<div class="mdc-layout-grid pt-3">
  <div class="mdc-layout-grid__inner">
    <%= render 'sidebar', course: @course %>
    <div class="mdc-layout-grid__cell--span-8">
      <div class="mdc-card mdc-card--outlined px-3 pt-4">
        <%= form_for [:admin, @school, @course], url: save_setting_admin_school_course_path, method: :post,remote: true  do |f| %>
          <h5>コース設定</h5>
          <div class="mdc-layout-grid py-3">
            <div class="mdc-layout-grid__inner">
              <div class="mdc-layout-grid__cell" id="error_explanation">
                <ul class="mb-0 error_explanation">
                </ul>
              </div>
            </div>
          </div>
          <hr>
          <p>
            以下の説明はコース紹介ページに表示され、一般の訪問者も見ることができるため、コースの業績に直接影響します。学習者にとっては、コースが自分に適しているかどうかを判断する材料になります。
          </p>
          <hr>
          <div class="form-group mt-3">
            <h6 class="fw-bold">
              カテゴリ
            </h6>
            <div class="form-group">
            <%= text_field_tag 'course[tag_list]', @course.tag_list.join(','), id: "tag_list", class: "form-control tagit-field" %>
            <% @course_tags&.each do |ct|%>
              <a href="javascript:void(0);" class="tags-list"><%= ct %></a>
            <% end %>
          </div>
          </div>
          <div class="form-group mt-3">
            <h6 class="fw-bold">
              期間
            </h6>
            <%= f.text_field :duration, class: "form-control" %>
          </div>
          <div class="form-group mt-3">
            <h6 class="fw-bold">
              利用規約選択
            </h6>
            <%= f.select :term_id, @school.terms.pluck(:name, :id), {include_blank: true}, {class: "form-control"} %>
          </div>
          <div class="form-group mt-3">
            <h6 class="fw-bold">
              レベル
            </h6>
            <%= f.select :level, Course.levels_i18n.keys.map {|k| [Course.levels_i18n[k], k]}, {},{class: "form-control"} %>
          </div>
          <div class="form-group mt-3">
            <h6 class="fw-bold">
              教師
            </h6>
            <%= render 'teacher_link' %>
          </div>
          <div class="form-group mt-3">
            <h6 class="fw-bold">
              デフォルトAIエージェント
            </h6>
            <p class="text-muted small">
              このコースのレッスンで優先的に使用されるAIエージェントを選択してください。選択しない場合は、学校のデフォルトエージェントが使用されます。
            </p>
            <%= f.collection_select :default_ai_tutor_agent_id,
                @school.ai_tutor_agents.enabled.where(agent_category: ['lesson', 'custom']).order(:name),
                :id, :name,
                { prompt: "選択してください（学校デフォルトを使用）" },
                { class: "form-control" } %>
          </div>
          <div class="form-group mt-3">
            <h6 class="fw-bold">
              スラーグ
            </h6>
            <%= f.text_field :slug, {class: "form-control"} %>
          </div>
          <div class="form-group mt-3">
            <h6 class="fw-bold">
              素材
            </h6>
            <%= f.hidden_field :is_upload_file, value: false, id: "js-file-upload" %>
          </div>
          <%= link_to "アップロード", "#", class: "btn btn-light btn-sm material-upload-btn"  %>
            <%= f.file_field :materials, multiple: true, class: "d-none auto-submit-upload material-upload" %>
            <hr class="mt-0 mb-2">
            <div id="materials_list">
              <%= render partial: "material", collection: @materials %>
            </div>
          </div>
        <% end %>
    </div>
  </div><!-- /.mdc-layout-grid__inner -->
</div><!-- /.mdc-layout-grid -->

<% content_for :bottom_script do %>
  <script>
    function submitForm() {
      $("#edit_course_<%= @course.id %>").submit()
    }
    $(document).ready(function() {
      $("#edit_course_<%= @course.id %>").data("changed",false);
      var tags = <%= raw @course_tags&.map(&:name).to_json %> || [];
      $("#tag_list").tagit({
        availableTags: tags,
        fieldName: "course[tag_list]",
        singleField: true,
      });

      $.each($(".tags-list"), function(key, value ) {
        if ($("#tag_list").val().split(",").indexOf(value.text) != -1) {
          $(value).hide()
        }
      });

      $(".tagit").click(function(e) {
        $("#edit_course_<%= @course.id %>").data("changed",true);
        if (e.target.textContent == "×") {
          var tag = $(e.target).parent().parent().find(".tagit-label").text();
          $( ".tags-list:contains('" + tag + "')" ).show();
        }
      });

      $(".tags-list").click(function() {
        $("#edit_course_<%= @course.id %>").data("changed",true);
        var tags = $("#tag_list").val();
        if (tags.indexOf(this.text == -1)) {
          $("#tag_list").tagit("createTag", this.text);
          $("#tag_list").val(tags + "," + this.text);
        }
        $(this).hide();
      });
      $(document).on("change", "#edit_course_<%= @course.id %> :input", function(e) {
        if (e.target.name != "course[tag_list]") {
          $("#edit_course_<%= @course.id %>").data("changed",true);
        }
      });
    });
  </script>
<% end %>
<%= render "admin/courses/common/save_js"%>
