<%= content_for :title, '面談予約システム - 面談記録' %>
<%= render 'layouts/admin_meeting' do %>
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-12 ms-auto">
        <div class="row py-3 border-bottom">
          <div class="col">
            <h4 class="mb-0">面談記録</h4>
          </div>
        </div>
        <div class="p-3">
          <div class="row">
            <div class="col-md-4">
              <div class="card mb-4">
                <div class="card-header bg-light">
                  <h6 class="mb-0">面談記録検索</h6>
                </div>
                <div class="card-body">
                  <%= form_with url: admin_school_teacher_meeting_records_path(@school, @teacher), method: :get, local: true do %>
                    <div class="mb-3">
                      <%= label_tag :student_name, '生徒名', class: 'form-label' %>
                      <%= text_field_tag :student_name, params[:student_name], class: 'form-control', placeholder: '生徒名を入力' %>
                    </div>
                    <div class="mb-3">
                      <%= label_tag :date_range, '日付範囲', class: 'form-label' %>
                      <div class="row">
                        <div class="col-6">
                          <%= date_field_tag :start_date, params[:start_date], class: 'form-control', id: 'start_date_field' %>
                          <div id="start_date_error" class="invalid-feedback"></div>
                        </div>
                        <div class="col-6">
                          <%= date_field_tag :end_date, params[:end_date], class: 'form-control', id: 'end_date_field' %>
                          <div id="end_date_error" class="invalid-feedback"></div>
                        </div>
                      </div>
                    </div>
                    <div class="mb-3">
                      <%= label_tag :keyword, 'キーワード', class: 'form-label' %>
                      <%= text_field_tag :keyword, params[:keyword], class: 'form-control', placeholder: 'キーワードを入力' %>
                    </div>
                    <div class="d-flex gap-2">
                      <%= submit_tag '検索', class: 'btn btn-primary flex-grow-1' %>
                      <% if params[:student_name].present? || params[:start_date].present? || params[:end_date].present? || params[:keyword].present? %>
                        <%= link_to 'クリア', admin_school_teacher_meeting_records_path(@school, @teacher), class: 'btn btn-outline-secondary' %>
                      <% end %>
                    </div>
                  <% end %>
                </div>
              </div>
            </div>
            <div class="col-md-8">
              <div class="card mb-4">
                <div class="card-header bg-light">
                  <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">面談記録一覧</h6>
                    <% if params[:student_name].present? || params[:start_date].present? || params[:end_date].present? || params[:keyword].present? %>
                      <span class="badge bg-info">検索結果: <%= @records.total_count %>件</span>
                    <% end %>
                  </div>
                  <% if params[:student_name].present? || params[:start_date].present? || params[:end_date].present? || params[:keyword].present? %>
                    <div class="mt-2 small">
                      <% if params[:student_name].present? %>
                        <span class="badge bg-light text-dark me-2">生徒名: <%= params[:student_name] %></span>
                      <% end %>
                      <% if params[:start_date].present? || params[:end_date].present? %>
                        <span class="badge bg-light text-dark me-2">日付: <%= params[:start_date] %> ~ <%= params[:end_date] %></span>
                      <% end %>
                      <% if params[:keyword].present? %>
                        <span class="badge bg-light text-dark me-2">キーワード: <%= params[:keyword] %></span>
                      <% end %>
                    </div>
                  <% end %>
                </div>
                <div class="card-body p-0 event-list">
                  <div class="list-group list-group-flush">
                    <% if @records.present? %>
                      <% @records.each do |record| %>
                        <a href="#" class="list-group-item list-group-item-action" data-bs-toggle="modal" data-bs-target="#recordModal<%= record.id %>">
                          <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">
                              <%= record.student_name %>
                              <% if record.approved? %>
                                <span class="badge bg-success"><%= I18n.t("meeting.event.status.#{record.status}") %></span>
                              <% elsif record.rejected? || record.canceled? %>
                                <span class="badge bg-danger"><%= I18n.t("meeting.event.status.#{record.status}") %></span>
                              <% else %>
                                <span class="badge bg-warning text-dark"><%= I18n.t("meeting.event.status.#{record.status}") %></span>
                              <% end %>
                            </h6>
                            <small class="text-muted"><%= record.date.strftime('%-m/%-d %H:%M') %></small>
                          </div>
                          <p class="mb-1"><%= record&.meeting_category&.name %></p>
                          <small class="text-muted">次回：<%= record.next_scheduled&.strftime('%-m/%-d') %>予定</small>
                        </a>
                      <% end %>
                    <% else %>
                      <p class="text-center text-muted p-4">面談記録がありません</p>
                    <% end %>
                  </div>
                </div>
                <% if @records.present? && @records.total_pages > 1 %>
                  <div class="card-footer">
                    <div class="d-flex justify-content-center">
                      <%= paginate @records %>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        </div>

        <% if @records.present? %>
          <% @records.each do |record| %>
            <div class="modal fade" id="recordModal<%= record.id %>" tabindex="-1" aria-labelledby="recordModalLabel<%= record.id %>" aria-hidden="true" data-bs-backdrop='static'>
              <div class="modal-dialog modal-lg">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title" id="recordModalLabel<%= record.id %>">面談記録：<%= record.student_name %> - <%= record.date.strftime('%-m/%-d %H:%M') %> - <%= record.id %></h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                  <div class="row">
                    <div class="col-md-6">
                      <h6>基本情報</h6>
                      <table class="table table-sm">
                        <tr>
                          <th style="width: 30%">生徒名</th>
                          <td><%= record.student_name %></td>
                        </tr>
                        <tr>
                          <th>日時</th>
                          <td><%= record.date.strftime('%Y/%m/%d %H:%M') %></td>
                        </tr>
                        <tr>
                          <th>内容</th>
                          <td><%= record&.meeting_category&.name %></td>
                        </tr>
                        <tr>
                          <th>評価</th>
                          <td><span class="text-warning"><%= '★' * record.rating %><%= '☆' * (5 - record.rating) %></span></td>
                        </tr>
                      </table>
                    </div>
                    <div class="col-md-6">
                      <h6>生徒情報</h6>
                      <table class="table table-sm">
                        <tr>
                          <th style="width: 30%">学年</th>
                          <td><%= record.grade %></td>
                        </tr>
                        <tr>
                          <th>過去の面談回数</th>
                          <td><%= record.past_meetings_count %>回</td>
                        </tr>
                        <tr>
                          <th>最初の面談</th>
                          <td><%= record.first_meeting_date&.strftime('%Y/%m/%d %H:%M') %></td>
                        </tr>
                        <tr>
                          <th>次回予定</th>
                          <td><%= record.next_scheduled&.strftime('%Y/%m/%d %H:%M') %></td>
                        </tr>
                      </table>
                    </div>
                  </div>
                  <div class="row mt-3">
                    <div class="col-12">
                      <h6 class="d-flex justify-content-between align-items-center">
                        面談メモ
                        <button class="btn btn-sm btn-outline-primary edit-memo-btn" data-record-id="<%= record.id %>">
                          <i class="fas fa-edit"></i> 編集
                        </button>
                        <button class="btn btn-sm btn-success save-memo-btn d-none" data-record-id="<%= record.id %>">
                          <i class="fas fa-save"></i> 保存
                        </button>
                      </h6>
                      <div class="border rounded p-3 bg-light memo-display" id="memo-display-<%= record.id %>">
                        <%= simple_format(record.memo.presence || "メモはありません") %>
                      </div>
                      <textarea class="form-control mt-2 d-none memo-editor" id="memo-editor-<%= record.id %>" rows="4"><%= record.memo %></textarea>
                    </div>
                  </div>
                  <div class="row mt-3">
                    <div class="col-md-6">
                      <h6 class="d-flex justify-content-between align-items-center">
                        課題・宿題
                        <button class="btn btn-sm btn-outline-primary edit-homework-btn" data-record-id="<%= record.id %>">
                          <i class="fas fa-edit"></i> 編集
                        </button>
                        <button class="btn btn-sm btn-success save-homework-btn d-none" data-record-id="<%= record.id %>">
                          <i class="fas fa-save"></i> 保存
                        </button>
                      </h6>
                      <div class="border rounded p-3 bg-light homework-display" id="homework-display-<%= record.id %>">
                        <% if record.homework.present? %>
                          <ul class="mb-0">
                            <% begin %>
                              <% JSON.parse(record.homework).each do |homework| %>
                                <li style="list-style-type: initial;"><%= homework %></li>
                              <% end %>
                            <% rescue JSON::ParserError %>
                              <li>Invalid homework data</li>
                            <% end %>
                          </ul>
                        <% else %>
                          課題・宿題はありません
                        <% end %>
                      </div>
                      <% if record.homework.present? %>
                        <textarea class="form-control mt-2 d-none homework-editor" id="homework-editor-<%= record.id %>" rows="4"><%= record.homework.present? ? JSON.parse(record.homework).join("\n") : "" %></textarea>
                      <% else %>
                        <textarea class="form-control mt-2 d-none homework-editor" id="homework-editor-<%= record.id %>" rows="4"></textarea>
                      <% end %>
                    </div>
                    <div class="col-md-6">
                      <h6>次回予定</h6>
                      <div class="border rounded p-3 bg-light">
                        <p class="mb-0"><%= record.next_scheduled&.strftime('%Y/%m/%d %H:%M') %></p>
                      </div>
                    </div>
                  </div>
                  <div class="row mt-3">
                    <div class="col-12">
                      <h6>生徒からのフィードバック</h6>
                      <% if record.feedback_summary.present? && record.feedback_summary.values.compact.present? %>
                        <% record.feedback_summary.each do |key, value| %>
                          <% if value.present? %>
                            <div class="mb-3">
                              <div class="fw-bold"><%= I18n.t("meeting.#{key}") %></div>
                              <div class="border rounded p-3 bg-light">
                                <p class="mb-0" style="white-space: break-spaces;"><%= sanitize(value) %></p>
                              </div>
                            </div>
                          <% end %>
                        <% end %>
                      <% else %>
                        <p class="text-center text-muted p-4">フィードバックがありません</p>
                      <% end %>
                    </div>
                  </div>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">閉じる</button>
                </div>
              </div>
              </div>
            </div>
          <% end %>
        <% end %>
      </div>
    </div>
  </div>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Date range validation
      const startDateField = document.getElementById('start_date_field');
      const endDateField = document.getElementById('end_date_field');
      const startDateError = document.getElementById('start_date_error');
      const endDateError = document.getElementById('end_date_error');
      const searchForm = document.querySelector('form[action*="meeting_records"]');

      // Set min date for end_date based on start_date
      function updateEndDateMin() {
        if (startDateField.value) {
          endDateField.min = startDateField.value;

          // If end_date is less than start_date, update it
          if (endDateField.value && endDateField.value < startDateField.value) {
            endDateField.value = startDateField.value;
          }
        } else {
          endDateField.min = '';
        }
      }

      // Validate date range before form submission
      function validateDateRange() {
        let isValid = true;

        // Reset error messages
        startDateField.classList.remove('is-invalid');
        endDateField.classList.remove('is-invalid');
        startDateError.textContent = '';
        endDateError.textContent = '';

        // If both dates are provided, validate the range
        if (startDateField.value && endDateField.value) {
          if (startDateField.value > endDateField.value) {
            endDateField.classList.add('is-invalid');
            endDateError.textContent = '終了日は開始日以降の日付を選択してください。';
            isValid = false;
          }
        }

        return isValid;
      }

      // Add event listeners
      if (startDateField && endDateField) {
        startDateField.addEventListener('change', updateEndDateMin);

        // Initialize min date for end_date
        updateEndDateMin();

        // Validate form before submission
        if (searchForm) {
          searchForm.addEventListener('submit', function(event) {
            if (!validateDateRange()) {
              event.preventDefault();
            }
          });
        }
      }

      // Memo edit/save functionality
      const editMemoBtns = document.querySelectorAll('.edit-memo-btn');
      const saveMemoBtns = document.querySelectorAll('.save-memo-btn');

      editMemoBtns.forEach(btn => {
        btn.addEventListener('click', function() {
          const recordId = this.dataset.recordId;
          const memoDisplay = document.getElementById(`memo-display-${recordId}`);
          const memoEditor = document.getElementById(`memo-editor-${recordId}`);

          memoDisplay.classList.add('d-none');
          memoEditor.classList.remove('d-none');
          this.classList.add('d-none');
          document.querySelector(`.save-memo-btn[data-record-id="${recordId}"]`).classList.remove('d-none');
        });
      });

      saveMemoBtns.forEach(btn => {
        btn.addEventListener('click', function() {
          const recordId = this.dataset.recordId;
          const memoDisplay = document.getElementById(`memo-display-${recordId}`);
          const memoEditor = document.getElementById(`memo-editor-${recordId}`);
          const memoContent = memoEditor.value;

          // AJAX request to save memo
          $.ajax({
            url: `<%= admin_school_teacher_meeting_events_path(@school, @teacher) %>/${recordId}`,
            type: 'PATCH',
            data: { meeting_event: { memo: memoContent } },
            success: function(response) {
              // Update display with new content
              memoDisplay.innerHTML = memoContent ? memoContent.replace(/\n/g, '<br>') : 'メモはありません';

              // Switch back to display mode
              memoDisplay.classList.remove('d-none');
              memoEditor.classList.add('d-none');
              document.querySelector(`.edit-memo-btn[data-record-id="${recordId}"]`).classList.remove('d-none');
              document.querySelector(`.save-memo-btn[data-record-id="${recordId}"]`).classList.add('d-none');
            },
            error: function(xhr, status, error) {
              alert('保存に失敗しました: ' + error);
            }
          });
        });
      });

      // Homework edit/save functionality
      const editHomeworkBtns = document.querySelectorAll('.edit-homework-btn');
      const saveHomeworkBtns = document.querySelectorAll('.save-homework-btn');

      editHomeworkBtns.forEach(btn => {
        btn.addEventListener('click', function() {
          const recordId = this.dataset.recordId;
          const homeworkDisplay = document.getElementById(`homework-display-${recordId}`);
          const homeworkEditor = document.getElementById(`homework-editor-${recordId}`);

          homeworkDisplay.classList.add('d-none');
          homeworkEditor.classList.remove('d-none');
          this.classList.add('d-none');
          document.querySelector(`.save-homework-btn[data-record-id="${recordId}"]`).classList.remove('d-none');
        });
      });

      saveHomeworkBtns.forEach(btn => {
        btn.addEventListener('click', function() {
          const recordId = this.dataset.recordId;
          const homeworkDisplay = document.getElementById(`homework-display-${recordId}`);
          const homeworkEditor = document.getElementById(`homework-editor-${recordId}`);
          const homeworkContent = homeworkEditor.value;
          const homeworkArray = homeworkContent.split('\n').filter(item => item.trim() !== '');

          // AJAX request to save homework
          $.ajax({
            url: `<%= admin_school_teacher_meeting_events_path(@school, @teacher) %>/${recordId}`,
            type: 'PATCH',
            data: { meeting_event: { homework: homeworkArray.length ? homeworkArray : [""] } },
            success: function(response) {
              // Update display with new content
              if (homeworkArray.length > 0) {
                let htmlContent = '<ul class="mb-0">';
                homeworkArray.forEach(function(hw) {
                  htmlContent += `<li style="list-style-type: initial;">${hw}</li>`;
                });
                htmlContent += '</ul>';
                homeworkDisplay.innerHTML = htmlContent;
              } else {
                homeworkDisplay.innerHTML = '課題・宿題はありません';
              }

              // Switch back to display mode
              homeworkDisplay.classList.remove('d-none');
              homeworkEditor.classList.add('d-none');
              document.querySelector(`.edit-homework-btn[data-record-id="${recordId}"]`).classList.remove('d-none');
              document.querySelector(`.save-homework-btn[data-record-id="${recordId}"]`).classList.add('d-none');
            },
            error: function(xhr, status, error) {
              alert('保存に失敗しました: ' + error);
            }
          });
        });
      });
    });
  </script>
<% end %>
