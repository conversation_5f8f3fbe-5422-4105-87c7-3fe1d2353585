<%= content_for :title, '面談予約システム - スケジュール管理' %>
<%= render 'layouts/admin_meeting' do %>
  <div class="row py-3 border-bottom">
    <div class="col d-flex justify-content-between align-items-center">
      <h4 class="mb-0">スケジュール管理</h4>
    </div>
  </div>
  <div class="p-3">
    <div class="row">
      <div class="col-md-4">
        <div class="card mb-4">
          <div class="card-header bg-light">
            <h6 class="mb-0">定期的な空き時間設定</h6>
          </div>
          <div class="card-body">
            <%= render 'form', work_time: @work_time_circle %>
          </div>
        </div>

        <div class="card">
          <div class="card-header bg-light">
            <h6 class="mb-0">個別日時の調整</h6>
          </div>
          <div class="card-body">
            <%= render 'form', work_time: @work_time_one_time %>
          </div>
        </div>
      </div>

      <div class="col-md-8">
        <div class="card">
          <div class="card-header bg-light">
            <h6 class="mb-0">スケジュールカレンダー</h6>
          </div>
          <div class="card-body">
            <div class="row align-items-center mb-3">
              <div class="col">
                <h5 class="mb-0"><%= l @month, format: :short_year_and_month %></h5>
              </div>
              <div class="col-auto">
                <div class="btn-group">
                  <%= link_to admin_school_teacher_meeting_work_times_path(@school, @teacher, params: { month: @month.prev_month }), class: "btn btn-outline-secondary btn-sm" do %>
                    <i class="fas fa-chevron-left"></i>
                  <% end %>
                  <%= link_to admin_school_teacher_meeting_work_times_path(@school, @teacher, params: { month: @month.next_month }), class: "btn btn-outline-secondary btn-sm" do %>
                    <i class="fas fa-chevron-right"></i>
                  <% end %>
                </div>
              </div>
            </div>

            <table class="table table-bordered text-center" style="table-layout: fixed;">
              <thead>
                <tr>
                  <th>月</th>
                  <th>火</th>
                  <th>水</th>
                  <th>木</th>
                  <th>金</th>
                  <th class="text-danger">土</th>
                  <th class="text-danger">日</th>
                </tr>
              </thead>
              <tbody>
              <% @schedule.each_slice(7).each do |week| %>
                <tr>
                  <% week.each.with_index do |(date, values), index| %>
                    <td class="<%= index > 4 ? 'text-danger' : 'text-muted' %>">
                      <%= date.day %>

                      <% if Meeting::Holiday.holiday? date %>
                        <div class="mt-1">
                          <span class="badge bg-secondary">休み</span>
                        </div>
                      <% end %>

                      <% if values.empty? && index > 4 %>
                        <div class="mt-1">
                          <span class="badge bg-danger">休み</span>
                        </div>
                      <% end %>

                      <% values.each do |period| %>
                        <div class="mt-1">
                          <span class="badge bg-success"><%= period[0] %>-<%= period[1] %></span>
                        </div>
                      <% end %>
                    </td>
                  <% end %>
                </tr>
              <% end %>
              </tbody>
            </table>

            <div class="mt-3">
              <div class="d-flex align-items-center mb-1">
                <span class="badge bg-success me-2">&nbsp;</span>
                <span>利用可能</span>
              </div>
              <div class="d-flex align-items-center mb-1">
                <span class="badge bg-danger me-2">&nbsp;</span>
                <span>休み</span>
              </div>
              <div class="d-flex align-items-center">
                <span class="badge bg-primary me-2">&nbsp;</span>
                <span>予約あり</span>
              </div>
            </div>
          </div>
        </div>

        <div class="card mt-4">
          <div class="card-header bg-light">
            <h6 class="mb-0">登録済み空き時間</h6>
          </div>
          <div class="card-body p-0">
            <ul class="list-group list-group-flush">
              <% if @work_times.present? %>
                <% @work_times.each do |time| %>
                  <% next if time.new_record? %>

                  <li class="list-group-item d-flex justify-content-between align-items-center">
                    <div>
                      <h6 class="mb-0"><%= time.display_wdays %> (<%= time.display_work_type %>)</h6>
                      <small class="text-muted"><%= time.start_time %>-<%= time.end_time %></small>
                    </div>

                    <div>
                      <!-- Modal -->
                      <div class="modal fade" id="editModal_<%= time.id %>" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true" data-bs-backdrop='static'>
                        <div class="modal-dialog">
                          <div class="modal-content">
                            <div class="modal-header">
                              <h5 class="modal-title"><%= time.circle? ? "定期的な空き時間設定" : "個別日時の調整" %></h5>
                              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body mb-3">
                              <%= render 'form', work_time: time %>
                            </div>
                          </div>
                        </div>
                      </div>

                      <%= link_to '<i class="fas fa-edit"></i>'.html_safe, "#editModal_#{time.id}", class: 'btn btn-sm btn-outline-primary me-1', title: '編集', data: { bs_toggle: 'modal' } %>
                      <%= link_to '<i class="fas fa-trash"></i>'.html_safe, admin_school_teacher_meeting_work_time_path(@school, @teacher, time), method: :delete, data: { confirm: '本当に削除しますか？' }, class: 'btn btn-sm btn-outline-danger', title: '削除' %>
                    </div>
                  </li>
                <% end %>
              <% else %>
                <li class="list-group-item text-center text-muted p-4">空き時間がありません</li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
<% end %>

<script>
  $(document).ready(function() {
    $('.input-start-time').on('change', function() {
      const $start = $(this)
      const $end = $start.closest('form').find('.input-end-time')

      const startValue = $start.val()
      if (startValue) {
        $end.find('option').each((_, option) => {
          option.disabled = option.value <= startValue;
        })
      } else {
        $end.find('option').each((_, option) => {
          if (option.value) {
            option.disabled = true
          } else {
            option.selected = true
          }
        })
      }
    }).trigger('change')


    $('.btn-submit-circle').on('click', function(e) {
      e.preventDefault()

      const $this = $(this)
      const $form = $this.closest('form')

      if ($form.find('.form-check-input:checked').length === 0) {
        alert('日付を入力してください。')
        return
      }

      const $startTime = $form.find('.input-start-time');
      const $endTime = $form.find('.input-end-time');

      if (!validateStartEndTime($startTime, $endTime)) {
        return;
      }

      $form.submit()
    })

    $('.btn-submit-one-time').on('click', function(e) {
      e.preventDefault()

      const $this = $(this)
      const $form = $this.closest('form')

      const $date = $form.find('.input-date')

      if (!$date.val()) {
        alert('日付を空白のままにすることはできません。')
        return
      }

      if (new Date($date.val()).setHours(0, 0, 0, 0) < new Date().setHours(0, 0, 0, 0)) {
        alert('You cannot select a date in the past.')
        return
      }

      const $startTime = $form.find('.input-start-time');
      const $endTime = $form.find('.input-end-time');

      if (!$startTime.val()) {
        alert('開始時間を入力してください。');
        $startTime.focus();
        return;
      }

      if (!$endTime.val()) {
        alert('終了時間を入力してください。');
        $endTime.focus();
        return;
      }

      if (!validateStartEndTime($startTime, $endTime)) {
        return;
      }

      $form.submit()
    })

    const validateStartEndTime = function($startTime, $endTime) {
      if ($startTime.val() && $endTime.val()) {
        const convertToMinutes = function(timeStr) {
          const parts = timeStr.split(':').map(Number);
          return parts[0] * 60 + parts[1];
        };

        const startMinutes = convertToMinutes($startTime.val());
        const endMinutes = convertToMinutes($endTime.val());

        // Check if start time is equal to or later than end time
        if (startMinutes >= endMinutes) {
          alert('開始時間は終了時間より前である必要があります。');
          return false;
        }
      }
      return true;
    };
  })
</script>
