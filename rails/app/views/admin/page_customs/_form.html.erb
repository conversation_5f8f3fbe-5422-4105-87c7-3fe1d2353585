<% simple_mde_class = "simple-mde-editor-page" %>

<%= form_for [:admin, @school, page_custom], multipart: true, remote: true do |form| %>
  <div class="mdc-layout-grid py-3">
    <div class="mdc-layout-grid__inner">
      <div class="mdc-layout-grid__cell--span-7">
        <a href="<%= url_for([:admin, @school, :page_customs]) %>" type="button" class="mdc-button mdc-button--outlined me-2">
          <i class="material-icons">west</i>
        </a>
        <%= page_custom.title %>
      </div>
      <div class="mdc-layout-grid__cell--span-5 text-end">
        <label class="mdc-typography--headline7 save-status text-success me-2 d-none"></label>
        <%= form.submit class: "mdc-button mdc-button--raised", onclick: "onSumbmitClick()", id: "submit_page_btn"%>
        <%# <label class="switch" role="group" onclick="_submit()"> %>
          <%#= form.check_box :published, class: "switch-input published-btn" %>
          <%# <span class="switch-label" data-off="非公開" data-on="公開中"></span> %>
          <%# <span class="switch-handle"></span> %>
        <%# </label> %>
      </div>
    </div>
  </div>

<div style="display:none">
  <div class="row">
    <div class="col-lg-12 d-flex justify-content-end p-0">
      <div class="publish-group">
        <label class="switch" role="group">
          <%= form.check_box :published_at, checked: @page_custom.published_at.present? , class: "switch-input published-btn" %>
          <span class="switch-label" data-off="非公開" data-on="公開中"></span>
          <span class="switch-handle"></span>
        </label>
      </div>
      <a
          data-toggle="tooltip"
          data-placement="top"
          title="ブラウザで開く"
          href="<%= page_link(@page_custom) %>"
          target="_blank"
          class="btn ms-2"
      >
          <i class="fas fa-external-link-alt"></i>
      </a>
      <span id="save-success-text" class="text-success m-2 d-none">保存しました</span>
      <button id="submit-btn" class="mdc-button mdc-button--raised">
        <span class="mdc-button__ripple"></span>更新する
      </button>
    </div>
  </div>

  <% if page_custom.errors.any? %>
    <div id="error_explanation">
      <ul>
        <% page_custom.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>
</div>

<div class="mdc-layout-grid py-0">
  <div class="mdc-layout-grid__inner">
    <div class="mdc-layout-grid__cell--span-8 page-form">
      <div class="mdc-card mdc-card--outlined p-3">
        <% if page_custom.errors.any? %>
          <div id="error_explanation">
            <ul>
            <% page_custom.errors.full_messages.each do |message| %>
              <li><%= message %></li>
            <% end %>
            </ul>
          </div>
        <% end %>
        <label class="mdc-text-field mdc-text-field--outlined mt-2">
          <span class="mdc-notched-outline">
            <span class="mdc-notched-outline__leading"></span>
            <span class="mdc-notched-outline__notch">
              <span class="mdc-floating-label">タイトル</span>
            </span>
            <span class="mdc-notched-outline__trailing"></span>
          </span>
          <%= form.text_field :title, id: :page_title, class: "mdc-text-field__input" %>
        </label>
        <label class="mdc-text-field mdc-text-field--outlined mt-2">
          <span class="mdc-notched-outline">
            <span class="mdc-notched-outline__leading"></span>
            <span class="mdc-notched-outline__notch">
              <span class="mdc-floating-label">Slug名</span>
            </span>
            <span class="mdc-notched-outline__trailing"></span>
          </span>
          <%= form.text_field :slag_name, id: :page_slag_name, class: "mdc-text-field__input" %>
        </label>
        <div class="mt-2">
          <div class="d-flex w-100 justify-content-between">
            <%= form.label :design_content %>
            <div class="d-flex align-items-center">
              <%= form.radio_button :body_type, 0, class: 'body_type_md' %> SimpleMde<br />
              <%= form.radio_button :body_type, 1, class: 'body_type_ck ms-2' %> CkEditor<br />
            </div>
          </div>
          <div class="md_body_editor">
            <%= form.text_area :design_content, id: :design_content, class: "#{simple_mde_class}" %>
          </div>
          <div class="ck_body_editor custom-ckeditor-box">
            <%= form.text_area :ck_body, id: "page-setting-ck", class: "custom-ckeditor", row: 10 %>
          </div>
        </div>
        <div class="mdc-checkbox">
          <%= form.check_box :is_thanks_page, class: "mdc-checkbox__native-control" %>
          <div class="mdc-checkbox__background">
            <svg class="mdc-checkbox__checkmark"
                viewBox="0 0 24 24">
              <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
            </svg>
            <div class="mdc-checkbox__mixedmark"></div>
          </div>
          <div class="mdc-checkbox__ripple"></div>
          <label class="mt-2" for="banner_is_not_show_banner_for_premium" style="margin-left: 30px;">サンクスページ</label>
        </div>
      </div>
    </div>
    <div class="mdc-layout-grid__cell--span-4">
      <div class="mdc-card mdc-card--outlined p-3 mb-3">
        <div class="mdc-layout-grid w-100 py-0">
          <div class="mdc-layout-grid__inner">
            <div class="mdc-layout-grid__cell--span-6">
              <label class="mdc-typography--subtitle1">公開/未公開</label>
            </div>
            <div class="mdc-layout-grid__cell--span-6 text-right">
              <%= link_to "#{page_link(page_custom)}?preview=true", class: "mdc-button mdc-button--outlined", target: "_blank" do %>
                <span class="mdc-button__ripple"></span>
                <span class="mdc-button__label">プレビュー</span>
              <% end %>
            </div>
          </div>
        </div>
        <div class="mdc-form-field">
          <div class="mdc-radio">
            <%= form.radio_button :published_at, "1", checked: page_custom.published_at.present?, class: "mdc-radio__native-control" %>
            <div class="mdc-radio__background">
              <div class="mdc-radio__outer-circle"></div>
              <div class="mdc-radio__inner-circle"></div>
            </div>
            <div class="mdc-radio__ripple"></div>
          </div>
          <label class="mt-2" for="blog_published_at_1">公開</label>
        </div>
        <div class="mdc-form-field mb-3">
          <div class="mdc-radio">
            <%= form.radio_button :published_at, "0", checked: !page_custom.published_at.present?, class: "mdc-radio__native-control" %>
            <div class="mdc-radio__background">
              <div class="mdc-radio__outer-circle"></div>
              <div class="mdc-radio__inner-circle"></div>
            </div>
            <div class="mdc-radio__ripple"></div>
          </div>
          <label class="mt-2" for="blog_published_at_0">未公開</label>
        </div>
        <label id="show-public-date" class="mdc-typography--subtitle1 mb-2">公開日を設定する</label>
        <div id="public-date-group" class="d-none">
          <label class="mdc-text-field mdc-text-field--outlined w-100 mb-3">
            <span class="mdc-notched-outline">
              <span class="mdc-notched-outline__leading"></span>
              <span class="mdc-notched-outline__notch">
                <span class="mdc-floating-label">公開日</span>
              </span>
              <span class="mdc-notched-outline__trailing"></span>
            </span>
            <%= text_field_tag :published_date, "", class: "mdc-text-field__input" %>
          </label>
          <label class="mdc-text-field mdc-text-field--outlined w-100">
            <span class="mdc-notched-outline">
              <span class="mdc-notched-outline__leading"></span>
              <span class="mdc-notched-outline__notch">
                <span class="mdc-floating-label">公開時</span>
              </span>
              <span class="mdc-notched-outline__trailing"></span>
            </span>
            <%= text_field_tag :published_time, "", class: "mdc-text-field__input" %>
          </label>
        </div>
      </div>

      <div class="mdc-card mdc-card--outlined p-3 mb-3">
        <label class="mdc-typography--subtitle1 mb-2">画像</label>
        <div class="row">
          <div class="col-md-4">
            <%= get_image(@page_custom) %>
          </div>
          <div class="col-md-8">
            <%= form.file_field :image %>
            <%= form.hidden_field :image_cache %>    
          </div>
        </div>
      </div>
      <div class="mdc-card mdc-card--outlined p-3 mb-3">
        <label class="mdc-typography--subtitle1 mb-2">概要</label>
        <div class="row">
          <div class="form-group">
            <%= form.text_area :description, class: "form-control", rows: 6  %>
          </div>
          
        </div>
      </div>

      <div class="mdc-card mdc-card--outlined p-3 mb-3">
        <div class="mdc-select mdc-select--outlined mb-3">
          <%= form.hidden_field :parent_id %>
          <div class="mdc-select__anchor" aria-labelledby="outlined-select-label">
            <span class="mdc-notched-outline">
              <span class="mdc-notched-outline__leading"></span>
              <span class="mdc-notched-outline__notch">
                <span id="outlined-select-label" class="mdc-floating-label">親ページ</span>
              </span>
              <span class="mdc-notched-outline__trailing"></span>
            </span>
            <span class="mdc-select__selected-text-container">
              <span id="demo-selected-text" class="mdc-select__selected-text"></span>
            </span>
            <span class="mdc-select__dropdown-icon">
              <svg
                class="mdc-select__dropdown-icon-graphic"
                viewBox="7 10 10 5" focusable="false">
                <polygon
                  class="mdc-select__dropdown-icon-inactive"
                  stroke="none"
                  fill-rule="evenodd"
                  points="7 10 12 15 17 10">
                </polygon>
                <polygon
                  class="mdc-select__dropdown-icon-active"
                  stroke="none"
                  fill-rule="evenodd"
                  points="7 15 12 10 17 15">
                </polygon>
              </svg>
            </span>
          </div>
          <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
            <ul class="mdc-list">
              <li class="mdc-list-item" data-value="0">
                <span class="mdc-list-item__ripple"></span>
                <span class="mdc-list-item__text">親無し</span>
              </li>
              <% @school.page_customs.where.not(id: page_custom.id).each do |parent| %>
                <li class="mdc-list-item" data-value="<%= parent.id %>">
                  <span class="mdc-list-item__ripple"></span>
                  <span class="mdc-list-item__text"><%= parent.title %></span>
                </li>
              <% end %>
            </ul>
          </div>
        </div>
        <div class="mdc-select mdc-select--outlined mb-3">
          <%= hidden_field_tag :page_template_id, page_custom.page_template(@school.school_design)&.id %>
          <div class="mdc-select__anchor" aria-labelledby="outlined-select-label">
            <span class="mdc-notched-outline">
              <span class="mdc-notched-outline__leading"></span>
              <span class="mdc-notched-outline__notch">
                <span id="outlined-select-label" class="mdc-floating-label">テンプレート</span>
              </span>
              <span class="mdc-notched-outline__trailing"></span>
            </span>
            <span class="mdc-select__selected-text-container">
              <span id="demo-selected-text" class="mdc-select__selected-text"></span>
            </span>
            <span class="mdc-select__dropdown-icon">
              <svg
                class="mdc-select__dropdown-icon-graphic"
                viewBox="7 10 10 5" focusable="false">
                <polygon
                  class="mdc-select__dropdown-icon-inactive"
                  stroke="none"
                  fill-rule="evenodd"
                  points="7 10 12 15 17 10">
                </polygon>
                <polygon
                  class="mdc-select__dropdown-icon-active"
                  stroke="none"
                  fill-rule="evenodd"
                  points="7 15 12 10 17 15">
                </polygon>
              </svg>
            </span>
          </div>
          <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
            <ul class="mdc-list">
              <li class="mdc-list-item" data-value="0">
                <span class="mdc-list-item__ripple"></span>
                <span class="mdc-list-item__text">デフォルト</span>
              </li>
              <% @school.school_design.page_templates.each do |template| %>
                <li class="mdc-list-item" data-value="<%= template.id %>">
                  <span class="mdc-list-item__ripple"></span>
                  <span class="mdc-list-item__text"><%= template.title %></span>
                </li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<% end %>

<% content_for :bottom_script do %>
  <script>
    function _submit() {
      setTimeout(function() {
        $("#submit_page_btn").click();
      }, 1000);
    }

    function onSumbmitClick() {
      $(".save-status").css("color", "darkgray").text("保存中");
    }

    $(document).ready(function() {
      // Check exist simpleMDE on screen
      if ($(".CodeMirror").length === 0) {
        addSimpleMdebyClass(".<%= simple_mde_class %>", function() {
          $("#submit_page_btn").click();
        });
      }

      $("#show-public-date").click(function(e) {
        e.preventDefault();
        $("#public-date-group").toggleClass("d-none");
      });


      ClassicEditor
        .create(document.querySelector('#page-setting-ck'), {
          licenseKey: '',
          simpleUpload: {
            // Upload the images to the server using the CKFinder QuickUpload command.
            uploadUrl: '/pictures'
          }
        })
        .then(editor => {
          window.editor = editor;
        })
        .catch(error => {
          console.error('Oops, something went wrong!');
          console.error(
            'Please, report the following error on https://github.com/ckeditor/ckeditor5/issues with the build id and the error stack trace:'
            );
          console.warn('Build id: lxq9hpkltlpe-1aze6kf9qb0d');
          console.error(error);
        });
        
      $(".body_type_md").click(function(e) {
        update_type_editor()
      });
      $(".body_type_ck").click(function(e) {
        update_type_editor()
      });

      function update_type_editor() {
        if($(".body_type_md").is(':checked')){
          $(".md_body_editor").show()
          $(".ck_body_editor").hide()
        } else {
          $(".md_body_editor").hide()
          $(".ck_body_editor").show()
        }
      }
      update_type_editor()
      

    });

    setupAutoSave()
    
  </script>
<% end %>
