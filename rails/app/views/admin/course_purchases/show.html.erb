<div class="mb-5">
  <div class="mdc-layout-grid py-3">
    <div class="mdc-layout-grid__inner">
      <div class="mdc-layout-grid__cell--span-6">
        <a href="<%= url_for([:admin, @school, :courses]) %>" type="button" class="mdc-button mdc-button--outlined me-2">
          <i class="material-icons">west</i>
        </a>
        <%= @course.name %>
      </div>
    </div>
  </div>
  <%= render "admin/courses/course_info" %>
  <div class="mdc-card mdc-card--outlined">
    <div class="mdc-layout-grid w-100 p-3">
      <div class="mdc-layout-grid__inner">
        <div class="mdc-layout-grid__cell--span-6 px-2">
          <h4>購入ユーザー</h4>
        </div>
      </div>
    </div>
    <div>
      <div class="row">
        <div class="col-md-9">
          <table class="table table-bordered table-striped">
            <tr>
              <th>ユーザー</th>
              <td>
                <%= get_user_image_path(@course_purchase.user, 20, 20, "img-thumbnail rounded-circle user-image-avatar-25") %>
                <%= link_to @course_purchase.user.name, [:admin, @school, @course_purchase.user], target: '_blank' %>
              </td>
            </tr>
            <tr>
              <th>支払い方法</th>
              <td>
                <span class="mdc-fab__icon material-icons">credit_card</span>
              </td>
            </tr>
            <tr>
              <th>作成日</th>
              <td><%= nichiji @course_purchase.start_at %></td>
            </tr>
            <tr>
              <th>値段</th>
              <td><%= @course_purchase.price %></td>
            </tr>
            <tr>
              <th>クーポン</th>
                <td>
                <% if @course_purchase.order_item&.coupon %>
                  <% if @course_purchase.order_item&.discount_price.present? %>
                    <%= -(@course_purchase.order_item.discount_price) %> (<%= @course_purchase.order_item.coupon.code %>)
                  <% end %>
                <% end %>
                </td>
            </tr>
            <tr>
              <th>Tax Price</th>
                <td>
                  <%=(@course_purchase.order_item&.tax_price) %>
                </td>
            </tr>
            <tr>
              <th>お支払い済み</th>
              <td><%= @course_purchase.paied_price %></td>
            </tr>
            <tr>
              <th>支払い状態</th>
              <td>
                <%= render 'common/course_purchase_status', course_purchase: @course_purchase %>
              </td>
            </tr>
            <% if !@course_purchase.stoped? %>
            <tr>
              <th>支払い状態</th>
              <td>
                <%= form_with(model: [:admin, @school, @course, @course_purchase], local: true) do |f| %>
                <%= f.hidden_field :status, :value => CoursePurchase::STATUS_STOPED %>
                <%= f.submit "契約停止", class: "btn btn-primary btn-sm", data: {confirm: "本当によろしいですか？"}  %>
                <% end %>
              </td>
            </tr>
            <% end %>
          </table>
        </div>
      </div>
    </div>
  </div>
  <div class="mdc-card mdc-card--outlined">
    <div class="mdc-layout-grid w-100 p-3">
      <div class="mdc-layout-grid__inner">
        <div class="mdc-layout-grid__cell--span-6 px-2">
          <h4>購入履歴</h4>
        </div>
      </div>
    </div>
    <div>
      <table class="table table-striped">
        <thead>
          <tr>
            <th>日時</th>
            <th>Paied Price</th>
          </tr>
        </thead>
          <% @course_purchase.course_purchase_invoices.each do |course_purchase_invoice| %>
          <tr>
            <td><%= nichiji course_purchase_invoice.paied_at %></td>
            <td><%= course_purchase_invoice.amount_paid %></td>
          </tr>
          <% end %>
        </tbody>
      </table>
    </div>
  </div>
</div>
