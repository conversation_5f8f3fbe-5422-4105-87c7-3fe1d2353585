<div class="form-group">
  <%= f.label "User", class: "fw-bold text-secondary" %>
  <table class="table table-bordered">
    <tbody>
      <tr>
        <td><%= f.check_box :use_user_list %></td>
        <td><%= f.label :use_user_list %></td>
        <td>ユーザーリストを使用する</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_user_blocks %></td>
        <td><%= f.label :use_user_blocks %></td>
        <td>ユーザーブロックを使用する</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_teacher_list %></td>
        <td><%= f.label :use_teacher_list %></td>
        <td>教師リストを使用する</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_iams %></td>
        <td><%= f.label :use_iams %></td>
        <td>IAMsを使用する</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_affiliates %></td>
        <td><%= f.label :use_affiliates %></td>
        <td>アフィリエイトを使用する</td>
      </tr>
      <tr>
        <td><%= f.check_box :allow_user_edit_group %></td>
        <td><%= f.label :allow_user_edit_group %></td>
        <td>ユーザーをグルーピングする機能です。<br>ユーザーのプロフィール画面で「所属」を表示することができます。</td>
      </tr>
      <tr id="registerable">
        <td><%= f.check_box :registerable, id: "registerable_check_box"%></td>
        <td><%= f.label :registerable %></td>
        <td>ユーザーサインアップさせるか。</td>
      </tr>
      <tr id="lazy_sign_up">
        <td><%= f.check_box :lazy_sign_up, id: "lazy_sign_up_check_box"%></td>
        <td><%= f.label :lazy_sign_up %></td>
        <td>メール認証無しでゲストユーザーでサインアップします。</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_mail %></td>
        <td><%= f.label :use_mail %></td>
        <td>メール機能使用させるか</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_skill %></td>
        <td><%= f.label :use_skill %></td>
        <td>スキル機能使用させるか</td>
      </tr>
    </tbody>
  </table>

  <%= f.label "Course", class: "fw-bold text-secondary" %>
  <table class="table table-bordered">
    <tbody>
      <tr>
        <td><%= f.check_box :use_course_lesson %></td>
        <td><%= f.label :use_course_lesson %></td>
        <td>ビデオやテキストのコース・レッスンを作成できます。</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_catalog %></td>
        <td><%= f.label :use_catalog %></td>
        <td>コースカタログを使用する</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_evaluate_lesson, id: "use_evaluate_lesson_check_box" %></td>
        <td><%= f.label :use_evaluate_lesson %></td>
        <td>レッスンの評価を使用する</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_vector_button, id: "use_vector_button_check_box" %></td>
        <td><%= f.label :use_vector_button %></td>
        <td>ベクター生成を使用しますか</td>
      </tr>
    </tbody>
  </table>

  <%= f.label "Exam", class: "fw-bold text-secondary" %>
  <table class="table table-bordered">
    <tbody>
      <tr>
        <td><%= f.check_box :use_exam_prompt %></td>
        <td><%= f.label :use_exam_prompt %></td>
        <td>テストのプロンプトを使用する</td>
      </tr>
      <tr id="use_exam">
        <td><%= f.check_box :use_exam, id: "use_exam_check_box" %></td>
        <td><%= f.label :use_exam %></td>
        <td>テスト使用させるか</td>
      </tr>
      <tr id="use_exam_stock">
        <td><%= f.check_box :use_exam_stock, id: "use_exam_stock_check_box" %></td>
        <td><%= f.label :use_exam_stock %></td>
        <td>テストストック使用させるか</td>
      </tr>
      <tr id="use_exam_recommendation">
        <td><%= f.check_box :use_exam_recommendation, id: "use_exam_recommendation_check_box" %></td>
        <td><%= f.label :use_exam_recommendation %></td>
        <td>おすすめテスト使用させるか</td>
      </tr>
    </tbody>
  </table>

  <%= f.label "Question", class: "fw-bold text-secondary" %>
  <table class="table table-bordered">
    <tbody>
      <tr>
        <td><%= f.check_box :use_user_question %></td>
        <td><%= f.label :use_user_question %></td>
        <td>レッスンに関する質問や全体に関する質問を作成することができます。</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_question_list %></td>
        <td><%= f.label :use_question_list %></td>
        <td>質問リストを使用する</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_course_question %></td>
        <td><%= f.label :use_course_question %></td>
        <td>コースの質問を使用する</td>
      </tr>
    </tbody>
  </table>

  <%= f.label "Premium service", class: "fw-bold text-secondary" %>
  <table class="table table-bordered">
    <tbody>
      <tr>
        <td><%= f.check_box :use_premium_service %></td>
        <td><%= f.label :use_premium_service %></td>
        <td>プレミアムサービスを使用してビジネスできます。</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_goal %></td>
        <td><%= f.label :use_goal %></td>
        <td>ユーザーゴールを使うか。</td>
      </tr>
    </tbody>
  </table>

  <%= f.label "Marketing", class: "fw-bold text-secondary" %>
  <table class="table table-bordered">
    <tbody>
      <tr>
        <td><%= f.check_box :use_user_group %></td>
        <td><%= f.label :use_user_group %></td>
        <td>ユーザーグループを使用する</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_signup_email_template %></td>
        <td><%= f.label :use_signup_email_template %></td>
        <td>サインアップメールテンプレートを使用する</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_step_email %></td>
        <td><%= f.label :use_step_email %></td>
        <td>ステップメールを使用する</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_banner %></td>
        <td><%= f.label :use_banner %></td>
        <td>バナー機能使用させるか</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_coupon %></td>
        <td><%= f.label :use_coupon %></td>
        <td>クーポンを使用する</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_access_report %></td>
        <td><%= f.label :use_access_report %></td>
        <td>アクセスレポートを使用する</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_blog %></td>
        <td><%= f.label :use_blog %></td>
        <td>ブログを作成することができます。</td>
      </tr>
    </tbody>
  </table>

  <%= f.label "School", class: "fw-bold text-secondary" %>
  <table class="table table-bordered">
    <tbody>
      <tr>
        <td><%= f.check_box :use_school_onboarding %></td>
        <td><%= f.label :use_school_onboarding %></td>
        <td>スクールオンボードを使用する</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_dashboard %></td>
        <td><%= f.label :use_dashboard %></td>
        <td>スクールのダッシュボードを使うか。</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_user_dashboard %></td>
        <td><%= f.label :use_user_dashboard %></td>
        <td>ユーザのダッシュボードを使うか。</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_user_graph %></td>
        <td><%= f.label :use_user_graph %></td>
        <td>テストのダッシュボードグラフの表示・非表示</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_school_designs_code_editor %></td>
        <td><%= f.label :use_school_designs_code_editor %></td>
        <td>コードエディターを使用する</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_school_designs_general %></td>
        <td><%= f.label :use_school_designs_general %></td>
        <td>一般的なデザインを使用する</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_lists %></td>
        <td><%= f.label :use_lists %></td>
        <td>リストを使用する</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_terms %></td>
        <td><%= f.label :use_terms %></td>
        <td>利用規約を使用する</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_activities %></td>
        <td><%= f.label :use_activities %></td>
        <td>アクティビティを使用する</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_school_plan %></td>
        <td><%= f.label :use_school_plan %></td>
        <td>スクールプランを使用する</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_custom_texts %></td>
        <td><%= f.label :use_custom_texts %></td>
        <td>カスタムテキストを使用する</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_meeting, id: "use_meeting_check_box" %></td>
        <td><%= f.label :use_meeting %></td>
        <td>面談・予約を使用する</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_meeting_admin, id: "use_meeting_admin_check_box" %></td>
        <td><%= f.label :use_meeting_admin %></td>
        <td>管理者の面談機能を利用する</td>
      </tr>
    </tbody>
  </table>

  <%= f.label "AI機能", class: "fw-bold text-secondary" %>
  <table class="table table-bordered">
    <tbody>
      <tr>
        <td><%= f.check_box :use_ai_chat %></td>
        <td><%= f.label :use_ai_chat %></td>
        <td>AIチャットを使用する</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_ai_chat_question %></td>
        <td><%= f.label :use_ai_chat_question %></td>
        <td>AIチャットの質問を使用する</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_ai_model_api %></td>
        <td><%= f.label :use_ai_model_api %></td>
        <td>AIモデルAPIを使用する</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_ai_chat_lesson %></td>
        <td><%= f.label :use_ai_chat_lesson %></td>
        <td>クラスルームのレッスンでAIを使用する</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_ai_exam_generation %></td>
        <td><%= f.label :use_ai_exam_generation %></td>
        <td>アドミンのテスト作成でAIを使用する</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_ai_chat_goal %></td>
        <td><%= f.label :use_ai_chat_goal %></td>
        <td>クラスルームのゴールでAIを使用する</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_ai_chat_exam %></td>
        <td><%= f.label :use_ai_chat_exam %></td>
        <td>テスト結果でAI機能を使用する</td>
      </tr>
      <tr>
        <td><%= f.check_box :use_ai_grading %></td>
        <td><%= f.label :use_ai_grading %></td>
        <td>AI でテストを採点する</td>
      </tr>
    </tbody>
  </table>
</div>

<style>
  .table td:first-child {
    width: 5%;
    text-align: center;
    vertical-align: middle;
  }
  .table td:nth-child(2) {
    width: 40%;
    vertical-align: middle;
  }
  .table td:nth-child(3) {
    width: 55%;
    vertical-align: middle;
  }
</style>
