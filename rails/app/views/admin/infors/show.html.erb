<div class="mdc-layout-grid py-3">
  <div class="mdc-layout-grid__inner">
    <div class="mdc-layout-grid__cell--span-6">
      <a href="<%= url_for([:admin, @school, :infors]) %>" type="button" class="mdc-button mdc-button--outlined me-2">
        <i class="material-icons">west</i>
      </a>
      <%= @infor.title %>
    </div>
    <div class="mdc-layout-grid__cell--span-6 text-end">
      <%= link_to "#{t('edit')}", [:edit, :admin, @school, @infor], class: "btn btn-primary" %>
      <%= link_to "#{t('destroy')}", [:admin, @school, @infor],
        class: "btn btn-danger", data: {confirm: t('confirm')}, method: :delete %>
    </div>
  </div>
</div>

<div class="row p-2" style="height: calc(100vh - 120px);">
  <div class="col-sm-8 bg-white">
    <div class="w-100 d-flex">
      <div>
        <div>
          <span class="display-2"><%= @infor.read_rate %></span>
          <span>%</span>
        </div>
        <div class="text-center w-100">
          既読率
        </div>
      </div>
      <div class="ms-3 mt-3">
        <div>作成日:　<%= nichi @infor.created_at %></div>
        <div>送信数:　<%= @school.users.count %></div>
        <div>開封数:　<%= @infor.users.count %></div>
      </div>
    </div>

    <div class="mt-4">
      <div class="infor-body">
        <%= custom_sanitize UtilityHelper.markdown_to_html(@infor.content) %>
      </div>
    </div>
  </div>
</div>