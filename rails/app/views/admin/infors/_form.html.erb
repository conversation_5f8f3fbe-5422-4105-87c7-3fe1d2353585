<% simple_mde_class = "simple-mde-editor-#{UtilityHelper.ran_dom_str}" %>
<%= form_for([:admin, @school, @infor], multipart: true, remote: remote) do |f| %>
  <div class="mdc-layout-grid py-3">
    <div class="mdc-layout-grid__inner d-flex justify-content-between">
      <div class="mdc-layout-grid__cell--span-6">
        <a href="<%= url_for([:admin, @school, @infor]) %>" type="button" class="mdc-button mdc-button--outlined me-2">
          <i class="material-icons">west</i>
        </a>
        <%= @infor.title %>
      </div>
      <div class="actions">
        <label class="mdc-typography--headline7 save-status text-success me-2 d-none"></label>
        <% if @infor.new_record? %>
          <%= f.submit class: "btn btn-primary" %>
        <% else %>
          <button id="update-infor-btn" type="button" class="btn btn-primary">更新する</button>
        <% end %>
        <%= link_to "/classroom/infors/#{@infor.id}", class: "mdc-button mdc-button--outlined", target: "_blank" do %>
          <span class="mdc-button__ripple"></span>
          <span class="mdc-button__label">プレビュー</span>
        <% end %>
      </div>
    </div>
  </div>

  <% if @infor.errors.any? %>
    <div class="mdc-layout-grid py-3">
      <div class="mdc-layout-grid__inner">
        <div class="mdc-layout-grid__cell" id="error_explanation">
          <ul class="mb-0">
            <% @infor.errors.full_messages.each do |message| %>
              <li><%= message %></li>
            <% end %>
          </ul>
        </div>
      </div>
    </div>
  <% end %>
  <div class="mdc-layout-grid pt-0">
    <div class="mdc-layout-grid__inner">
      <div class="mdc-layout-grid__cell--span-8">
        <div class="mdc-card mdc-card--outlined px-3 pt-4">
          <label class="mdc-text-field mdc-text-field--outlined">
            <span class="mdc-notched-outline">
              <span class="mdc-notched-outline__leading"></span>
              <span class="mdc-notched-outline__notch">
                <span class="mdc-floating-label">タイトル</span>
              </span>
              <span class="mdc-notched-outline__trailing"></span>
            </span>
            <%= f.text_field :title, class: "mdc-text-field__input" %>
          </label>
          <div class="form-group mt-2">
            <%= f.label :content %>
            <%= f.text_area :content, class: "form-control #{simple_mde_class}" %>
          </div>
        </div>
      </div>

      <div class="mdc-layout-grid__cell--span-4">
        <div class="mdc-card mdc-card--outlined p-3 mb-3">
          <div class="mdc-layout-grid w-100 py-0">
            <div class="mdc-layout-grid__inner">
              <div class="mdc-layout-grid__cell--span-6">
                <label class="mdc-typography--subtitle1">公開/未公開</label>
              </div>
            </div>
          </div>
          <div class="mdc-form-field">
            <div class="mdc-radio">
              <%= f.radio_button :published, "1", checked: @infor.published_at.present?, class: "mdc-radio__native-control" %>
              <div class="mdc-radio__background">
                <div class="mdc-radio__outer-circle"></div>
                <div class="mdc-radio__inner-circle"></div>
              </div>
              <div class="mdc-radio__ripple"></div>
            </div>
            <label class="mt-2" for="course_published_1">公開
              <% if @infor.published_at.present? %>
                <span class="ms-3"><small><%= nichiji @infor.published_at %></small></span>
              <% end %>
            </label>
          </div>
          <div class="mdc-form-field mb-3">
            <div class="mdc-radio">
              <%= f.radio_button :published, "0", checked: !@infor.published_at.present?, class: "mdc-radio__native-control" %>
              <div class="mdc-radio__background">
                <div class="mdc-radio__outer-circle"></div>
                <div class="mdc-radio__inner-circle"></div>
              </div>
              <div class="mdc-radio__ripple"></div>
            </div>
            <label class="mt-2" for="course_published_0">未公開</label>
          </div>
        </div>
        <div class="mdc-card mdc-card--outlined p-3 mb-3">
          <div class="mdc-form-field">
            <div class="mdc-checkbox">
              <%= f.check_box :show_dashboard,class: "mdc-checkbox__native-control", id: "can_test_all_check_box"  %>
              <div class="mdc-checkbox__background">
                <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                  <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59" />
                </svg>
                <div class="mdc-checkbox__mixedmark"></div>
              </div>
            </div>
            <label for="XXX" class="mb-0">ユーザーダッシュボードに表示する</label>
          </div>
          <div class="ms-5">
            <small>
              アクティブにする（このページが表示されます）</br>

              コンテンツ全部がユーザーダッシュボードに表示されます。</br>
              ダッシュボードには、ひとつだけしか表示できません。
            </small>
          </div>
        </div>
        
        <div class="mdc-card mdc-card--outlined p-3 mb-3">
          <div class="mdc-layout-grid w-100 py-0">
            <div class="mdc-layout-grid__inner">
              <div class="mdc-layout-grid__cell--span-3 view-image">
                <%= image_tag @infor.image.url, class: "show-image", style: "width: 60px; height: 40px;" if @infor.image?%>
              </div>
              <div class="mdc-layout-grid__cell--span-9 float-left">
                <%= f.file_field :image, accept: Settings.upload_type.image%>
              </div>
            </div>
          </div>
          <%= f.hidden_field :image_cache %>
          <a href="javascript:void(0);" class="delete-image", style="<%='display: none;' unless @infor.image? %>"> 画像を削除する</a>
        </div>
      </div>
    </div>
  </div>

  <div id="modal-confirm" class="modal fade" role="dialog">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header align-items-start">
          <h4 class="modal-title">全員に通知しますか？</h4>
          <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
        </div>
        <div class="modal-body text-center" id="modal-content">
          <%= f.submit "はい", class: "btn btn-primary mx-3" %>
          <%= f.submit "いいえ", class: "btn btn-secondary mx-3" %>
        </div>
      </div>
    </div>
  </div>

<% end %>

<% content_for :custom_style do %>
  <style>
    #modal-confirm .modal-header {
      border-bottom: unset;
    }

    #modal-confirm .btn {
      width: 100px
    }

    #modal-confirm button {
      font-size: 21px;
      line-height: 21px;
      width: 21px;
    }
  </style>
<% end %>

<script>
  $(document).ready(function() {
    // Check exist simpleMDE on screen
    if ($(".CodeMirror").length === 0) {
      addSimpleMdebyClass(".<%= simple_mde_class %>")
    }
  })
  $(document).on("click", "#infor_image", function(){
    $("#infor_image").on("change", function (e) {
      var reader = new FileReader();
      reader.readAsDataURL(e.target.files[0]);
      reader.onload = function (e) {
        $(".show-image").show()
        $(".view-image").show()
        $(".show-image").attr("src", e.target.result);
        $(".delete-image").show()
      }
    })
  })
  $(".delete-image").click(function(){
    var check = confirm("本当に削除してよろしいですか？");
    if (check == true) {
      $("#infor_image").val("")
      $(".show-image").hide()
      $(".view-image").hide()
      $(".delete-image").hide()
      $("#infor_image").val("delete")
    } else {
      return false
    }
  })

  $("#update-infor-btn").click(function(){
    if ($("#infor_published_1").prop("checked")) {
      $('#modal-confirm').modal('show')
    } else {
      $("form").submit()
    }
  })
</script>
