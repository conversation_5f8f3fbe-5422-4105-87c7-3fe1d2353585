<div class="m-1 p-1 bg-light" style="overflow-y: scroll;height: calc(100vh - 120px);">
  <div class="p-2 fw-bold"><%= I18n.t('admin.school_designs.sections.fixed_templates') %></div>
  <div>
    <div class="mx-2 <%= @page_name == :main_page_custom ? 'page-custom-design-active' : 'page-custom-design' %>">
      <%= link_to I18n.t('admin.school_designs.pages.main_page_custom'), page_main_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
    </div>
    <div class="mx-2 <%= @page_name == :exam_list ? 'page-custom-design-active' : 'page-custom-design' %>">
      <%= link_to I18n.t('admin.school_designs.pages.exam_list'), page_exam_list_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
    </div>
    <div class="mx-2 <%= @page_name == :exam_show ? 'page-custom-design-active' : 'page-custom-design' %>">
      <%= link_to I18n.t('admin.school_designs.pages.exam_show'), page_exam_show_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
    </div>
    <div class="mx-2 <%= @page_name == :course_list ? 'page-custom-design-active' : 'page-custom-design' %>">
      <%= link_to I18n.t('admin.school_designs.pages.course_list'), page_course_list_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
    </div>
     <div class="mx-2 <%= @page_name == :purchase_cart ? 'page-custom-design-active' : 'page-custom-design' %>">
      <%= link_to "Cart", page_purchase_cart_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
    </div>
    <div class="mx-2 <%= @page_name == :purchase_checkout ? 'page-custom-design-active' : 'page-custom-design' %>">
      <%= link_to "Checkout", page_purchase_checkout_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
    </div>
    <div class="mx-2 <%= @page_name == :purchase_thank ? 'page-custom-design-active' : 'page-custom-design' %>">
      <%= link_to "Thank", page_purchase_thank_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
    </div>
    <div class="mx-2 <%= @page_name == :course_show ? 'page-custom-design-active' : 'page-custom-design' %>">
      <%= link_to I18n.t('admin.school_designs.pages.course_show'), page_course_show_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
    </div>
    <div class="mx-2 <%= @page_name == :lesson_show ? 'page-custom-design-active' : 'page-custom-design' %>">
      <%= link_to I18n.t('admin.school_designs.pages.lesson_show'), page_lesson_show_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
    </div>
    <div class="mx-2 <%= @page_name == :blog_list ? 'page-custom-design-active' : 'page-custom-design' %>">
      <%= link_to I18n.t('admin.school_designs.pages.blog_list'), page_blog_list_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
    </div>
    <div class="mx-2 <%= @page_name == :blog_show ? 'page-custom-design-active' : 'page-custom-design' %>">
      <%= link_to I18n.t('admin.school_designs.pages.blog_show'), page_blog_show_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
    </div>
    <div class="mx-2 <%= @page_name == :premium_list ? 'page-custom-design-active' : 'page-custom-design' %>">
      <%= link_to I18n.t('admin.school_designs.pages.premium_list'), page_premium_list_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
    </div>
    <div class="mx-2 <%= @page_name == :premium_show ? 'page-custom-design-active' : 'page-custom-design' %>">
      <%= link_to I18n.t('admin.school_designs.pages.premium_show'), page_premium_show_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
    </div>
    <div class="mx-2 <%= @page_name == :inquiry ? 'page-custom-design-active' : 'page-custom-design' %>">
      <%= link_to I18n.t('admin.school_designs.pages.inquiry'), page_inquiry_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
    </div>
    <div class="mx-2 <%= @page_name == :inquiry_thank ? 'page-custom-design-active' : 'page-custom-design' %>">
      <%= link_to I18n.t('admin.school_designs.pages.inquiry_thank'), page_inquiry_thank_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
    </div>
    <div class="mx-2 <%= @page_name == :teacher_list ? 'page-custom-design-active' : 'page-custom-design' %>">
      <%= link_to I18n.t('admin.school_designs.pages.teacher_list'), page_teacher_list_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
    </div>
    <div class="mx-2 <%= @page_name == :teacher_show ? 'page-custom-design-active' : 'page-custom-design' %>">
      <%= link_to I18n.t('admin.school_designs.pages.teacher_show'), page_teacher_show_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
    </div>
    <div class="mx-2 <%= @page_name == :review_show ? 'page-custom-design-active' : 'page-custom-design' %>">
      <%= link_to I18n.t('admin.school_designs.pages.review_show'), page_review_show_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
    </div>
    <div class="mx-2 <%= @page_name == :question_list ? 'page-custom-design-active' : 'page-custom-design' %>">
      <%= link_to I18n.t('admin.school_designs.pages.question_list'), page_question_list_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
    </div>
    <div class="mx-2 <%= @page_name == :question_show ? 'page-custom-design-active' : 'page-custom-design' %>">
      <%= link_to I18n.t('admin.school_designs.pages.question_show'), page_question_show_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
    </div>
    <div class="mx-2 <%= @page_name == :custom_page_list ? 'page-custom-design-active' : 'page-custom-design' %>">
      <%= link_to I18n.t('admin.school_designs.pages.custom_page_list'), page_custom_page_list_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
    </div>
    <% if @school.id == School::MAIN_DOMAIN_ID.to_i %>
      <div class="mx-2 <%= @page_name == :school_plan_list ? 'page-custom-design-active' : 'page-custom-design' %>">
        <%= link_to I18n.t('admin.school_designs.pages.school_plan_list'), page_school_plan_list_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
      </div>
      <div class="mx-2 <%= @page_name == :school_plan_show ? 'page-custom-design-active' : 'page-custom-design' %>">
        <%= link_to I18n.t('admin.school_designs.pages.school_plan_show'), page_school_plan_show_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
      </div>
    <% end %>
    <div class="mx-2 <%= @page_name == :custom_page_sign_up ? 'page-custom-design-active' : 'page-custom-design' %>">
      <%= link_to I18n.t('admin.school_designs.pages.custom_page_sign_up'), page_custom_page_sign_up_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
    </div>
    <div class="mx-2 <%= @page_name == :custom_page_sign_in ? 'page-custom-design-active' : 'page-custom-design' %>">
      <%= link_to I18n.t('admin.school_designs.pages.custom_page_sign_in'), page_custom_page_sign_in_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
    </div>
    <div class="mx-2 <%= @page_name == :custom_page_course_purchases_new ? 'page-custom-design-active' : 'page-custom-design' %>">
      <%= link_to I18n.t('admin.school_designs.pages.custom_page_course_purchases_new'), page_custom_page_course_purchases_new_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
    </div>
    <div class="mx-2 <%= @page_name == :custom_page_subscriptions_new ? 'page-custom-design-active' : 'page-custom-design' %>">
      <%= link_to I18n.t('admin.school_designs.pages.custom_page_subscriptions_new'), page_custom_page_subscriptions_new_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
    </div>
  </div>
  <div class="mt-3 p-2 fw-bold"><%= I18n.t('admin.school_designs.sections.media') %></div>
  <div class="mx-2 <%= @imagable ? 'page-custom-design-active' : 'page-custom-design' %>">
    <%= link_to I18n.t('admin.school_designs.links.default_common'), design_images_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
  </div>
  <div class="mt-3 p-2 fw-bold"><%= I18n.t('admin.school_designs.sections.css') %></div>
  <div class="mx-2 <%= @page_name == :default_css ? 'page-custom-design-active' : 'page-custom-design' %>">
    <%= link_to I18n.t('admin.school_designs.links.default_common'), page_default_css_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
  </div>
  <div class="mt-3 p-2 fw-bold"><%= I18n.t('admin.school_designs.sections.header') %></div>
  <div class="mx-2 <%= @page_name == :default_header ? 'page-custom-design-active' : 'page-custom-design' %>">
    <%= link_to I18n.t('admin.school_designs.links.header_default'), page_default_header_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
  </div>
  <div class="mt-3 p-2 fw-bold"><%= I18n.t('admin.school_designs.sections.footer') %></div>
  <div class="mx-2 <%= @page_name == :default_footer ? 'page-custom-design-active' : 'page-custom-design' %>">
    <%= link_to I18n.t('admin.school_designs.links.footer_default'), page_default_footer_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
  </div>
  <div class="mt-3 p-2 fw-bold"><%= I18n.t('admin.school_designs.pages.template') %></div>
  <div>
    <div class="mx-2 mt-2">
      <div class="input-group mb-1">
        <input type="text" id="template-title" class="form-control" placeholder="<%= I18n.t('admin.school_designs.placeholders.new_template') %>" aria-label="<%= I18n.t('admin.school_designs.placeholders.new_template') %>" aria-describedby="template-add-button">
        <div class="input-group-append">
          <button class="btn btn-outline-primary h-100" type="button" id="template-add-button"><%= I18n.t('admin.school_designs.buttons.create') %></button>
        </div>
      </div>
    </div>
    <div class="mx-2 <%= @page_name == :default_page_template ? 'page-custom-design-active' : 'page-custom-design' %>">
      <%= link_to I18n.t('admin.school_designs.links.default'), page_default_page_template_admin_school_school_design_path(@school, @school_design, { mode: @mode }) %>
    </div>

    <% @school_design.page_templates.each do |page_template_item| %>
      <div class="mx-2 <%= @page_template&.id == page_template_item.id ? 'page-custom-design-active' : 'page-custom-design' %>">
        <%= link_to page_template_item.title, admin_school_school_design_page_template_path(@school, @school_design, page_template_item, { mode: @mode }) %>
      </div>
    <% end %>
  </div>
</div>

<script>
  $("#template-add-button").click(function (params) {
    var school_id = "<%= @school.id %>";
    var school_design_id = "<%= @school_design.id %>";
    var template_title = $("#template-title").val();

    if (template_title) {
      $.ajax({
        url: `/admin/schools/${school_id}/school_designs/${school_design_id}/page_templates`,
        type: 'POST',
        dataType: "script",
        data: {
          page_template: {
            title: template_title,
          },
        },
        success: function () {
          //   NOOP
        }
      });
    }
  });
</script>
