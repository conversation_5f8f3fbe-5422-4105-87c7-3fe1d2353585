
<div class="main_content">
  <%= form_with(local: false, :html => { :id => "form_for_code" }) do |form| %>
    <div>
      <div class="row mb-2">
        <div class="col-lg-8 d-flex justify-content-between p-0 align-items-center">
          <%= render "tablist", menu_only: true %>
        </div>
        <div class="col-lg-4 d-flex justify-content-end p-0 align-items-center">
          <div class="form-group d-flex me-1">
            <%= form.text_field :name, value: @school_design.name, class: "form-control" %>
          </div>
          <% if @page_name != :default_page_template && @page_name != :default_css %>
            <a
                data-toggle="tooltip"
                data-placement="top"
                title="ブラウザで開く"
                href="<%= preview_url(@page_name, @school, @school_design, params[:revision_id]) %>"
                target="_blank"
                class="btn me-1"
            >
                <i class="fas fa-external-link-alt"></i>
            </a>
          <% end %>

          <span id="save-success-text" class="text-success m-2 d-none me-1">保存しました</span>
          <% unless params[:revision_id].present? %>
            <button id="submit-btn" class="mdc-button mdc-button--raised me-1 mb-2">
              <span class="mdc-button__ripple"></span>更新する
            </button>
          <% else %>
            <%= hidden_field_tag :revision_id, params[:revision_id]  %>
            <span class="text-danger m-2 me-1">編集不可</span>
            <button type="button" class="btn btn-warning btn-sm" onclick="onSubmitRevision('#form_for_code')">このヒストリーを使う</button>
          <% end %>

          <%= render 'admin/revisions/revision_button', target_id: @school_design.id, target_type: "SchoolDesign", target_key: @page_name %>
        </div>
      </div>

      <% if @school_design.errors.any? %>
        <div id="error_explanation">
          <ul>
            <% @school_design.errors.full_messages.each do |message| %>
              <li><%= message %></li>
            <% end %>
          </ul>
        </div>
      <% end %>
    </div>

    <div class="row">
      <div class="col-lg-2 p-0">
        <%= render 'siderbar_page_custom'%>
      </div>
      <div class="col-lg-10 school-design-form p-0">
        <div class="m-1 p-1 bg-light school-design-form-view">
          <%= render 'form_page_custom', 
            form: form, 
            attribute_name: :page_custom,
            show_css: (@page_name != :default_header), 
            show_html: (@page_name != :default_css && @page_name != :default_header),
            show_header: (@page_name != :default_css && @page_name != :default_footer),
            current_page_description: @school_design.send(@page_name).nil? ? nil : @school_design.send(@page_name)["page_description"],
            current_page_description_css: @school_design.send(@page_name).nil? ? nil : @school_design.send(@page_name)["page_description_css"],
            current_page_description_header: @school_design.send(@page_name).nil? ? nil : @school_design.send(@page_name)["page_description_header"]
          %>
        </div>
        
        <%= render 'admin/revisions/revision_box' %>
      </div>

      <!-- 一旦出さない -->
      <!-- <%= render 'preview', page_name: @page_name, school: @school, school_design: @school_design %> -->
    </div>
  <% end %>
</div>

<script>
  setupAutoSave()
</script>
