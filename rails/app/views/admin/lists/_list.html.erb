<% current_padding = (defined? padding) ? padding : 0 %>
<% has_children = list.children.any? %>
<% parent_id_attr = list.parent_id ? "data-parent-id=#{list.parent_id}" : "" %>
<% initial_style = list.parent_id ? 'display: none;' : '' %>

<tr class="mdc-data-table__row category-item <%= 'parent-category' if has_children %> <%= 'child-category' if list.parent_id %>"
    data-category-id="<%= list.id %>"
    data-parent-id="<%= list.parent_id || 'root' %>"
    style="<%= initial_style %>">

  <td class="mdc-data-table__cell text-start">
    <div style="padding-left: <%= current_padding %>px; display: flex; align-items: center;">
      <% if has_children %>
        <button class="mdc-icon-button material-icons toggle-children"
                data-target-category="<%= list.id %>"
                aria-expanded="false"
                style="margin-right: 8px; padding: 0; min-width: 24px; height: 24px; vertical-align: middle;">
          keyboard_arrow_right
        </button>
      <% else %>
        <span style="display: inline-block; width: 24px; height: 24px; margin-right: 8px;"></span>
      <% end %>
      <%= list.name %>
    </div>
  </td>

  <td class="mdc-data-table__cell text-start">
    <%= list.description %>
  </td>

  <td class="mdc-data-table__cell text-end">
    <%= link_to [:edit, :admin, @school, @parent, list], remote: true, class: "btn-edit mdc-icon-button material-icons" do %>
      <span class="mdc-fab__icon material-icons text-primary" tabindex="0" role="button">edit</span>
    <% end %>
    <%= link_to [:admin, @school, @parent, list], method: :delete, data: { confirm: t("confirm") }, class: "btn-delete mdc-icon-button material-icons" do %>
      <span class="mdc-fab__icon material-icons text-danger" tabindex="0" role="button">delete_forever</span>
    <% end %>
  </td>
</tr>

<% if has_children %>
  <% list.children.ordered_by_position.each do |child| %>
    <%= render "list", list: child, padding: current_padding + 30 %>
  <% end %>
<% end %>
