<div class="mdc-layout-grid py-3">
  <div class="mdc-layout-grid__inner">
    <div class="mdc-layout-grid__cell--span-8">
      <label class="mdc-typography--subtitle1">すべてのテストカテゴリ</label>
    </div>
    <div class="mdc-layout-grid__cell--span-4 text-end">
    </div>
  </div>
</div>

<div class="mdc-layout-grid w-100 py-0 mb-5">
  <div class="mdc-layout-grid__inner">
    <div class="mdc-layout-grid__cell mdc-layout-grid__cell--span-4">
      <div id="category-form" class="mdc-card mdc-card--outlined px-3 pt-4 pb-3">
        <%= render "form", list: List.new %>
      </div>
    </div>
    <div class="mdc-layout-grid__cell mdc-layout-grid__cell--span-8">
      <div class="mdc-data-table">
        <div class="mdc-data-table__table-container">
          <table class="mdc-data-table__table table-sortable w-100">
            <thead>
              <tr class="mdc-data-table__header-row">
                <th class="mdc-data-table__header-cell" role="columnheader" scope="col" width="50">名前</th>
                <th class="mdc-data-table__header-cell" role="columnheader" scope="col" width="40">説明</th>
                <th class="mdc-data-table__header-cell" role="columnheader" scope="col" width="10"></th>
              </tr>
            </thead>
            <tbody id="category-list" class="mdc-data-table__content table-sortable-menu">
              <%= render @lists %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
<script>
document.addEventListener('DOMContentLoaded', () => {
  function collapseChildrenRecursively(categoryId) {
    const childRows = document.querySelectorAll(`tr.child-category[data-parent-id='${categoryId}']`);
    childRows.forEach(childRow => {
      childRow.style.display = 'none';
      const childId = childRow.dataset.categoryId;
      const childToggleButton = childRow.querySelector(`.toggle-children[data-target-category='${childId}']`);
      if (childToggleButton) {
        childToggleButton.textContent = 'keyboard_arrow_right';
        childToggleButton.setAttribute('aria-expanded', 'false');
        childRow.classList.remove('expanded');
        collapseChildrenRecursively(childId);
      }
    });
  }

  document.body.addEventListener('click', function(event) {
    const toggleButton = event.target.closest('.toggle-children');

    if (toggleButton) {
      const categoryId = toggleButton.dataset.targetCategory;
      const parentRow = toggleButton.closest('tr');
      const childRows = document.querySelectorAll(`tr.child-category[data-parent-id='${categoryId}']`);
      const isExpanded = toggleButton.getAttribute('aria-expanded') === 'true';

      if (isExpanded) {
        collapseChildrenRecursively(categoryId);
        toggleButton.textContent = 'keyboard_arrow_right';
        toggleButton.setAttribute('aria-expanded', 'false');
        parentRow.classList.remove('expanded');
      } else {
        childRows.forEach(row => {
          row.style.display = 'table-row';
        });
        toggleButton.textContent = 'keyboard_arrow_down';
        toggleButton.setAttribute('aria-expanded', 'true');
        parentRow.classList.add('expanded');
      }
    }
  });
});
$(document).ready(function() {
  function getAllDescendants($parentItem, $container) {
    let $descendants = $();
    const parentId = $parentItem.data('category-id');

    if (!parentId) {
      return $descendants;
    }

    const $directChildren = $container.find(`tr.child-category[data-parent-id='${parentId}']`);

    $directChildren.each(function() {
      const $child = $(this);
      $descendants = $descendants.add($child);
      $descendants = $descendants.add(getAllDescendants($child, $container));
    });

    return $descendants;
  }

  $('.table-sortable-menu').sortable({
    items: "> tr",
    axis: 'y',
    handle: 'td:first-child',
    placeholder: "ui-state-highlight",
    forcePlaceholderSize: true,

    start: function(event, ui) {
      ui.item.data('original-parent-id', ui.item.data('parent-id') || null);
      ui.placeholder.height(ui.item.outerHeight());
    },
    stop: function(event, ui) {
    },
    update: function(event, ui) {
      const $movedItem = ui.item;
      const $sortableContainer = $(this);

      const $allDescendants = getAllDescendants($movedItem, $sortableContainer);

      if ($allDescendants.length > 0) {
        $allDescendants.insertAfter($movedItem);
      }

      const parentIdOfItemGroup = $movedItem.data('parent-id') || null;

      const orderedIds = $sortableContainer
        .find(`tr[data-category-id]`)
        .filter(function() {
          return ($(this).data('parent-id') || null) === parentIdOfItemGroup;
        })
        .map(function() {
          return $(this).data('category-id');
        })
        .get();

      if (orderedIds.length > 0) {
        console.log(parentIdOfItemGroup)
        console.log(orderedIds)
        const reorderUrl = '/admin/lists/reorder';
        $.ajax({
          url: reorderUrl,
          type: 'POST',
          data: JSON.stringify({
            parent_id: parentIdOfItemGroup,
            ordered_ids: orderedIds
          }),
          contentType: 'application/json; charset=utf-8',
          dataType: 'json',
          headers: {
            'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
          },
          success: function(response) {
            console.log('Reorder successful:', response);
          },
          error: function(xhr, status, error) {
            console.error('Reorder failed:', xhr.responseText || error);
          }
        });
      } else if ($movedItem.length > 0 && parentIdOfItemGroup !== undefined) {
        console.warn("orderedIds []");
      }
    }
  });
})
</script>
<style>
.toggle-children {
  cursor: pointer;
  user-select: none;
}
</style>
