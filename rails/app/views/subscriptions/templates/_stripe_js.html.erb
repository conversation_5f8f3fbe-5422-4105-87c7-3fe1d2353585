<script src="https://js.stripe.com/v3/"></script>
<script>
  function setPricePurchase(option) {
    $("#purchase-amount").text(option.val());
    $("#purchase-tax").text(option.data("tax"));
    $("#purchase-total").text(option.data("total"));
    $("input[name='price_plan_id']").val(option.data("id"));
    // update for custom form
    $(".charge-price-without-tax").text(option.val());
    $(".charge-tax").text(option.data("tax"));
    $(".charge-price-with-tax").text(option.data("total"));
    $(".charge-membership-fee-with-tax").text(option.data("charge-membership-fee-with-tax"));
    $(".charge-membership-fee").text(option.data("charge-membership-fee"));
    // trial
    $(".trial-charge-price-without-tax").text(option.data("trial-charge-price-without-tax"));
    $(".trial-charge-tax").text(option.data("trial-charge-tax"));
    $(".trial-charge-price-with-tax").text(option.data("trial-charge-price-with-tax"));

    set_installment_count_purchase();
  }

  function set_installment_count_purchase() {
    installment_count_purchase = get_installment_count_purchase();

    installment_count = parseInt(document.querySelector("#installment_count").value);

    if (!installment_count_purchase) {
      $("#installment_purchase_total").hide();
      return;
    }

    $("#installment_purchase_total").show();
    $("#installment_purchase_total").text(`お支払いの回数: ${installment_count}回（${numberWithCommas(installment_count_purchase)}円 / 回）`);

    $("input[name='installment_count_value']").val(installment_count);
  }

  function get_installment_count_purchase() {
    if (!document.querySelector("#installment_count_select") || document.querySelector("#installment_count_select").style.display === 'none') {
      return null;
    }

    installment_count = parseInt(document.querySelector("#installment_count").value);
    var purchase_total = $("#purchase-total").text().replace(/[^0-9.-]+/g, "");
    installment_count_purchase = purchase_total / installment_count;

    return Math.round(installment_count_purchase);
  }

  function numberWithCommas(x) {
    return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  }

  // Initialize Stripe
  const stripe = Stripe("<%= Rails.configuration.stripe[:publishable_key] %>");
  const elements = stripe.elements();
  let cardElement = null;
  let cardElementMounted = false;

  function getCardElement() {
    if (!cardElement) {
      cardElement = elements.create('card', {
        style: {
          base: {
            fontSize: '16px',
            color: '#32325d',
            fontFamily: '-apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif',
            '::placeholder': { color: '#aab7c4' }
          },
          invalid: {
            color: '#dc3545',
            iconColor: '#dc3545'
          }
        },
        hidePostalCode: true
      });
    }
    return cardElement;
  }

  // Modified: Mount card element safely
  function mountCardElement() {
    const element = getCardElement();
    const mountPoint = document.getElementById('stripe-card-element');

    // Only mount if not already mounted and the mount point exists
    if (!cardElementMounted && mountPoint) {
      element.mount('#stripe-card-element');
      cardElementMounted = true;

      element.on('change', function(event) {
        const displayError = document.getElementById('stripe-card-errors');
        if (displayError) {
          if (event.error) {
            displayError.textContent = event.error.message;
            displayError.style.display = 'block';
          } else {
            displayError.textContent = '';
            displayError.style.display = 'none';
          }
        }
      });
    }
  }

  // Modified: Unmount card element when modal closes
  function unmountCardElement() {
    if (cardElement && cardElementMounted) {
      cardElement.unmount();
      cardElementMounted = false;
    }
  }

  // Modified: Create and show payment modal
  function openStripeModal(purchaseTotal) {
    const existingModal = document.getElementById('stripeModal');
    if (existingModal) {
      existingModal.remove();
    }

    cardElementMounted = false;

    const modalHTML = `
      <div class="modal fade" id="stripeModal" tabindex="-1" aria-labelledby="stripeModalLabel" aria-hidden="true">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="stripeModalLabel">クレジットカード情報</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <form id="stripe-payment-form">
                <input type="hidden" id="modal-purchase-total" value="${purchaseTotal}">

                <div class="mb-3">
                  <label for="stripe-card-element" class="form-label">カード情報</label>
                  <div id="stripe-card-element" class="form-control" style="height: 40px; padding-top: 10px;"></div>
                  <div id="stripe-card-errors" class="invalid-feedback" style="display: none;"></div>
                </div>

                <div class="mb-3">
                  <strong>お支払い合計: ¥${numberWithCommas(purchaseTotal)}</strong>
                  ${document.querySelector('#installment_count_select')?.style.display !== 'none' ? 
                    `<div class="mt-2 text-muted">${document.getElementById('installment_purchase_total')?.textContent || ''}</div>` : 
                    ''
                  }
                </div>

                <div class="d-grid gap-2 mt-4">
                  <button id="stripe-submit" type="submit" class="btn btn-primary">支払いを確定する</button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    `;

    // Append modal to body
    const modalContainer = document.createElement('div');
    modalContainer.innerHTML = modalHTML;
    document.body.appendChild(modalContainer);

    // Get modal reference
    const modalElement = document.getElementById('stripeModal');

    // Add event listener for modal hidden event
    modalElement.addEventListener('hidden.bs.modal', function() {
      unmountCardElement();
    });

    // Show the modal
    const myModal = new bootstrap.Modal(modalElement);
    myModal.show();

    // Wait for modal animation to complete before mounting card element
    setTimeout(() => {
      mountCardElement();

      // Add submit handler to form
      document.getElementById('stripe-payment-form').addEventListener('submit', handlePaymentSubmission);
    }, 300);
  }

  // Handle payment submission with 3D Secure
  async function handlePaymentSubmission(e) {
    e.preventDefault();

    const submitButton = document.getElementById('stripe-submit');
    const errorDisplay = document.getElementById('stripe-card-errors');

    // Disable button and show loading state
    submitButton.disabled = true;
    submitButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>処理中...';

    try {
      // Get form data
      let formData = {};
      $("#subscription-form").serializeArray().forEach(function(item) {
        formData[item.name] = item.value;
      });

      const purchaseTotal = document.getElementById('modal-purchase-total').value;

      const { paymentMethod, error: paymentMethodError } = await stripe.createPaymentMethod({
        type: 'card',
        card: cardElement,
        billing_details: {
          name: formData['subscription[name]'] || "",
          email: "<%= current_user.email %>"
        }
      });

      if (paymentMethodError) {
        throw new Error(paymentMethodError.message);
      }

      const response = await fetch('/premium_services/<%= @premium_service.id %>/subscriptions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
          premium_service_id: "<%= @premium_service.id %>",
          price_plan_id: formData['price_plan_id'] || "",
          coupon_code: formData['coupon_code_value'] || "",
          installment_count: formData['installment_count_value'] || "",
          amount: purchaseTotal,
          name: formData['subscription[name]'] || "",
          payment_method_id: paymentMethod.id
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'サーバーエラーが発生しました。もう一度お試しください。');
      }

      const data = await response.json();
      if (!data.client_secret) {
        window.location.href = "/classroom";
        return
      }

      let error = null;
      let setupIntent = null;
      let paymentIntent = null;

      if (data.intent_type === 'setup') {
        const result = await stripe.confirmCardSetup(data.client_secret, {
          payment_method: {
            card: cardElement,
            billing_details: {
              name: formData['subscription[name]'] || "",
              email: "<%= current_user.email %>"
            }
          }
        });

        error = result.error;
        paymentIntent = result.setupIntent;

        if(!error) {
          let params = {
            payment_method: result.setupIntent.payment_method,
            subscription_id: data.subscription_id
          }
          savePaymentMethod(params)
        }
      } else {
        const result = await stripe.confirmCardPayment(data.client_secret, {
          payment_method: {
            card: cardElement,
            billing_details: {
              name: formData['subscription[name]'] || "",
              email: "<%= current_user.email %>"
            }
          },
          setup_future_usage: 'off_session'
        });

        error = result.error;
        paymentIntent = result.paymentIntent;

        if(!error) {
          let params = {
            payment_method: paymentIntent.payment_method,
            subscription_id: data.subscription_id
          }
          savePaymentMethod(params)
        }
      }

      if (error) {
        if (data.client_secret === null) {
          errorDisplay.textContent = "決済に失敗しました。もう一度お試しください。";
        } else {
          errorDisplay.textContent = error.message;
        }
        errorDisplay.style.display = 'block';
        submitButton.disabled = false;
        submitButton.innerHTML = '支払いを確定する';
      } else if (paymentIntent && paymentIntent.status === 'succeeded') {
        bootstrap.Modal.getInstance(document.getElementById('stripeModal')).hide();

        window.location.href = "/classroom";
      }
    } catch (err) {
      errorDisplay.textContent = err.message;
      errorDisplay.style.display = 'block';
      submitButton.disabled = false;
      submitButton.innerHTML = '支払いを確定する';
    }
  }

  $(document).ready(function () {
    const edbase_ref = localStorage.getItem("edbase_ref");
    const guest_uuid = localStorage.getItem("guest_uuid");
    if (edbase_ref != undefined) {
      let new_ele = document.createElement('input');
      new_ele.setAttribute("name", "edbase_ref");
      new_ele.setAttribute("value", edbase_ref);
      new_ele.setAttribute("type", "hidden");
      $("form").append(new_ele);
    }
    if (guest_uuid != undefined) {
      new_ele = document.createElement('input');
      new_ele.setAttribute("name", "guest_uuid");
      new_ele.setAttribute("value", guest_uuid);
      new_ele.setAttribute("type", "hidden");
      $("form").append(new_ele);
    }
    var initPricePlan = "<%= flash[:price_plan_id] %>";

    if (initPricePlan) {
      $("#terms_of_use").prop("checked", true);
      $("#card-pay").prop("disabled", false);

      var option = $(`#plan_price option[data-id='${initPricePlan}']`);
      option.prop("selected", true);
      setPricePurchase(option);
    }
  });

  $(function () {
    $("#plan_price").change(function () {
      // 金額設定
      setPricePurchase($(this).find(':selected'));

      // Coupon設定
      $("#js_coupon_value").prop('disabled', false);
      $(".coupon-price-text").text("");
      $(".coupon-success").hide();
      $("#js_submit_coupon").show();
    });

    $("#terms_of_use").click(function () {
      $("#card-pay").prop("disabled", !$(this).is(":checked"));
    });

    // Card payment button handler
    $("#card-pay").click(function(e) {
      e.preventDefault();

      // Calculate purchase total
      let purchase_total = get_installment_count_purchase();
      if (!purchase_total) {
        purchase_total = $("#purchase-total").text().replace(/[^0-9.-]+/g, "");
      }

      let formData = {};
      $("#subscription-form").serializeArray().forEach(function (item) {
        formData[item.name] = item.value;
      });

      purchase_total = calculatePurchaseTotal(formData);

      if (parseInt(purchase_total) >= 0 && $("#premium_service_id_verify").val() == "<%= @premium_service.id %>") {
        // Open Stripe modal with 3D Secure support
        openStripeModal(purchase_total);
      }
    });

    $("#js_submit_coupon").click(function () {
      var coupon_code = $("#js_coupon_value").val();
      var plan_id = $("#plan_price").find(":selected").data("id");
      $.ajax({
        url: "<%= "/premium_services/#{@premium_service.id}/subscriptions/get_coupon" %>",
        type: "POST",
        data: {"coupon_code": coupon_code, "plan_id": plan_id},
        success: function (response) {
          if (response["success"]) {
            var discount_price = 0;
            var purchase_total = $("#purchase-total").text().replace(/[^0-9.-]+/g, "");
            if (response["discount_type"] == "cash") {
              discount_price = response["discount_price"];
            } else {
              discount_price = parseInt(purchase_total) * parseInt(response["discount_rate"]) / 100;
            }
            $(".coupon-price-text").text(` - ${numberWithCommas(discount_price)}円`);
            var new_price = parseInt(purchase_total) - discount_price;
            $("#purchase-total").text(`${numberWithCommas(new_price)}円`);
            $("#js_submit_coupon").hide();
            $(".coupon-error").hide();
            $(".previous_price").text(response["previous_price"]);
            $(".coupon-success").show();
            $("#js_coupon_value").prop('disabled', true);
            $("#coupon_code_value").val($("#js_coupon_value").val());
          } else {
            $(".coupon-error").show();
          }
        },
        error: function (response) {
        }
      });
    });
    $("#js_cancel_submit_coupon").click(function () {
      $("#js_coupon_value").prop('disabled', false);
      $(".coupon-price-text").text("");
      $(".coupon-success").hide();
      $("#js_submit_coupon").show();
      var old_price = $(".previous_price").text();
      $("#purchase-total").text(`${numberWithCommas(old_price)}円`);
    });

    $("#installment_count").change(function () {
      set_installment_count_purchase();
    });

    document.querySelectorAll("input[name='installment_flag']").forEach((input) => {
      input.addEventListener('change', function (input) {
        if (input.target.value == 'true') {
          $("#installment_count_select").show();
          set_installment_count_purchase();
        } else {
          $("#installment_count_select").hide();
          $("#installment_purchase_total").hide();
        }
      });
    });

    function calculatePurchaseTotal(params) {
      let purchase_total;
      $.ajax({
        url: "/premium_services/<%= @premium_service.id %>/subscriptions/calculate_purchase_total",
        type: "POST",
        data: params,
        async: false,
        success: function (response) {
          if (response["success"]) {
            purchase_total = response["purchase_total"];
          } else {
            purchase_total = null;
            console.log(response["error"]);
          }
        }
      });

      return purchase_total;
    }
  });

  function savePaymentMethod(params) {
    $.ajax({
      url: "/premium_services/<%= @premium_service.id %>/subscriptions/save_payment_method",
      type: "POST",
      data: params,
      success: function (response) {
        console.log(response);
      },
      error: function (response) {
        console.log(response);
      }
    });
  }
</script>

<style>
  .coupon-btn {
    margin: 10px 0 10px 0;
  }

  /* Stripe Elements styling */
  .StripeElement {
    box-sizing: border-box;
    height: 40px;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    background-color: white;
  }

  .StripeElement--focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
  }

  .StripeElement--invalid {
    border-color: #dc3545;
  }

  /* Hide postal code field */
  .StripeElement .CardField-postalCode,
  .CardField-postalCode {
    display: none !important;
  }
</style>
