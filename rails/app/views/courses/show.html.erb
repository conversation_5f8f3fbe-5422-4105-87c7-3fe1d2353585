<% content_for :title, "Edbase[エドベース]のマニュアル | Edbase[エドベース]" %>
<% content_for :description, "" %>
<% content_for :og do %>
<% end %>
<% content_for :canonical do %>
  <link rel='canonical' rel='<%= courses_url %>' >
<% end %>
<%= content_for :loading_block do %>
  <div class="loader">
    <div class="loading-animation"></div>
  </div>
<% end %>
<% unless @page_custom_html.empty? %>
  <%= convert_banner(@page_custom_html.html_safe, request, current_user, "courses-#{@course.id}").html_safe %>
<% else %>
  <section class="bg-primary-3 has-divider text-light o-hidden">
    <div class="container layer-2">
      <div class="row justify-content-center pt-lg-5">
        <div class="col-xl-5 col-lg-6 col-md-7 text-center text-lg-start mb-5 mb-lg-0">
          <h1 class="display-4"><%= @course.name %></h1>
          <div class="my-4">
            <p class="lead"><%= @course.description %></p>
          </div>
          <div class="d-flex justify-content-center justify-content-lg-start">
          </div>
        </div>
        <div class="col">
          <div class="row justify-content-center">
            <div class="col-xl-8 col-md-10">
              <div class="mb-3">
                <%= image_tag @course.image.url, class: "img-fluid" if @course.image.present? %>
              </div>
              <% unless @course.type_is == "text" %>
                <% if @course.type_is == "free" %>
                  <% if @enrollment.present? %>
                    <%= link_to "受講コースへ移動する（すでに受講済みです）", "/classroom/my-courses/#{@enrollment.course.id}", class: "btn btn-lg btn-primary btn-block mb-2" %>
                  <% else %>
                    <%= button_to "今すぐ受講する#{ @course.type_is == "free" ? "【無料】" : ""}",
                    school_course_enrollments_path(@school, @course, enrollment: {school_id: @course.school.id, course_id: @course.id}),
                    id: "new_enrollment",
                    class: "btn btn-primary btn-lg btn-block mb-2"
                  %>
                  <% end %>
                <% elsif @course.type_is == "product" %>
                  <% if @enrollment %>
                    <%= link_to "受講コースへ移動する（すでに受講済みです）", "/classroom/my-courses/#{@enrollment.course.id}", class: "btn btn-lg btn-primary btn-block mb-2" %>
                  <% else %>
                    <div class="center clear">
                       <button class="btn btn-lg btn-primary btn-block mb-2 add-to-cart-btn add-to-cart-btn-in-show"
                              data-product-id="<%= @course.id %>"
                              data-product-title="<%= @course.name %>"
                              data-product-price="<%= @course.price || 0 %>"
                              data-product-image="<%= @course.image.url %>"
                              data-product-type="Course">
                        今すぐ購入する
                      </button>

                      <div class="my-2">もしくは</div>
                      <%= link_to "まずは問い合わせてみる", new_inquiry_path, class: "btn btn-lg btn-warning btn-block mb-2" %>
                    </div>
                  <% end %>
                <% else %>
                  <%= link_to "プレミアムサービスを見る", premium_services_path(course_id: @course.id), class: "btn btn-lg btn-primary btn-block mb-2" %>
                  <div class="small">※この講座はプレミアムサービスに加入する必要があります。</div>
                <% end %>
              <% else %>
                <%= link_to "今すぐ閲覧する", [@course, @course_lessons.first.lesson], class: "btn btn-lg btn-primary btn-block mb-2" %>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="decoration-wrapper d-none d-sm-block">
      <div data-jarallax-element="0 50">
        <div class="decoration top middle-y scale-5">
          <img class="bg-primary-2" src="/assets/img/decorations/deco-blob-9.svg" alt="deco-blob-9 decoration" data-inject-svg />
        </div>
      </div>
    </div>
    <div class="divider">
      <img class="bg-primary-alt" src="/assets/img/dividers/divider-2.svg" alt="divider graphic" data-inject-svg />
    </div>
  </section>
  
  <section>
    <div class="container">
      <div id="page-body-container">
        <%= LiquidRender::Render.render(@course.active_body).html_safe %>
      </div>
    </div>
  </section>
  
  <section>
    <div class="container">
      <div class="justify-content-center text-center mb-5">
        
        <h2 class="display-4 mx-xl-6">レッスン</h2>
        <p class="lead">
          ここで学ぶレッスンを紹介します。
        </p>
        <div class="col-6 mx-auto text-center">
        <ul>
        <% @course_lessons.each_with_index do |cl, index| %>
          <li class="d-flex py-2">
            <div class="icon-round icon-round-xs bg-primary-2 me-2">
              <img class="icon bg-primary-2" src="/images/icons/interface/check.svg" alt="Checkmark icon" data-inject-svg />
            </div>
            <h3><%= link_to "Lesson #{(index + 1)}: #{cl.lesson.name}", [@course, cl.lesson], class: "fw-bold" %></h3>
          </li>
        <% end %>
        </ul>
        </div>
      </div>
  </div>
</section>
<% end %>

<!-- Course Modal -->
<div class="udemy-popover" style="display: none;">
  <img class="product-image w-100 mb-3" src="" alt="商品詳細">
  <h5 class="mb-3">商品タイトル</h5>
  <p class="mb-2">更新済み <span class="product-updated-date">2025年1月</span></p>
  <p class="mb-2">合計 <span class="product-duration">12</span> 時間・<span class="product-level">初級</span>レベル・字幕</p>
  <p class="product-description mb-3">商品説明がここに表示されます。</p>
  <ul class="list-unstyled mb-3 product-highlights">
    <li class="mb-2"><i class="bi bi-check me-2"></i>各学部ごとの過去10年分の傾向分析</li>
    <li class="mb-2"><i class="bi bi-check me-2"></i>効率的な学習法のステップ解説</li>
    <li class="mb-2"><i class="bi bi-check me-2"></i>合格者体験談と学習計画例</li>
  </ul>
  <button class="btn btn-primary w-100 add-to-cart-btn" data-product-id="" data-product-title="" data-product-price="0" data-product-type="">カートに入れる</button>
</div>

<%= render partial: "purchase/products/preview" %>
<script type="text/javascript" src="/assets/require/product/preview.js"></script>

<script>
  $(document).ready(function() {
    const urlParams = new URLSearchParams(window.location.search);
    const edbaseRef = urlParams.get('edbase_ref');
    if (edbaseRef){
      document.cookie = "edbase_ref=" + edbaseRef;
      localStorage.setItem("edbase_ref", edbaseRef)
      <% if @guest_uuid.present? %>
        localStorage.setItem("guest_uuid", "<%= @guest_uuid %>")
      <% end %>
    }
  });
</script>
