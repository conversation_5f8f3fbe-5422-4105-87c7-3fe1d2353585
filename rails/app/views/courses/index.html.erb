<% content_for :title, "Edbase[エドベース]のマニュアル | Edbase[エドベース]" %>
<% content_for :description, "" %>
<% content_for :og do %>
<% end %>
<% content_for :canonical do %>
  <link rel="canonical" rel="<%= courses_url %>" >
<% end %>
<%= content_for :loading_block do %>
  <div class="loader">
    <div class="loading-animation"></div>
  </div>
<% end %>
<% unless @page_custom_html.empty? %>
  <%= convert_banner(@page_custom_html.html_safe, request, current_user, "courses-index").html_safe %>
<% else %>
  <section>
    <div class="container">
      <div class="row g-4">
        <% @courses.each do |course| %>
          <div class="col-12 col-md-6 col-lg-3">
            <div class="card course-card card-with-popover"
                data-product-id="<%= course.id %>"
                data-product-title="<%= course.name %>"
                data-product-description="<%= truncate(course.description, length: 160) %>"
                data-product-price="<%= course.price || 0 %>"
                data-product-duration="<%= course.duration || 12 %>"
                data-product-level="<%= course.level || '初級' %>"
                data-product-type="Course"
                data-product-updated="<%= course.updated_at.strftime('%Y年%m月') %>">
              <%= link_to course do %>
                <%= image_tag course.image.url || "course-default.png", alt: course.name, class: "card-img-top" %>
              <% end %>
              <div class="card-body d-flex flex-column">
                <div class="mb-2">
                  <% if course.list_names_under("大学").present? %>
                    <% course.list_names_under("大学").each do |item| %>
                      <span class="university-badge bg-primary text-white"><%= item %></span>
                    <% end %>
                  <% end %>

                  <% if course.list_names_under("学校の種類").present? %>
                    <% course.list_names_under("学校の種類").each do |item| %>
                      <span class="free-badge"><%= item %></span>
                    <% end %>
                  <% end %>
                </div>
                <h5 class="card-title truncate">
                  <%= link_to course.name, course, class: "text-decoration-none text-dark" %>
                </h5>
                <p class="small text-muted mb-1">制作講師名:
                  <%= course.teachers&.map { |teacher| "#{teacher&.name}" }.join(', ') %>
                </p>
                <div class="d-flex align-items-center mb-2">
                  <span class="fw-bold me-1">4.8</span>
                  <div class="rating-stars me-1">
                    <i class="bi bi-star-fill"></i>
                    <i class="bi bi-star-fill"></i>
                    <i class="bi bi-star-fill"></i>
                    <i class="bi bi-star-fill"></i>
                    <i class="bi bi-star-half"></i>
                  </div>
                  <span class="text-muted small">(8)</span>
                </div>
                <% course.list_names_summary_excluding_special.each do |item| %>
                  <% item.each do |key, value| %>
                    <% next if value.blank? %>
                    <span>- <%= key %>: <%= value %></span>
                  <% end %>
                <% end %>
                <div class="mt-auto">
                  <div class="d-flex justify-content-between align-items-center">
                    <div>
                      <span class="fw-bold">¥<%= number_with_delimiter course.price %></span>
                    </div>
                    <button class="btn btn-sm btn-outline-primary add-to-cart-btn add-to-cart-btn-index" 
                            data-product-id="<%= course.id %>"
                            data-product-title="<%= course.name %>"
                            data-product-price="<%= course.price || 0 %>"
                            data-product-image="<%= course.image.url %>"
                            data-product-type="Course">
                      カートに入れる
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        <% end %>
        <div class="col-md-4 col-lg-3">
          <h3 class="heading">カテゴリー</h3>
          <ul class="categories">
            <li><%= link_to "すべて", courses_path %></li>

            <% @courses.tag_counts_on(:tags).each do |tag| %>
              <% course_size_of_tag = Course.published.from_master_schools.tagged_with(tag.name).size %>
              <% if course_size_of_tag > 0 %>
                <li>
                  <%= link_to tag.name, courses_path(tag_name: tag.name) %>
                  <span class="lessoncount">(<%= course_size_of_tag %>)</span>
                </li>
              <% end %>
            <% end %>
          </ul>
        </div>
      </div>
      <div class="row justify-content-between align-items-center">
        <%= paginate @courses %>
      </div>
    </div>
  </section>

  <!-- Course Modal -->
  <div class="udemy-popover" style="display: none;">
    <img class="product-image w-100 mb-3" src="" alt="商品詳細">
    <h5 class="mb-3">商品タイトル</h5>
    <p class="mb-2">更新済み <span class="product-updated-date">2025年1月</span></p>
    <p class="mb-2">合計 <span class="product-duration">12</span> 時間・<span class="product-level">初級</span>レベル・字幕</p>
    <p class="product-description mb-3">商品説明がここに表示されます。</p>
    <ul class="list-unstyled mb-3 product-highlights">
      <li class="mb-2"><i class="bi bi-check me-2"></i>各学部ごとの過去10年分の傾向分析</li>
      <li class="mb-2"><i class="bi bi-check me-2"></i>効率的な学習法のステップ解説</li>
      <li class="mb-2"><i class="bi bi-check me-2"></i>合格者体験談と学習計画例</li>
    </ul>
    <button class="btn btn-primary w-100 add-to-cart-btn" data-product-id="" data-product-title="" data-product-price="0" data-product-type="">カートに入れる</button>
  </div>

  <%# Preview Modal %>
  <%= render partial: "purchase/products/preview" %>
<% end %>

<script type="text/javascript" src="/assets/require/product/preview.js"></script>

<style>
  .course-card {
    transition: all 0.3s;
    height: 100%;
    position: relative;
    z-index: 1;
    background-color: #fff;
    border: 1px solid #e6e9ec;
    border-radius: 8px;
  }
  .course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    z-index: 5;
  }
  .rating-stars {
    color: #f4c150;
  }
  .truncate {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .bestseller-badge {
    background-color: #eceb98;
    color: #3d3c0a;
    font-size: 0.7rem;
    padding: 2px 8px;
  }
  .recommended-badge {
    background-color: #e6f7ff;
    color: #0070f3;
    font-size: 0.7rem;
    padding: 2px 8px;
  }
  .popular-badge {
    background-color: #ffe6e6;
    color: #ff4d4f;
    font-size: 0.7rem;
    padding: 2px 8px;
  }
  .free-badge {
    background-color: #f0f9eb;
    color: #52c41a;
    font-size: 0.7rem;
    padding: 2px 8px;
  }
  .paid-badge {
    background-color: #fff7e6;
    color: #fa8c16;
    font-size: 0.7rem;
    padding: 2px 8px;
  }
  .university-badge {
    font-weight: 700;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    display: inline-block;
  }

  .card-with-popover {
    position: relative;
    margin-bottom: 20px;
  }

  .udemy-popover {
    display: none;
    position: absolute;
    width: 320px;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    padding: 20px;
    z-index: 1000;
    border: 1px solid #e8e9eb;
    margin-left: 10px;
    margin-right: 10px;
  }

  .udemy-popover:before {
    content: '';
    position: absolute;
    left: -10px;
    top: 20px;
    width: 0;
    height: 0;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
    border-right: 10px solid white;
    filter: drop-shadow(-3px 0px 2px rgba(0,0,0,0.05));
  }

  .udemy-popover:after {
    content: '';
    position: absolute;
    left: -10px;
    top: 0;
    bottom: 0;
    width: 10px;
    opacity: 0;
  }

  .udemy-popover.right-side:before {
    left: auto;
    right: -10px;
    border-right: none;
    border-left: 10px solid white;
  }

  .udemy-popover.right-side:after {
    left: unset;
    right: -10px;
  }

  .udemy-popover h5 {
    font-weight: 600;
  }

  .udemy-popover .course-image {
    width: 100%;
    border-radius: 4px;
    margin-bottom: 15px;
  }

  .udemy-popover .add-to-cart-btn {
    background-color: #0d6efd;
    border-color: #0d6efd;
  }

  .udemy-popover .add-to-cart-btn:hover {
    background-color:rgb(12, 71, 160);
    border-color:rgb(8, 62, 144);
  }

  /* Toast styling */
  .toast {
    position: fixed;
    bottom: 20px;
    right: 20px;
    min-width: 300px;
  }

  .toast-success {
    background-color: #d1e7dd;
    border-color: #badbcc;
  }
</style>
