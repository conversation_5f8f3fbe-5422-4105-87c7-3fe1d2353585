<% content_for :title, "注文手続き | Edbase[エドベース]" %>
<% content_for :description, "" %>
<% content_for :og do %>
<% end %>
<% content_for :canonical do %>
  <link rel="canonical" rel="<%= courses_url %>" >
<% end %>
<%= content_for :loading_block do %>
  <div class="loader">
    <div class="loading-animation"></div>
  </div>
<% end %>
<% unless @page_custom_html.empty? %>
  <%= convert_banner(@page_custom_html.html_safe, request, current_user, "purchase_thank").html_safe %>
<% else %>
  <div class="thank-you-container mt-5">
    <!-- サンクスカード -->
    <div class="thank-you-card text-center">
      <i class="bi bi-check-circle-fill success-icon"></i>
      <h1 class="display-6 mb-3">ご購入ありがとうございました！</h1>
      <p class="lead mb-4">注文処理が完了しました。購入したコースは「マイラーニング」からアクセスできます。</p>
      <a href="/classroom" class="btn btn-primary start-course-btn px-4">マイラーニングに進む</a>

      <!-- 注文詳細 -->
      <div class="order-details mt-5 text-start">
        <h2 class="h5 mb-3">注文詳細</h2>
        <p class="mb-3">注文番号: <strong>order-<%= @order.id %></strong> | 注文日: <%= @order.created_at.strftime('%Y年%m月%d日') %></p>

        <!-- 注文アイテム一覧 -->
        <% @order_items.each do |item| %>
          <% product_purchase = item.product.productable %>

          <div class="order-item">
            <div class="row">
              <div class="col-md-8">
                <p class="mb-1"><strong><%= product_purchase.name %></strong></p>
                  <p class="text-muted small mb-0">説明: <%= product_purchase.description %></p>
              </div>
              <div class="col-md-4 text-md-end">
                <p class="mb-0">¥<%= number_with_delimiter item.price_at_time %></p>
              </div>
            </div>
          </div>
        <% end %>

        <!-- 注文合計 -->
        <div class="mt-3 pt-3 border-top">
          <div class="row">
            <div class="col-6">
              <p class="mb-0">小計:</p>
            </div>
            <div class="col-6 text-end">
              <%# original_price = @order.order_items.includes(:product).map(&:product).sum(&:price) %>
              <p class="mb-0">¥<%= number_with_delimiter @original_price %></p>
            </div>
          </div>
          <div class="row">
            <div class="col-6">
              <p class="mb-0">割引:</p>
            </div>
            <div class="col-6 text-end">
              <% discount_price = (@original_price - @order.pre_tax_amount) %>
              <% if discount_price > 0 %>
                <p class="mb-0">-¥<%= number_with_delimiter discount_price %></p>
              <% end %>
            </div>
          </div>
          <div class="row mt-2">
            <div class="col-6">
              <p class="mb-0">Tax(10%):</p>
            </div>
            <div class="col-6 text-end">
                <% tax = (@order.total_amount - @order.pre_tax_amount) %>
                <p class="mb-0">¥<%= number_with_delimiter tax %></p>
            </div>
          </div>
          <div class="row mt-2">
            <div class="col-6">
              <p class="mb-0 order-total">合計:</p>
            </div>
            <div class="col-6 text-end">
              <p class="mb-0 order-total">¥<%= number_with_delimiter(@order.total_amount) %></p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 購入したコース -->
    <h2 class="courses-heading">購入したコース</h2>
    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-5 g-4 mb-5">
      <!-- コース1 -->
      <% @product_payment.each do |item| %>
        <% item_purchase = item.product.productable %>

        <div class="col">
        <div class="course-card">
          <% if item_purchase.image.present? %>
            <%= image_tag item_purchase.image.url, alt: item_purchase.name, class: "course-image" %>
          <% end %>
          <div class="p-3">
          <h3 class="course-title"><%= item_purchase.name %></h3>
          <p class="text-muted small mb-3">説明: <%= item_purchase.description %></p>
          <a class="btn btn-primary w-100 start-btn" href="/classroom/my-courses/<%= item_purchase.id %>">コースを始める</a>
          </div>
        </div>
        </div>
      <% end %>
    </div>

    <!-- 次におすすめのアクション -->
    <div class="row mb-5">
      <div class="col-md-6 mb-4 mb-md-0">
        <div class="card h-100">
          <div class="card-body text-center p-4">
            <i class="bi bi-journal-check fs-1 mb-3 text-primary"></i>
            <h3 class="card-title h5 mb-3">学習目標を設定しましょう</h3>
            <p class="card-text">学習計画を立てて、目標達成までのスケジュールを管理できます。</p>
            <a href="#" class="btn btn-outline-primary">学習スケジュールを設定</a>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="card h-100">
          <div class="card-body text-center p-4">
            <i class="bi bi-phone fs-1 mb-3 text-primary"></i>
            <h3 class="card-title h5 mb-3">モバイルアプリをダウンロード</h3>
            <p class="card-text">いつでもどこでも学習できるように、Udemyモバイルアプリをインストールしましょう。</p>
            <div class="d-flex justify-content-center">
              <a href="#" class="me-2"><img src="/api/placeholder/120/40" alt="App Store" height="40"></a>
              <a href="#"><img src="/api/placeholder/120/40" alt="Google Play" height="40"></a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- カスタマーサポート情報 -->
    <div class="text-center mb-5">
      <h3 class="h5 mb-3">ご質問やサポートが必要ですか？</h3>
      <p class="mb-3">何かご不明な点がございましたら、お気軽にカスタマーサポートまでお問い合わせください。</p>
      <a href="#" class="btn btn-outline-dark">ヘルプセンター</a>
    </div>
  </div>
<% end %>

<style>
  body {
    background-color: #f7f9fa;
  }
  .rating-stars {
    color: #f4c150;
  }
  .bg-light-blue {
    background-color: #f0f7f8;
  }
  .cart-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    font-size: 0.7rem;
  }
  .udemy-logo {
    height: 34px;
  }
  .bestseller-badge {
    background-color: #eceb98;
    color: #3d3c0a;
    font-size: 0.7rem;
    padding: 2px 8px;
  }
  .success-icon {
    color: #3cb371;
    font-size: 4rem;
    margin-bottom: 1rem;
  }
  .thank-you-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 15px;
  }
  .thank-you-card {
    background-color: #fff;
    border-radius: 6px;
    padding: 40px;
    margin-bottom: 30px;
    box-shadow: 0 0 10px rgba(0,0,0,0.05);
  }
  .course-card {
    transition: all 0.3s;
    height: 100%;
    background-color: #fff;
    border-radius: 6px;
    box-shadow: 0 0 5px rgba(0,0,0,0.05);
    overflow: hidden;
  }
  .course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
  }
  .course-image {
    width: 100%;
    height: 160px;
    object-fit: cover;
  }
  .start-course-btn {
    background-color: #5624d0;
    border-color: #5624d0;
    font-weight: bold;
    padding: 12px 20px;
  }
  .start-course-btn:hover {
    background-color: #401b9c;
    border-color: #401b9c;
  }
  .order-details {
    margin-top: 24px;
    padding: 20px;
    background-color: #f7f9fa;
    border-radius: 4px;
  }
  .order-item {
    padding: 16px 0;
    border-bottom: 1px solid #e6e9ec;
  }
  .order-item:last-child {
    border-bottom: none;
  }
  .order-total {
    font-weight: bold;
    font-size: 1.2rem;
  }
  /* .receipt-link {
    color: #5624d0;
    text-decoration: none;
  }
  .receipt-link:hover {
    text-decoration: underline;
  } */
  .courses-heading {
    margin-top: 40px;
    margin-bottom: 20px;
    font-weight: bold;
  }
  .course-title {
    font-weight: 700;
    font-size: 1rem;
    color: #1c1d1f;
    margin: 0.5rem 0;
  }
  
  /* レスポンシブ調整 */
  @media (max-width: 768px) {
    .thank-you-card {
      padding: 20px;
    }
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    sessionStorage.removeItem('cart');
    sessionStorage.removeItem('checkout_cart');
    // 領収書ダウンロードリンク
    // const receiptLink = document.querySelector('.receipt-link');
    // if (receiptLink) {
    //   receiptLink.addEventListener('click', function(e) {
    //     e.preventDefault();
    //     alert('領収書のダウンロードを開始します。');
    //   });
    // }

    // // 現在の日付を設定
    // const today = new Date();
    // const options = { year: 'numeric', month: 'long', day: 'numeric' };
    // const dateString = today.toLocaleDateString('ja-JP', options);

    // // 注文日の更新
    // const orderDate = document.querySelector('.order-details p');
    // if (orderDate) {
    //   const orderNumber = orderDate.innerHTML.split('|')[0];
    //   orderDate.innerHTML = `${orderNumber} | 注文日: ${dateString}`;
    // }
  });
</script>
