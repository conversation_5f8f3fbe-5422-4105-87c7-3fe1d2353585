<% content_for :title, "注文手続き | Edbase[エドベース]" %>
<% content_for :description, "" %>
<% content_for :og do %>
<% end %>
<% content_for :canonical do %>
  <link rel="canonical" rel="<%= courses_url %>" >
<% end %>
<%= content_for :loading_block do %>
  <div class="loader">
    <div class="loading-animation"></div>
  </div>
<% end %>
<% unless @page_custom_html.empty? %>
  <%= convert_banner(@page_custom_html.html_safe, request, current_user, "purchase_checkout").html_safe %>
<% else %>
  <!-- メインコンテンツ -->
  <main class="container py-5 mt-5">
    <div class="checkout-container">
      <div class="row g-4">
        <!-- 左側：注文情報 -->
        <div class="col-lg-8">
          <div class="bg-white p-4 rounded mb-4">
            <h2 class="h5 mb-4">お名前をフルネームでご記入ください</h2>
            <div class="mb-4">
              <input type="text" class="form-control" id="customerName" placeholder="例：佐藤 太郎">
            </div>

            <div class="mb-4">
              <div class="form-check">
                <input class="form-check-input agreement-checkbox" type="checkbox" id="agreementCheck">
                <label class="form-check-label" for="agreementCheck">
                  利用規約に同意します
                </label>
              </div>
              <p class="terms-text mt-2 mb-0">下記の受講規約をご確認いただき、同意のうえお申込みください。</p>
            </div>

            <div class="card mb-4">
              <div class="card-body terms-text">
                <p>本規約は、エデュ・プラニング株式会社（以下「当社」といいます。）が提供する「bridge★」（以下「本サービス」といいます。）を利用される際に適用されます。ご利用にあたっては、本規約をお読みいただき、内容をご承諾の上でご利用ください。</p>
                <p>第1条（規約の適用）１本規約は、当社が本サービスを提供するにあたって、利用者が本サービスの提供を受けるにあたっての諸条件を定めたものです。２当社は、本サービスに関して、本規約のほか、本サービスの利用に関する個別規約その他のガイドライン等を定めることがあります。この場合、当該個別規約その他のガイドライン等は、本規約の一部として適用されます。</p>
              </div>
            </div>
          </div>

          <!-- 注文詳細 -->
          <h3 class="h5 mb-3">ご注文詳細 (<span id="itemCount"><%= @products.count %></span>件のコース)</h3>
          <% @products.each do |item| %>
            <% product = item.productable %>

            <div class="d-flex mb-4 pb-3 border-bottom">
              <img src="<%= product.image.url || image_path("course-default.png") %>" class="course-image me-3" alt="コースイメージ">
              <div>
                <h4 class="course-title mb-1"><%= product.name %></h4>
                <div class="d-flex justify-content-between align-items-end">
                  <p class="mb-0 text-muted small">¥<%= number_with_delimiter(item.price) %></p>
                  <p class="mb-0 discount-price"></p>
                </div>
              </div>
            </div>
          <% end %>
        </div>

        <!-- 右側：注文サマリー -->
        <div class="col-lg-4">
          <div class="order-summary">
            <h2 class="h5 mb-4">注文の概要</h2>

            <div class="price-line">
              <span>元の価格:</span>
              <span class="original-price"></span>
            </div>

            <div class="price-line">
              <span class="discount-percent">割引:</span>

              <span class="discount-amount"></span>
            </div>

            <div class="price-line">
              <span class="">Tax(10%):</span>

              <span class="tax-amount"></span>
            </div>

            <div class="price-line border-top pt-3 mt-3">
              <span>合計 (<span id="summaryItemCount product-count"><%= @products.count %></span>件のコース):</span>
              <span class="total-price">¥</span>
            </div>

            <p class="small text-muted mt-3 mb-4">支払いを完了することにより、ご利用のサービス規約に同意したものとみなされます。</p>

            <button id="checkoutButton" class="btn checkout-btn w-100 disabled" disabled>
              <i class="bi bi-lock-fill me-2"></i>決済完了
            </button>

            <div class="mt-4 text-center">
              <p class="mb-1"><strong>30日間返金保証</strong></p>
              <p class="small text-muted mb-0">ご満足いただけない場合は30日以内に全額返金いたします。簡単でシンプルです。一部例外あり。詳しくは<a href="#">返金ポリシー</a>をご参照ください。</p>
            </div>

            <hr class="my-4">

            <div class="small text-muted">
              <p class="mb-1">支払い時期/価格設定については、<a href="#">特定商取引法</a>に基づく表示をご確認ください。</p>
              <p class="mb-0">ご注文内容の変更は、「キャンセル」ボタンまたはブラウザの戻るボタンでショッピングカートに戻ってください。</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>

  <!-- Stripe Payment Modal -->
  <div class="modal fade" id="stripeModal" tabindex="-1" aria-labelledby="stripeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="stripeModalLabel">クレジットカード情報</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="stripe-payment-form">
            <input type="hidden" id="purchase-amount" value="">

            <div class="mb-4">
              <label for="stripe-card-element" class="form-label">カード情報</label>
              <div id="stripe-card-element" class="form-control" style="height: 40px; padding-top: 10px;"></div>
              <div id="stripe-card-errors" class="invalid-feedback" style="display: none;"></div>
            </div>

            <div class="mb-4">
              <div class="d-flex justify-content-between">
                <span>お支払い合計:</span>
                <strong id="payment-total-display">¥</strong>
              </div>
            </div>

            <div class="d-grid gap-2 mt-4">
              <button id="stripe-submit" type="submit" class="btn btn-primary">支払いを確定する</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Completion Loading Modal -->
  <div class="modal fade" id="loadingModal" tabindex="-1" aria-labelledby="loadingModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-body text-center py-4">
          <div class="spinner-border text-primary mb-3" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <h5>お支払い処理中...</h5>
          <p class="text-muted mb-0">ブラウザを閉じたり更新したりしないでください。</p>
        </div>
      </div>
    </div>
  </div>
<% end %>

<style>
  #page-content {
    background-color:rgb(255, 255, 255);
  }

  body {
    background-color: #f7f9fa;
  }
  .rating-stars {
    color: #f4c150;
  }
  .udemy-logo {
    height: 34px;
  }
  .bestseller-badge {
    background-color: #eceb98;
    color: #3d3c0a;
    font-size: 0.7rem;
    padding: 2px 8px;
  }
  .checkout-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
  }
  .course-image {
    width: 100px;
    height: 56px;
    object-fit: cover;
    border-radius: 4px;
  }
  .order-summary {
    background-color: #fff;
    border-radius: 6px;
    padding: 24px;
    box-shadow: 0 0 10px rgba(0,0,0,0.05);
  }
  .checkout-btn {
    background-color: #8710d8;
    border-color: #8710d8;
    font-weight: bold;
    padding: 12px;
    color: white;
  }
  .checkout-btn:hover {
    background-color: #7212b3;
    border-color: #7212b3;
    color: white;
  }
  .checkout-btn.disabled {
    cursor: not-allowed;
  }
  .terms-text {
    font-size: 0.85rem;
    color: #6a6f73;
  }
  .agreement-checkbox {
    margin-right: 8px;
  }
  .course-title {
    font-weight: 700;
    font-size: 1rem;
    color: #1c1d1f;
  }
  .price-line {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
  }
  .discount-price {
    font-weight: bold;
    color: #1c1d1f;
  }
  .original-price {
    color: #6a6f73;
  }
  .total-price {
    font-size: 1.5rem;
    font-weight: bold;
    color: #1c1d1f;
  }

  /* Stripe Element styling */
  .StripeElement {
    height: 40px;
    padding: 10px 12px;
    border: 1px solid #e3e6f0;
    border-radius: 4px;
    background-color: white;
  }

  .StripeElement--focus {
    border-color: #8710d8;
    box-shadow: 0 0 0 0.25rem rgba(135, 16, 216, 0.25);
  }

  .StripeElement--invalid {
    border-color: #dc3545;
  }

  /* レスポンシブ調整 */
  @media (max-width: 768px) {
    .order-summary {
      margin-top: 2rem;
    }
  }
</style>

<script src="https://js.stripe.com/v3/"></script>
<script>
  document.addEventListener('DOMContentLoaded', function () {
    const customerNameInput = document.getElementById('customerName');
    const agreementCheck = document.getElementById('agreementCheck');
    const checkoutButton = document.getElementById('checkoutButton');

    // Function to validate the form
    function validateForm() {
      const customerName = customerNameInput.value.trim();
      const isAgreementChecked = agreementCheck.checked;

      // Enable or disable the checkout button based on validation
      if (customerName && isAgreementChecked) {
        checkoutButton.disabled = false;
        checkoutButton.classList.remove('disabled');
      } else {
        checkoutButton.disabled = true;
        checkoutButton.classList.add('disabled');
      }
    }

    // Add event listeners to validate on input or checkbox change
    customerNameInput.addEventListener('input', validateForm);
    agreementCheck.addEventListener('change', validateForm);

    // Initial validation on page load
    validateForm();
  });

  document.addEventListener('DOMContentLoaded', function() {
    // Initialize Stripe
    const stripe = Stripe('<%= Rails.configuration.stripe[:publishable_key] %>');
    let cardElement = null;
    let cardElementMounted = false;

    // Mount card element
    function mountCardElement() {
      if (cardElementMounted) return;

      const elements = stripe.elements();

      cardElement = elements.create('card', {
        style: {
          base: {
            fontSize: '16px',
            color: '#32325d',
            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
            '::placeholder': { color: '#aab7c4' }
          },
          invalid: {
            color: '#dc3545',
            iconColor: '#dc3545'
          }
        },
        hidePostalCode: true
      });

      cardElement.mount('#stripe-card-element');
      cardElementMounted = true;

      cardElement.on('change', function(event) {
        const displayError = document.getElementById('stripe-card-errors');
        if (displayError) {
          if (event.error) {
            displayError.textContent = event.error.message;
            displayError.style.display = 'block';
          } else {
            displayError.textContent = '';
            displayError.style.display = 'none';
          }
        }
      });
    }

    // Unmount card element
    function unmountCardElement() {
      if (cardElement && cardElementMounted) {
        cardElement.unmount();
        cardElementMounted = false;
      }
    }

    // Handle checkout button click
    document.getElementById('checkoutButton').addEventListener('click', function() {
      // Validate customer name
      const customerName = document.getElementById('customerName').value.trim();
      if (!customerName) {
        alert('お名前を入力してください');
        return;
      }

      // Validate agreement checkbox
      const agreementCheck = document.getElementById('agreementCheck');
      if (!agreementCheck.checked) {
        alert('利用規約に同意してください');
        return;
      }

      // Show the Stripe modal
      const stripeModal = new bootstrap.Modal(document.getElementById('stripeModal'));
      stripeModal.show();

      // Mount card element after modal is shown
      setTimeout(mountCardElement, 300);
    });

    // Handle modal close
    document.getElementById('stripeModal').addEventListener('hidden.bs.modal', function() {
      unmountCardElement();
    });

    // Handle payment form submission
    document.getElementById('stripe-payment-form').addEventListener('submit', async function(event) {
      event.preventDefault();

      const submitButton = document.getElementById('stripe-submit');
      const errorDisplay = document.getElementById('stripe-card-errors');

      // Disable button and show loading state
      submitButton.disabled = true;
      submitButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>処理中...';

      try {
        const customerName = document.getElementById('customerName').value.trim();
        const amount = document.getElementById('purchase-amount').value;

        const { paymentMethod, error } = await stripe.createPaymentMethod({
          type: 'card',
          card: cardElement,
          billing_details: {
            name: customerName,
            email: '<%= current_user&.email || "" %>'
          }
        });

        if (error) {
          throw error;
        }

        // Hide the Stripe modal
        const stripeModal = bootstrap.Modal.getInstance(document.getElementById('stripeModal'));
        stripeModal.hide();

        // Show loading modal
        const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
        loadingModal.show();

        // Send payment info to server
        const response = await fetch('/purchase/payments', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
          },
          body: JSON.stringify({
            payment_method_id: paymentMethod.id,
            amount: amount,
            customer_name: customerName
          })
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'サーバーエラーが発生しました。もう一度お試しください。');
        }

        if (data.requires_action && data.client_secret && data.amount > 0 ) {
          const confirmResult = await stripe.confirmCardPayment(data.client_secret);

          if (confirmResult.error) {
            throw confirmResult.error;
          } else if (confirmResult.paymentIntent &&
                    confirmResult.paymentIntent.status === 'succeeded') {
            handleSuccessfulPayment(data.payment_intent, data.amount);
          } else {
            throw new Error('お支払いが完了していません。もう一度お試しください。');
          }
        } else if (data.amount == 0) {
          handleSuccessfulPayment("", data.amount);
        } else {
          handleSuccessfulPayment(data.payment_intent);
        }

      } catch (error) {
        // Hide loading modal if visible
        const loadingModalElement = document.getElementById('loadingModal');
        if (loadingModalElement) {
          const loadingModalInstance = bootstrap.Modal.getInstance(loadingModalElement);
          if (loadingModalInstance) {
            loadingModalInstance.hide();
          }
        }

        // Show Stripe modal again
        const stripeModal = new bootstrap.Modal(document.getElementById('stripeModal'));
        stripeModal.show();

        setTimeout(mountCardElement, 300);

        errorDisplay.textContent = error.message || 'お支払い処理中にエラーが発生しました。もう一度お試しください。';
        errorDisplay.style.display = 'block';

        // Reset button
        submitButton.disabled = false;
        submitButton.innerHTML = '支払いを確定する';

        console.error('Payment error:', error);
      }
    });

    async function handleSuccessfulPayment(payment_intent, amount) {
      // Retrieve coupon code from sessionStorage
      let couponCode = '';

      try {
        const checkoutCartData = sessionStorage.getItem('checkout_cart');
        if (checkoutCartData) {
          const cartData = JSON.parse(checkoutCartData);
          couponCode = cartData.couponCode || '';
        }
      } catch (e) {
        console.error('Error retrieving coupon data from session storage:', e);
      }

      // Create order, payment
      const response = await fetch('/purchase/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        },
        body: JSON.stringify({
          payment_intent: payment_intent,
          coupon_code: couponCode,
          amount: amount
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error);
      }

      sessionStorage.removeItem('cart');
      sessionStorage.removeItem('checkout_cart');

      window.location.href = `/purchase/payments/thanks_page?order_id=${data.order_id}`;
    }
  });

  document.addEventListener('DOMContentLoaded', function() {
    // Function to fill checkout summary with data from session storage
    function fillCheckoutSummary() {
      try {
        const savedCart = sessionStorage.getItem('cart');
        const checkoutCartData = sessionStorage.getItem('checkout_cart');

        if (savedCart && checkoutCartData) {
          const cart = JSON.parse(savedCart);
          const checkoutCart = JSON.parse(checkoutCartData);

          // Format numbers with commas
          const formatNumber = (num) => {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
          };

          // Check if the number of items in cart and checkout_cart are different
          const cartItemsCount = cart.items ? cart.items.length : 0;
          const checkoutItemsCount = checkoutCart.products ? checkoutCart.products.length : 0;

          if (cartItemsCount !== checkoutItemsCount) {
            console.log("Cart and checkout_cart have different item counts. Updating checkout_cart...");

            // Update checkout_cart with the latest cart data
            const updatedCheckoutCart = {
              subtotal: cart.total || 0,
              discountAmount: 0,
              discountPercent: 0,
              total: cart.total || 0,
              couponCode: checkoutCart.couponCode || "",
              products: []
            };

            // Map cart items to checkout_cart products format
            if (cart.items && cart.items.length > 0) {
              updatedCheckoutCart.products = cart.items.map(item => ({
                id: item.id,
                type: item.type,
                price: item.price,
                discountAmount: 0,
                currentPrice: item.price
              }));
            }

            // If there's a coupon code in the original checkout_cart, apply it
            if (checkoutCart.couponCode) {
              // Re-apply coupon to get updated discount
              applyCouponToCheckout(updatedCheckoutCart, checkoutCart.couponCode);
            }

            // Calculate tax
            updatedCheckoutCart.taxAmount = Math.round(updatedCheckoutCart.total * 0.1);
            updatedCheckoutCart.totalWithTax = updatedCheckoutCart.total + updatedCheckoutCart.taxAmount;

            // Save updated checkout_cart back to sessionStorage
            sessionStorage.setItem('checkout_cart', JSON.stringify(updatedCheckoutCart));

            // Use the updated data
            fillCheckoutWithData(updatedCheckoutCart);
          } else {
            // No discrepancy, use the existing checkout_cart data
            fillCheckoutWithData(checkoutCart);
          }
        } else if (savedCart) {
          // Only cart exists, no checkout_cart - create one
          const cart = JSON.parse(savedCart);

          const newCheckoutCart = {
            subtotal: cart.total || 0,
            discountAmount: 0,
            discountPercent: 0,
            total: cart.total || 0,
            couponCode: "",
            products: []
          };

          // Map cart items to checkout_cart products format
          if (cart.items && cart.items.length > 0) {
            newCheckoutCart.products = cart.items.map(item => ({
              id: item.id,
              type: item.type,
              price: item.price,
              discountAmount: 0,
              currentPrice: item.price
            }));
          }

          // Apply coupon if exists in cart
          if (cart.coupon) {
            newCheckoutCart.couponCode = cart.coupon;

            // If the cart already has discount info, use it
            if (cart.discountAmount) {
              newCheckoutCart.discountAmount = cart.discountAmount;
              newCheckoutCart.discountPercent = cart.discountPercent || 0;
              newCheckoutCart.total = newCheckoutCart.subtotal - newCheckoutCart.discountAmount;
            }
          }

          // Calculate tax
          newCheckoutCart.taxAmount = Math.round(newCheckoutCart.total * 0.1);
          newCheckoutCart.totalWithTax = newCheckoutCart.total + newCheckoutCart.taxAmount;

          // Save new checkout_cart to sessionStorage
          sessionStorage.setItem('checkout_cart', JSON.stringify(newCheckoutCart));

          // Use the new data
          fillCheckoutWithData(newCheckoutCart);
        } else if (checkoutCartData) {
          // Only checkout_cart exists, no cart - use it directly
          fillCheckoutWithData(JSON.parse(checkoutCartData));
        }
      } catch (e) {
        console.error('Error loading or processing checkout data:', e);
      }
    }

    // Function to apply coupon to checkout cart
    function applyCouponToCheckout(checkoutCart, couponCode) {
      // This would ideally call the server, but for client-side calculation:
      if (!couponCode) return;

      // Get stored coupon info from cart if available
      const savedCart = sessionStorage.getItem('cart');
      if (savedCart) {
        const cart = JSON.parse(savedCart);

        if (cart.coupon && cart.coupon === couponCode && cart.discountAmount) {
          // Use existing discount calculation from cart
          checkoutCart.discountAmount = cart.discountAmount;
          checkoutCart.discountPercent = cart.discountPercent || 0;
          checkoutCart.total = checkoutCart.subtotal - checkoutCart.discountAmount;
          
          // Recalculate tax based on the discounted amount
          checkoutCart.taxAmount = Math.round(checkoutCart.total * 0.1);
          checkoutCart.totalWithTax = checkoutCart.total + checkoutCart.taxAmount;
          
          return;
        }
      }

      // If we don't have discount info from cart, make a server request
      fetch('/purchase/cart/apply_coupon', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        },
        body: JSON.stringify({ 
          coupon_code: couponCode,
          product_ids: checkoutCart.products.map(p => ({ id: p.id, type: p.type }))
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          // Calculate total discount
          let totalDiscount = 0;

          // Apply discount to individual products
          data.applicable_products.forEach(appliedProduct => {
            const productIndex = checkoutCart.products.findIndex(p => 
              p.id.toString() === appliedProduct.product_id.toString() && 
              p.type === appliedProduct.product_type
            );

            if (productIndex !== -1) {
              const product = checkoutCart.products[productIndex];
              product.discountAmount = appliedProduct.discount_amount;
              product.currentPrice = product.price - product.discountAmount;
              totalDiscount += product.discountAmount;
            }
          });

          // Update checkout cart totals
          checkoutCart.discountAmount = totalDiscount;
          checkoutCart.total = checkoutCart.subtotal - totalDiscount;

          if (checkoutCart.subtotal > 0) {
            checkoutCart.discountPercent = Math.round((totalDiscount / checkoutCart.subtotal) * 100);
          } else {
            checkoutCart.discountPercent = 0;
          }

          // Calculate tax on the discounted amount
          checkoutCart.taxAmount = Math.round(checkoutCart.total * 0.1);
          checkoutCart.totalWithTax = checkoutCart.total + checkoutCart.taxAmount;

          // Save updated checkout cart
          sessionStorage.setItem('checkout_cart', JSON.stringify(checkoutCart));

          // Update display
          fillCheckoutWithData(checkoutCart);
        }
      })
      .catch(error => console.error('Error applying coupon:', error));
    }

    // Helper function to fill checkout UI with data
    function fillCheckoutWithData(cartData) {
      const formatNumber = (num) => {
        return Math.round(num).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      };

      // Calculate subtotal (before tax)
      const subtotal = cartData.subtotal || 0;
      const discountAmount = cartData.discountAmount || 0;

      // Calculate price after discount but before tax
      const priceAfterDiscount = subtotal - discountAmount;

      // Calculate 10% tax
      const taxAmount = Math.round(priceAfterDiscount * 0.1);

      // Calculate final total (price after discount + tax)
      const totalWithTax = priceAfterDiscount + taxAmount;

      // Store the updated total with tax
      cartData.taxAmount = taxAmount;
      cartData.totalWithTax = totalWithTax;

      // Fill in original price
      const originalPriceEl = document.querySelector('.original-price');
      if (originalPriceEl) {
        originalPriceEl.textContent = `¥${formatNumber(subtotal)}`;
      }

      // Apply discounts to individual products if there are any
      if (cartData.products && cartData.products.length > 0) {
        // Get all product elements
        const productElements = document.querySelectorAll('.col-lg-8 .d-flex.mb-4');

        // Loop through each product element
        productElements.forEach((productEl, index) => {
          // Get the product data from cart
          const product = cartData.products[index];
          if (!product) return;

          // Check if this product has a discount
          if (product.discountAmount && product.discountAmount > 0) {
            // Get the discount-price element
            const discountPriceEl = productEl.querySelector('.discount-price');

            if (discountPriceEl) {
              // Calculate the discounted price
              const discountedPrice = product.price - product.discountAmount;

              // Display the discounted price
              discountPriceEl.textContent = `¥${formatNumber(discountedPrice)}`;

              // Cross out the original price
              const originalPriceEl = productEl.querySelector('.text-muted.small');
              if (originalPriceEl) {
                originalPriceEl.style.textDecoration = 'line-through';
              }
            }
          }
        });
      }

      // Fill in discount percentage and amount
      const discountPercentEl = document.querySelector('.discount-percent');
      const discountAmountEl = document.querySelector('.discount-amount');
      const discountRow = document.querySelector('.price-line:nth-child(2)');

      if (discountPercentEl && discountAmountEl) {
        if (discountAmount > 0) {
          discountPercentEl.textContent = `割引 (${cartData.discountPercent}%OFF) :`;
          discountAmountEl.textContent = `-¥${formatNumber(discountAmount)}`;

          // Show the discount line
          if (discountRow) discountRow.style.display = 'flex';
        } else {
          // Hide the discount line if no discount
          if (discountRow) discountRow.style.display = 'none';
        }
      }
      
      // Fill in tax amount
      const taxAmountEl = document.querySelector('.tax-amount');
      if (taxAmountEl) {
        taxAmountEl.textContent = `¥${formatNumber(taxAmount)}`;
      }

      // Fill in product count
      const productCountEls = document.querySelectorAll('#summaryItemCount, .product-count, #itemCount');
      const productCount = cartData.products ? cartData.products.length : 0;
      productCountEls.forEach(el => {
        if (el) el.textContent = productCount;
      });

      // Fill in total price (including tax)
      const totalPriceEl = document.querySelector('.total-price');
      if (totalPriceEl) {
        totalPriceEl.textContent = `¥${formatNumber(totalWithTax)}`;

        // Also update the hidden input for Stripe payment
        const purchaseAmountInput = document.getElementById('purchase-amount');
        if (purchaseAmountInput) {
          purchaseAmountInput.value = totalWithTax;
        }

        // Update payment total display in Stripe modal
        const paymentTotalDisplay = document.getElementById('payment-total-display');
        if (paymentTotalDisplay) {
          paymentTotalDisplay.textContent = `¥${formatNumber(totalWithTax)}`;
        }
      }

      // Save the updated cart data with tax information back to session storage
      sessionStorage.setItem('checkout_cart', JSON.stringify(cartData));

      console.log("Checkout data loaded:", cartData);
    }

    // Call the function to fill checkout summary
    fillCheckoutSummary();
  });
</script>
