class SchoolManager

  def self.init_from_request request, current_page = :home
    # logger = Logger.new "log/school_manager.log"
    domain = request.host

    if true
      # logger.info "Request from #{request.host} In Subdomain"
      # subdomain = domain.gsub("." + Settings.default_domain, "")
      Current.is_custom_domain = true
      # schools = School.where(domain: domain)
      # schools = schools.or(School.where(subdomain: subdomain)) if subdomain.present?
      Current.main_school = School.find_by(id: 26)
    else
      # logger.info "Request from #{request.host} In MainDomain"
      Current.is_custom_domain = false
      Current.main_school = School.find_by(id: School::MAIN_DOMAIN_ID)
    end
    Current.ip_address = request.remote_ip || ""
    Current.session_hash = request.session_options[:id] || ""
    Current.schools = Current.main_school ? [Current.main_school] : []
    Current.current_page = current_page || :home
  end

  def self.set_loaded_school_ids(ids)
    Current.loaded_school_ids = ids || []
  end
  def self.loaded_school_ids
    Current.loaded_school_ids
  end

  def self.ip_address
    Current.ip_address
  end

  def self.session_hash
    Current.session_hash
  end

  def self.is_admin_page
    Current.current_page == :admin
  end

  def self.is_classroom
    Current.current_page == :classroom
  end

  def self.is_home
    Current.current_page == :home
  end

  def self.is_custom_domain
    Current.is_custom_domain
  end

  def self.main_school
    Current.main_school
  end

  def self.schools
    Current.schools
  end

  def self.school_ids
    UtilityHelper.to_ids(SchoolManager.schools)
  end

  def self.courses
    Course.where('courses.school_id IN (?)', SchoolManager.school_ids)
  end

  def self.lessons
    Lesson.joins(:course_lessons, :courses).where("courses.school_id IN (?)", SchoolManager.school_ids)
  end

  def self.course_ids
    UtilityHelper.to_ids(SchoolManager.courses)
  end

  def self.blogs
    Blog.where('blogs.school_id IN (?)', SchoolManager.school_ids)
  end

  def self.blog_ids
    UtilityHelper.to_ids(SchoolManager.blogs)
  end

  def self.exams
    Exam.where('exams.school_id IN (?)', SchoolManager.school_ids)
  end

  def self.exam_ids
    UtilityHelper.to_ids(SchoolManager.exams)
  end

  def self.goals
    Goal.where('goals.school_id IN (?)', SchoolManager.school_ids)
  end

  def self.goal_ids
    UtilityHelper.to_ids(SchoolManager.goals)
  end

  def self.master_milestones
    MasterMilestone.where('master_milestones.goal_id IN (?)', SchoolManager.goal_ids)
  end

  def self.master_milestone_ids
    UtilityHelper.to_ids(SchoolManager.master_milestones)
  end

  def self.questions
    Question.where('questions.school_id IN (?)', SchoolManager.school_ids)
  end

  def self.reviews
    Review.where('reviews.school_id IN (?)', SchoolManager.school_ids)
  end

  def self.question_ids
    UtilityHelper.to_ids(SchoolManager.questions)
  end

  def self.inquiries
    Inquiry.where('inquiries.school_id IN (?)', SchoolManager.school_ids)
  end

  def self.inquiry_ids
    UtilityHelper.to_ids(SchoolManager.inquiries)
  end

  def self.premium_services
    PremiumService.published.where('premium_services.school_id IN (?)', SchoolManager.school_ids)
  end

  def self.premium_services_all
    PremiumService.where('premium_services.school_id IN (?)', SchoolManager.school_ids)
  end

  def self.premium_service_ids
    UtilityHelper.to_ids(SchoolManager.premium_services)
  end

  def self.page_customs
    PageCustom.where('page_customs.school_id IN (?)', SchoolManager.school_ids)
  end

  def self.teachers
    Teacher.published.where('teachers.school_id IN (?)', SchoolManager.school_ids)
  end

  # Get last impression and add schoolid in it
  def self.update_impression
    # impressionistライブラリーはrails 7 サポートされてないため、一旦そうします。
    # Update: 2023-01-19　https://github.com/charlotte-ruby/impressionist/issues/302#issuecomment-1381167188
    impression = Impression.where(ip_address: Current.ip_address, session_hash: Current.session_hash.to_s).last
    if impression.present?
      impression.school_id = SchoolManager.main_school.id
      impression.save
    end
  end

  def self.create_impression(impressionable)
    Impression.create(impressionable: impressionable, ip_address: Current.ip_address, session_hash: Current.session_hash.to_s, school_id: SchoolManager.main_school.id)
    impressionable.update(impressions_count: Impression.where(impressionable: impressionable, school_id: SchoolManager.main_school.id).size)
  end
end
