ja:
  views:
    pagination:
      truncate: "..."
      first: "&laquo;"
      last: "&raquo;"
      next: "&#155;"
      previous: "&#139;"
  activerecord:
    models:
      exam: 'テスト'
      school: "グループ"
      goal: "ゴール"
      school_evaluation: "評価"
      school_user: "グループ内ユーザー"
      school_exam: テスト
      user_goal: "ゴールユーザー"
      evaluation: "評価"
      evaluation_sheet: "評価シート"
      evaluation_item: "評価シート内容"
      evaluation_user: "評価ユーザー"
      user: "ユーザー"
      skill: スキル
      blog: ブログ
      premium_service: プレミアムサービス
      question: 質問
    attributes:
      catalog:
        name: 名前
      user_exam:
        started_at: 日付
        position: 順位
        score: 得点
        passed: 合格
      evaluation_user:
        school_evaluation_id: "ユーザー"
      school_evaluation:
        image: 'イメージ'
      limitation:
        users: '人数の制限'
        evaluation: '評価機能'
      evaluation_limitation:
        users: '人数の制限'
      announcement_mail:
        title: 'タイトル'
        body: '本文'
      course:
        name: "名前"
        body: '本文'
        description: '概要'
        image: 'イメージ'
        master_milestones: 'マイルストーン'
        duration: '期間'
        type_is: 'タイプ'
        level: 'レベル'
      blog:
        title: "タイトル"
        body: '本文'
        description: '概要'
        image: 'イメージ'
        publish: '公開する'
        duration: '期間'
        published_at: 公開
      banner: 
        title: "タイトル"
        content: '本文'
        positions: '場所'
      infor: 
        title: "タイトル"
        content: '本文'
        published: "公開"
      exam:
        name: 'テスト名'
        published: '公開'
        created_at: '作成日'
        users_count: '参加人数'
        average: '平均点'
        pass_rate: '合格率'
        body: '本文'
        master_milestone_id: 'マイルストーン'
        detect_cheating: 'カンニング防止機能'
        average_score_title: "タイトル"
        done_score_title: "タイトル"
        public_type: 公開モード
        user_exam_type: テストユーザーのタイプ
        pass_score: "合格基準"
        difficult_rate: "難易度"
        reexam_id: 再テスト
        context: "コンテクスト"
        time_limit: "制限時間"
        mail_title: "タイトル"
        mail_body: "内容"
      evaluation:
        id: "Id"
        status: "ステータス"
        name: "評価シート"
        duration: "期間"
        evaluatee: "ユーザー名"
        evaluator: "評価者"
        reviewers: "閲覧者"
        evaluation_sheet: "評価シート"
        evaluation_sheet_id: "評価シート"
        duration: "期間"
        start_at: "期間"
        score: "合計"
        end_at: "期間まで"
        submit: "申請"
        confirm: "承認"
        unconfirm: "差し戻し"
        cancel: "キャンセル"
      evaluation_sheet:
        id: "Id"
        status: "ステータス"
        name: "評価シート名"
        duration: "期間"
        start_at: "期間から"
        end_at: "期間まで"
        evaluation_items: "評価シート内容"
        register: "登録"
      evaluation_item:
        name: "項目"
        rate: "重み"
        evaluation_gradings: "尺度"
        description: "備考欄"
      evaluation_items:
        name: "項目"
        rate: "重み"
        evaluation_gradings: "尺度"
        description: "備考欄"
      evaluation_items:
        name: "項目"
        rate: "レート"
        evaluation_gradings: "尺度"
        description: "備考欄"
        pass_score: "合格点"
        difficult_rate: "難易度"
        reexam_id: 再テスト
      lesson:
        name: "名前"
        body: '本文'
        description: '概要'
        video: 'ビデオ'
        duration: '再生時間'
        skill_item_manager: 'スキルアイテム'
        published_at: 公開
      chapter:
        name: チャプター名
        description: チャプター説明
      user:
        email: メールアドレス
        name: 名前
        password: パスワード
        password_confirmation: パスワード（再入力）
        remember_me: 次回からパスワード入力を省く
        image: 画像
        plan: プラン
        role: ロール
        ranking: ランキング
        score: スコア
      user_goal:
        message: 'メッセージ'
      master_milestone:
        name: 'マイルストーン名'
        body: '本文'
        target_date: 'ターゲット期日'
        milestone_course: '講座'
      milestone:
        name: 'マイルストーン名'
        body: '本文'
        target_date: 'ターゲット期日'
        milestone_course: '講座'
      list:
        name: カテゴリー名
        description: 説明
        parent: 親カテゴリー
      school:
        name: '名前'
        body: '本文'
        description: '概要'
        domain: 'ドメイン'
        image: 'イメージ'
        limitation: '人数の制限'
        duration: '周期'
        duration_start_at: '周期の初日'
        plans: 'プラン'
        subdomain: サブドメイン
        whitelisted_ips: IPホワイトリスト
        merchant_info: Stripeアカウントの連携
        fee_percent: アプリのフリー
        merchant_info_type: 振り込みの設定
        merchant_info_uid: 振り込み情報
        contract_type: 手数料設定
        rate_value: 割り合い（％）
        fee_value: 割り合い or 金額
        lazy_sign_up: レイジーサインアップ
        allow_user_edit_group: ユーザーブロック表示
        use_goal: ユーザーゴール
        registerable: ユーザーサインアップ
        use_premium_service: プレミアムサービス
        use_course_lesson: コース・レッスン機能
        use_user_question: 質問機能
        use_blog: ブログ
        use_mail: メール機能
        use_skill: スキル機能
        use_user_dashboard: ユーザのダッシュボード
        use_user_graph: テストのダッシュボード
        use_dashboard: スクールのダッシュボード
        use_exam: テスト
        use_exam_stock: テストストック
        use_exam_recommendation: おすすめテスト
        use_banner: バナー機能
        use_meeting: 面談機能
        use_meeting_admin: 管理者面談機能
        use_ai_chat_lesson: レッスンAI機能を使用する
        use_ai_exam_generation: テストAI機能を使用する
        use_ai_chat_goal: ゴールAI機能を使用する
        use_ai_chat_exam: テスト結果でAI機能を使用する
        use_ai_grading: AI テスト採点を使用する
        use_vector_button: ベクター生成
        use_evaluate_lesson: レッスン評価機能を使用する
        use_user_list: ユーザーリスト
        use_user_blocks: ユーザーブロック
        use_teacher_list: ティーチャーリスト
        use_iams: IAMs
        use_affiliates: アフィリエイト
        use_catalog: コースカタログを使用する
        use_exam_prompt: テストプロンプトを使用する
        use_question_list: 質問リスト
        use_course_question: コース質問を使用する
        use_user_group: ユーザーグループを使用する
        use_signup_email_template: サインアップメールテンプレートを使用する
        use_step_email: ステップメールを使用する
        use_coupon: クーポンを使用する
        use_access_report: アクセスレポートを使用する
        use_school_designs_code_editor: コードエディターを使用する
        use_school_designs_general: 一般デザインを使用する
        use_lists: カテゴリーを使用する
        use_terms: 利用規約を使用する
        use_activities: アクティビティを使用する
        use_custom_texts: カスタムテキストを使用する
        use_ai_chat: AIチャットを使用する
        use_ai_chat_question: AIチャット質問を使用する
        use_ai_model_api: AIモデルAPIを使用する
        use_school_onboarding: スクールオンボーディングを使用する
        use_school_plan: スクールプランを使用する
      school_plan:
        lazy_sign_up: レイジーサインアップ
        allow_user_edit_group: ユーザーブロック表示
        use_goal: ユーザーゴール
        registerable: ユーザーサインアップ
        use_premium_service: プレミアムサービス
        use_course_lesson: コース・レッスン機能
        use_user_question: 質問機能
        use_blog: ブログ
        use_mail: メール機能
        use_skill: スキル機能
        use_user_dashboard: ユーザのダッシュボード
        use_user_graph: テストのダッシュボード
        use_dashboard: スクールのダッシュボード
        use_exam: テスト
        use_exam_stock: テストストック
        use_exam_recommendation: おすすめテスト
        use_banner: バナー機能
        use_meeting: 面談機能
        use_meeting_admin: 管理者面談機能
        use_ai_chat_lesson: レッスンAI機能を使用する
        use_ai_exam_generation: テストAI機能を使用する
        use_ai_chat_goal: ゴールAI機能を使用する
        use_ai_chat_exam: テスト結果でAI機能を使用する
        use_ai_grading: AI テスト採点を使用する
        use_vector_button: ベクター生成
        use_evaluate_lesson: レッスン評価機能を使用する
        use_user_list: ユーザーリスト
        use_user_blocks: ユーザーブロック
        use_teacher_list: ティーチャーリスト
        use_iams: IAMs
        use_affiliates: アフィリエイト
        use_catalog: コースカタログを使用する
        use_exam_prompt: テストプロンプトを使用する
        use_question_list: 質問リスト
        use_course_question: コース質問を使用する
        use_user_group: ユーザーグループを使用する
        use_signup_email_template: サインアップメールテンプレートを使用する
        use_step_email: ステップメールを使用する
        use_coupon: クーポンを使用する
        use_access_report: アクセスレポートを使用する
        use_school_designs_code_editor: コードエディターを使用する
        use_school_designs_general: 一般デザインを使用する
        use_lists: カテゴリーを使用する
        use_terms: 利用規約を使用する
        use_activities: アクティビティを使用する
        use_custom_texts: カスタムテキストを使用する
        use_ai_chat: AIチャットを使用する
        use_ai_chat_question: AIチャット質問を使用する
        use_ai_model_api: AIモデルAPIを使用する
        use_school_onboarding: スクールオンボーディングを使用する
        use_school_plan: スクールプランを使用する
      skill_item:
        name: '名前'
        description: '概要'
      goal:
        name: 'ゴール名'
        body: '本文'
        description: '概要'
        image: 'イメージ'
      project:
        name: 'ゴール名'
        body: '本文'
        description: '概要'
        image: 'イメージ'
        project_id: 'プロジェクト'
        price: 原価
      goal_limitation:
        users: '人数の制限'
      manager:
        user_id: 'ユーザー'
      q:
        body: '本文'
        explanation: '解説'
        skill_item: 'スキル'
      skill:
        name: '名前'
        description: '概要'
      plan:
        name: '名前'
        goal: 'ゴール'
      exam_average_score:
        score: '点数'
      exam_done_score:
        score: '点数'
      inquiry:
        name: "お名前"
        body: 'お問い合わせ内容'
        email: 'メールアドレス'
      question:
        title: '質問のタイトル'
        body: '質問の内容'
        category: '質問のカテゴリー'
        question_type: 'プライベート質問 ＊この質問は一般には公開されません'
      page_custom:
        title: 'タイトル'
        design_content: 'デザイン内容'
        page_template_id: 'テンプレート'
      stripe_account:
        email: メールアドレス
        status: アカウント状態
        first_name_kanji: お名前(名)
        last_name_kanji: お名前(姓) 
        dob: 生年月日
        gender: 性別
        phone_number: 電話番号
        phone: 電話番号
        address_kanji_postal_code: 郵便番号
        address_kanji_state: 都道府県
        address_kanji_city: 市区郡
        address_kanji_town: 町村名
        address_kanji_line1: 丁目、番地
        address_kanji_line2: 物件名
        business_profile_mcc: サービス区分
        business_profile_product_description: サービス説明
        business_profile_url: サービスURL
        bank: 銀行名
        branch: 支店
        account_holder_name: お名前
        account_number: 口座番号
        attachment: 証明書（前面）
        back_attachment: 証明書（後面）
        acceptance: 利用ポリシーを同意します
      step_email:
        title: メールタイトル
      step_email_item:
        subject: メールタイトル
      meeting_events:
        status:
          pending: 保留中
          approved: 承認済み
          rejected: 却下
          canceled: キャンセル
          done: 完了
      meeting/coupon:
        code: クーポンコード
        discount_rate: 割引率
        type_discount: 割引タイプ
        expired_at: 有効期限
        type_discounts:
          percentage: パーセント割引
          fixed_amount: 固定金額割引
    errors:
      messages:
        record_invalid: "バリデーションに失敗しました: %{errors}"
        restrict_dependent_destroy:
          has_one: "%{record}が存在しているので削除できません"
          has_many: "%{record}が存在しているので削除できません"
        overlap: "がかぶっています"
        invalid_datetime: "は時間に設定して下さい"
  activemodel:
    attributes:
      subscription_plan:
        name: プラン名
        nickname: 説明
        amount: 金額
  course:
    success_create: 'やった！できたぞ講座'
  date:
    abbr_day_names:
    - 日
    - 月
    - 火
    - 水
    - 木
    - 金
    - 土
    abbr_month_names:
    -
    - 1月
    - 2月
    - 3月
    - 4月
    - 5月
    - 6月
    - 7月
    - 8月
    - 9月
    - 10月
    - 11月
    - 12月
    day_names:
    - 日曜日
    - 月曜日
    - 火曜日
    - 水曜日
    - 木曜日
    - 金曜日
    - 土曜日
    formats:
      default: "%Y/%m/%d"
      long: "%Y年%m月%d日(%a)"
      short: "%m/%d"
      short_year_and_month: "%Y年%m月"
    month_names:
    -
    - 1月
    - 2月
    - 3月
    - 4月
    - 5月
    - 6月
    - 7月
    - 8月
    - 9月
    - 10月
    - 11月
    - 12月
    order:
    - :year
    - :month
    - :day
  datetime:
    distance_in_words:
      about_x_hours:
        one: 約1時間
        other: 約%{count}時間
      about_x_months:
        one: 約1ヶ月
        other: 約%{count}ヶ月
      about_x_years:
        one: 約1年
        other: 約%{count}年
      almost_x_years:
        one: 1年弱
        other: "%{count}年弱"
      half_a_minute: 30秒前後
      less_than_x_minutes:
        one: 1分以内
        other: "%{count}分未満"
      less_than_x_seconds:
        one: 1秒以内
        other: "%{count}秒未満"
      over_x_years:
        one: 1年以上
        other: "%{count}年以上"
      x_days:
        one: 1日
        other: "%{count}日"
      x_minutes:
        one: 1分
        other: "%{count}分"
      x_months:
        one: 1ヶ月
        other: "%{count}ヶ月"
      x_years:
        one: 1年
        other: "%{count}年"
      x_seconds:
        one: 1秒
        other: "%{count}秒"
    prompts:
      day: 日
      hour: 時
      minute: 分
      month: 月
      second: 秒
      year: 年
  errors:
    format: "%{attribute}%{message}"
    messages:
      accepted: を受諾してください
      blank: を入力してください
      present: は入力しないでください
      confirmation: と%{attribute}の入力が一致しません
      empty: を入力してください
      equal_to: は%{count}にしてください
      even: は偶数にしてください
      exclusion: は予約されています
      greater_than: は%{count}より大きい値にしてください
      greater_than_or_equal_to: は%{count}以上の値にしてください
      inclusion: は一覧にありません
      invalid: は不正な値です
      less_than: は%{count}より小さい値にしてください
      less_than_or_equal_to: は%{count}以下の値にしてください
      model_invalid: "バリデーションに失敗しました: %{errors}"
      not_a_number: は数値で入力してください
      not_an_integer: は整数で入力してください
      odd: は奇数にしてください
      required: を入力してください
      taken: はすでに存在します
      too_long: は%{count}文字以内で入力してください
      too_short: は%{count}文字以上で入力してください
      wrong_length: は%{count}文字で入力してください
      other_than: は%{count}以外の値にしてください
      mini_magick_processing_error: はアップロードできないです。再確認してください。
      wrong_file_type: ファイルタイプは間違いです。%{type}のファイルをアップロードしてください。
    template:
      body: 次の項目を確認してください
      header:
        one: "%{model}にエラーが発生しました"
        other: "%{model}に%{count}個のエラーが発生しました"
  helpers:
    select:
      prompt: 選択してください
    submit:
      create: 登録する
      submit: 保存する
      update: 更新する
      cancel: キャンセル
  number:
    currency:
      format:
        delimiter: ","
        format: "%n%u"
        precision: 0
        separator: "."
        significant: false
        strip_insignificant_zeros: false
        unit: 円
    format:
      delimiter: ","
      precision: 3
      separator: "."
      significant: false
      strip_insignificant_zeros: false
    human:
      decimal_units:
        format: "%n %u"
        units:
          billion: 十億
          million: 百万
          quadrillion: 千兆
          thousand: 千
          trillion: 兆
          unit: ''
      format:
        delimiter: ''
        precision: 3
        significant: true
        strip_insignificant_zeros: true
      storage_units:
        format: "%n%u"
        units:
          byte: バイト
          gb: GB
          kb: KB
          mb: MB
          tb: TB
    percentage:
      format:
        delimiter: ''
        format: "%n%"
    precision:
      format:
        delimiter: ''
  support:
    array:
      last_word_connector: と
      two_words_connector: と
      words_connector: と
  time:
    am: 午前
    formats:
      default: "%Y/%m/%d %H:%M:%S"
      long: "%Y年%m月%d日(%a) %H時%M分%S秒 %z"
      short: "%y/%m/%d %H:%M"
      simple: "%Y年%m月%d日(%a) %H時%M"
    pm: 午後
  message:
    create: '作成されました'
    update: '更新されました'
    destroy: '削除されました'
    soft_delete: '停止されました'
    restore: '戻しました'
  announcement_mail:
    draft: '作成中メール'
    sent: '送信済みメール'
    all: 'すべて'
  acquire_skills: '獲得スキル'
  announcement: 'アナウンスメント'
  activity: 'アクティビティ'
  back: '戻る'
  chat: 'チャット'
  classroom: 'マイクラス'
  course: 'コース'
  confirm: '本当によろしいですか？'
  logout: 'ログアウト'
  delete: '削除'
  destroy: '削除'
  soft_delete: '利用停止'
  restore: '戻す'
  enrollment: '参加する'
  web_exam: WEBテスト
  change_user_group: '所属を変更する'
  exam:
    no_master_milestones: 'マイルストーンを作成してください'
    index: 'テスト一覧'
    new: '新規テスト'
  edit: '編集'
  index: '一覧'
  goals: 'ゴール一覧'
  goal: 'ゴール'
  project: "プロジェクト"
  detail: '詳細'
  level: 'レベル'
  mail: 'メール'
  step_mail: 'ステップメール'
  manager: 'マネージャー'
  school_manager: "スクールマネージャー"
  teacher: "ティーチャー"
  enrollments: '参加者'
  passed: '合格者'
  skills: 'スキル'
  skill: 'スキル'
  skill_item: 'スキルアイテム'
  school: 'スクール'
  title: 'Edbase [エドベース]'
  courses: 'ビデオ講座'
  tutorials: 'チュートリアル'
  blogs: 'ブログ'
  premium_services: 'プレミアムサービス'
  cut_rate: '割り引き'
  inquiries: 'お問い合わせ'
  your_assessment: 'あなたの評価'
  lessons: 'レッスン'
  milestones: 'マイルストーン'
  milestone: 'マイルストーン'
  question: '質問'
  user: 'ユーザー'
  sample_file: 'サンプルファイル'
  export: 'エクスポート'
  merchant_info: 契約設定
  users:
    created: 'ユーザーを作りました'
    updated: 'ユーザーを編集しました'
    deleted: 'ユーザーを削除しました'
    added: 'ユーザーを追加しました'
    exits: 'メールアドレスはすでに存在します'
    imported: 'ユーザーをインポートしました'
    blank: 'ユーザーを選択してください'
  lists:
    created: 'カテゴリーを作りました'
    updated: 'カテゴリーを編集しました'
    deleted: 'カテゴリーを削除しました'
  user_blocks:
    created: 'ユーザーブロックを作りました'
    updated: 'ユーザーブロックを編集しました'
    deleted: 'ユーザーブロックを削除しました'
    added: 'ユーザーブロックを追加しました'
    imported: 'ユーザーブロックにユーザーをインポートしました'
  materials:
    index: 素材一覧
  show: '詳細'
  material: 素材
  dupplicate: "複製"
  import: "インポート"
  new: '新規作成'
  my_exam: 'マイテスト'
  plan: 'プラン'
  home: 'ホーム'
  email: 'メールアドレス'
  password: 'パスワード'
  logo_text: 'Skillhub [スキルハブ]'
  inquiry: 'お問い合わせ'
  comment: 'コメントを追加…'
  answer: 'アンサー'
  user_goal:
    already_exists: "%{peoples}人はすでに存在しました。"
    imported: "%{peoples}人のユーザーをインポートしました。"
    not_in_school: "%{peoples}人はインポートできませんでした。"
  role:
    trainee: "受講生"
    manager: "マネージャー"
    admin: "アドミン"
  evaluation:
    status:
      unconfirmed: 差し戻し
      confirmed: 承認済
      submited: 申請中
      draft: 下書き
      unregistered: 閲覧
      registered: 未申請
  evaluation_sheet:
    status:
      draft: "下書き"
      registered: "登録済"
  milestone_action:
    index: マイルストーン一覧
    new: 新規マイルストーン
    create: 新規マイルストーン
    edit: 編集マイルストーン
    update: 編集マイルストーン
    show: マイルストーン
  notification: 'お知らせ'
  q:
    test:
      radio: ラジオボタン（単一選択）
      checkbox: チェックボックス（部分点あり）
      multi_checkbox: チェックボックス（全問正解で得点）
      textarea: フリーテキスト（人間が採点）
      textfield: フリーテキスト（自動採点）
      select: セレクトボックス
      inline_select: インラインセレクト
    enquery:
      radio: アンケート用ラジオボタン
      checkbox: アンケート用チェックボックス
      textarea: アンケート用フリーテキスト
      select: アンケート用セレクトボックス
      inline_select: インラインセレクト
      textfield: アンケート用フリーテキスト
      overview: 説明
  enums:
    course:
      type_is:
        free: '[ビデオ]無料'
        sign_in: '[ビデオ]ログインユーザーのみ'
        premium: '[ビデオ]プレミアムサービス'
        text: '[チュートリアル]無料'
        product: '商品'
      level:
        Beginner: ビギナー
        Easy: やさしい
        Normal: ふつう
        Hard: ややむずかしい
        VeryHard: むずかしい
    prompt:
      category:
        grading: "For Grading"
        auto_question: "For Auto Question"
    step_email_item:
      kind:
        after_sign_up: 登録から
      schedule_type:
        hour: 時間
        day: 日
  public: 公開
  private: 非公開
  mails:
    inquiry_notification_mail: '問い合わせ[%{id}]がありました'
    inquiry_comment_notification_mail: '問い合わせのコメント[%{id}]があります'
    question_notification_mail: '[質問]%{title}'
    question_comment_notification_mail: '[質問のコメント]%{question_title}'
    contact_form_notification_mail: 'コンタクトフォーム[%{id}]がありました。'
    course_purchase_success_mail_worker_to_admin: '%{user_name}さんから「%{course_name}」にお申し込みがありました'
    course_purchase_success_mail_worker_to_user: 'お申し込みありがとうございます'
    subscription_incomplete: '【ご対応のお願い】お支払い方法をご確認ください - %{school_name}'
  school_fee:
    not_set: 未契約
    rate: 割り合い％
    subscription: 定額（金額）
  stripe_account:
    status:
      verified: 確認済
      unverified: 未確認
      pending: 審査中
  ai_chat_question:
    context:
      lesson: Lesson
      question: Question
      goal: Goal
  custom_text:
    goal: ゴール
    course: コース
    exam: テスト
    goal_this_week: 今週のゴール
    total_learn_time: 今週の学習
    total_completed_count: 学習完了数
    consecutive_days: 連続日数
    progress_rate: 進捗率
    class_in_progress: 授業中の講座
    no_test_results: まだテスト結果はありません
    recent_tests: 最近受けたテスト
  meeting:
    positive_feedback: 良い点
    improvement_feedback: 改善点
    learnings: 学び
    content:
      exam: 試験対策
      grammar: 文法の質問
      speaking: スピーキング練習
      writing: ライティング添削
      other: その他
    event:
      status:
        pending: 未承認
        approved: 承認済み
        rejected: 承認不可
        canceled: キャンセル
