class Ahoy::Store < Ahoy::DatabaseStore
  def track_visit(data)    
    data[:school_id] = get_school_id
    super
  end
  
  def track_event(data)
    return if data[:properties][:controller].include?("admin")
    data[:referrer] = request.referrer
    data[:ip] = request.ip
    data[:controller_name] = data[:properties]["controller"]
    data[:controller_action] = data[:properties]["action"]
    data[:full_path] = request.path
    data[:school_id] = get_school_id
    data[:session_id] = request.session.id
    super
  end
  private
  def get_school_id
    subdomain = request.host.gsub(".#{Settings.default_domain}", "")
    school = School.where(subdomain: subdomain).or(School.where(domain: request.host)).first
    return School::MAIN_DOMAIN_ID unless school
    return school.id
  end
end

# set to true for JavaScript tracking
Ahoy.api = false

# set to true for geocoding
# we recommend configuring local geocoding first
# see https://github.com/ankane/ahoy#geocoding
Ahoy.geocode = false
