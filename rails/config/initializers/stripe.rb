if Rails.env == "production"
  Rails.configuration.stripe = {
    :publishable_key => ENV['STRIPE_PUBLISHABLE_KEY'],
    :secret_key      => ENV['STRIPE_SECRET_KEY']
  }
else
  Rails.configuration.stripe = {
    :publishable_key => "pk_test_51JTdp5BLyLEisqmg7jI53Eu2dMWbssUIo4Bcs69zI5ZybYLbAVJCwT5IuIYC6I9qzsIAFtyhnH1ReufLLPoBJMbM001cxeSv9D",
    :secret_key      => "sk_test_51JTdp5BLyLEisqmgS1fohEeUZb4hKxMtCAawpaCT2bMMWi7w46VS93e4CdwczjaiDuO4gkcPjBhQI8AiImzKZkoi00D9CslEF3"
  }
end

Stripe.api_key = Rails.configuration.stripe[:secret_key]
