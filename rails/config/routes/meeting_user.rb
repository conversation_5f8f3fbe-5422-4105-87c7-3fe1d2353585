namespace :meeting do
  get '/dashboard', to: 'dashboards#index'
  resources :events, only: [:index, :show, :update] do
    member do
      patch :canceled
      post :rate
      resources :feedback, only: [:new, :create, :edit, :update], param: :feedback_id
    end
  end

  resources :teachers, only: [:index, :show] do
    member do
      resources :bookings, only: [:show, :new, :create], controller: 'teachers/bookings'
    end
  end

  resources :tickets, only: [:index] do
    resources :payment_histories, only: :create

    collection do
      post :payment
      post :create_payment_intent
      post :validate_coupon
    end
  end

  resources :histories, only: [:index]
  resource :setting, only: [:show, :update]
  # resources :feedbacks, only: [:new, :create]
end
