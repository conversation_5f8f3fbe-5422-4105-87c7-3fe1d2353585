namespace :meeting do
  get '/', to: 'dashboards#index'
  resources :work_times, only: [:index, :create, :update, :destroy]
  resources :events, only: [:index, :create, :show, :update, :destroy] do
    member do
      patch :approve
      patch :reject
      resources :feedback, only: :show, param: :feedback_id

      post :notify_zoom_ready
    end
  end
  resources :records, only: [:index]
  resources :tickets, only: [:index]
  resource :setting, only: [:show, :update]

  resources :categories do
    member do
      patch :sort
    end
  end
end
