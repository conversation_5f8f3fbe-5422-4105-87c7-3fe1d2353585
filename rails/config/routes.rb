require "sidekiq/web"

Rails.application.routes.draw do
  get "business/lp"
  resources :company_infos, only: [:show]
  resources :tutorials, only: [:index, :show]
  get :banner_trackings, to: "banner_trackings#click"
  resources :inquiries, only: [:new, :create] do
    collection do
      get :thankyou
    end
  end

  # for api
  mount GrapeSwaggerRails::Engine => "/swagger"
  use_doorkeeper scope: "oauth"
  namespace :api do
    namespace :v1 do
      resources :users
      resources :schools do
        resources :exams do
          post :import, on: :member
        end
        resources :skills do
          resources :skill_items
        end
      end

      namespace :classroom do
        resources :user_goals, only: [:show, :index]
        resources :courses, only: [:show]
        resources :lessons, only: [:show]
        resources :notifications, only: [:index]
        resources :user_exams, only: [:update, :index, :create, :show] do
          resources :wafs, param: :hash do
            resource :result, only: [:create]
            resources :qs, only: [] do
              resource :a, only: :create
            end
          end
        end
      end
    end

    namespace :v2 do
      namespace :admin do
        resources :exams do
          resources :exam_revisions
          resources :exam_generations
        end
      end

      namespace :meeting do
        resources :tickets, only: [:index]
      end

      devise_scope :user do
        get "info", to: "sessions#info"
        post "signin", to: "sessions#create"
        post "signin_fb", to: "sessions#signin_fb"
        post "new_account", to: "sessions#new_account"
        delete "signout", to: "sessions#destroy", as: :destroy_user_session
      end

      resources :trackings, only: [:index] do
        post :update_tracking, on: :member
      end
      resources :activities, only: [:index]
      resources :inquiries
      resources :embed, only: [:create]

      post "/submit_contact_form/:contact_form_id", to: "submit_contact_form#create"

      resources :exams do
        post :join, on: :member
        post :rejoin, on: :member
        post :clear_all, on: :member
        get :my, on: :collection
        get :search, on: :collection
        get :my_search, on: :collection
        get :recommendation, on: :collection
        get :q_score_histories, on: :member
        resources :exam_purchases do
          get :get_coupon, on: :collection
        end
      end
      resources :user_exams, only: [:destroy, :show] do
        resources :qs, only: [:index, :show] do
          get :index_result, on: :collection
          get :show_result, on: :member
        end
      end
      resources :exam_status
      resources :as do
        collection do
          post :ai_grade
        end
      end
      resources :user, only: [:index, :create] do
        delete :delete, on: :collection
        post :update_learn_time_goal_value, on: :collection
      end
      resources :notifications do
        post :mark_readed, on: :member
      end
      resources :subscriptions, only: [:update, :destroy, :show, :destroy] do
        get :check_subscription_status, on: :member
        get :change_plan_list, on: :member
        get :get_target_premium_service, on: :member
      end
      resources :lists do
        get :my, on: :collection
      end
      resources :carts do
        get :cart_counter, on: :collection
      end
      resources :infors, only: [:index, :show] do
        post :readall, on: :collection
        post :unread, on: :member
      end
      resources :banners, only: [:index]
      resources :live_chat_messages, only: [:index, :create] do
        get :participants, on: :collection
      end
      resources :messages, only: [:index, :create, :stop, :clear, :show] do
        post :stop, on: :collection
        post :clear, on: :collection
        get :speech, on: :member
        get :regenerate, on: :member
      end
      resources :chat_threads

      resources :course_tags, only: [:index] do
        get :my, on: :collection
      end
      resources :course_purchases, only: [:show, :update]
      resources :courses do
        resources :course_lessons
        resources :materials
        resources :chapters
        resources :lessons do
          post :learnt, on: :member
          post :revert_learnt, on: :member
        end
        get :my, on: :collection
        resources :course_purchases do
          get :get_coupon, on: :collection
          get :show_by_course, on: :collection
        end
      end
      resources :lessons do
        resources :materials
      end
      resources :enrollments
      resources :questions do
        get :my, on: :collection
        get :my_question_answers, on: :collection
        resources :relative_lists
      end
      resources :comments
      resources :answers
      resources :votes
      resources :reviews
      resources :user_blocks
      resources :school_settings
      resources :user_goals do
        post :save_activity, on: :member
        get :current_goal, on: :collection
      end
      resources :speak_audios, only: [:create]
      resources :menus
      resources :user_onboards
      resources :attachments, only: [:create]
      resources :ai_chat_questions, only: [:index]
      resources :ai_tutor_agents, only: [:index, :show]
      resources :custom_texts, only: [:index]
      resources :stripe_tokens, only: [:show]
      resources :meeting_tickets, only: [:index]
    end
  end

  resources :goal_courses
  resources :blogs, only: [:index, :show]
  authenticate :user, lambda { |u| u.is_admin? } do
    mount Sidekiq::Web => "/sidekiq"
  end
  resources :skill_scores
  resources :serviceworker, only: [:index]
  resources :manifest, only: [:index]

  mount Ckeditor::Engine => "/ckeditor"

  resources :course_filters, only: [:index]
  resources :users, only: [:index] do
    post :stop_impersonating, on: :collection
  end
  resources :master_milestones
  resources :assignments
  resources :newsfeeds
  resources :managers
  resources :as
  resources :user_exams
  resources :answer_items
  resources :qs
  resources :exams
  resources :assessments
  resources :enrollments, except: [:index, :show, :edit]
  resources :lessons
  resources :projects
  resources :teachers
  resources :questions
  resources :school_plans do
    resources :school_plan_subscriptions
  end
  resources :courses, only: [:index, :show] do
    get :filter_count, on: :collection, to: "courses#filter_count"
    resources :lessons, only: [:show]
    get :get_recommendations, on: :member, action: :get_recommendations
    resources :course_purchases do
      post :get_coupon, on: :collection, to: "course_purchases#get_coupon"
    end
  end
  resources :search, only: [:index, :show]
  resources :exams, only: [:index, :show] do
    resources :exam_purchases do
      post :get_coupon, on: :collection, to: "exam_purchases#get_coupon"
    end
    get :preview, on: :member
  end
  resources :goals, only: [:index, :show] do
    get :import, on: :collection
  end
  resources :premium_services do
    resources :subscriptions do
      post :resume, on: :member
      post :stop, on: :member
      post :calculate_purchase_total, on: :collection
    end
    resources :subscriptions do
      post :get_coupon, on: :collection, to: "subscriptions#get_coupon"
      post :save_payment_method, on: :collection, to: "subscriptions#save_payment_method"
    end
  end
  resources :skill_item_managers
  resources :page_customs, path: 'pages'
  resources :page_customs

  resources :previews, only: [:show]

  namespace :purchase do
    resource :cart, controller: 'cart', only: [:show, :update, :destroy] do
      post :add_item, on: :collection
      delete :remove_item, on: :collection
      post 'remove_item', to: 'cart#remove_item', on: :collection
      post :apply_coupon, on: :collection
      delete :remove_coupon, on: :collection
      post 'remove_coupon', to: 'cart#remove_coupon', on: :collection
    end

    resources :orders, only: [:index, :show, :create]

    resources :payments, only: [:create] do
      get :thanks_page, on: :collection
    end
    resource :checkout, controller: 'checkout', only: [:show, :create]
  end

  concern :examable do
    resources :exams do
      resources :managers
      get :reexams, on: :member
      get :comments, on: :member
      post :update_published, on: :member
      get :purchases, on: :member
      get :exam_access_reports, to: "exam_access_reports#show"
      resources :examinees do
        delete :delete_multiple, on: :collection
        get :update_ranking, on: :collection
      end
      resources :user_examinees
      resources :user_exams, only: [] do
        resources :exam_marks
      end
      resources :qs do
        get :edit2, on: :collection
        get :edit_css, on: :collection
        post :update_css, on: :collection
        put :sort
        put :batch, on: :collection
        post :import, on: :collection
        resources :as, :answer_items
        get :link_to_lessons, on: :member
        patch :update_link_to_lessons, on: :member
        post :update_anwers, on: :member
      end
      resources :exam_evaluations
    end
  end

  # for admin

  namespace :admin do
    root to: "schools#index"
    get "as/index"
    get "applications/index"
    resources :company_infos
    resources :inquiries
    resources :applications
    resources :iams do
      get :get_resources, on: :collection
      post :create_iam, on: :collection
    end
    resources :school_plans do
      put :sort, on: :member
      patch :add_school, on: :member
      resources :school_plan_subscriptions do
        post :start, on: :member
        post :stop, on: :member
        post :resume, on: :member
        resources :school_plan_invoices
      end
    end
    resources :merchant_infos
    namespace :merchant_infos do
      resources :schools
    end
    resources :banks, only: :index do
      resources :branches, only: :index
    end
    resources :users do
      resources :user_skills, only: [:index]
      resources :activities, only: :index
      resources :enrollments, only: :index
      resources :questions, only: :index
      resources :user_goals, only: :index do
        resources :milestones, only: :index
      end
      resources :user_exams, only: [:index, :show]
      resources :subscriptions, only: [:index, :show]
    end
    resources :courses, only: :index
    resources :goals, only: :index
    resources :answers
    resources :comments
    resources :announcement_mails
    resources :votes, only: [:create, :show]
    resources :aichats do
      resources :users, except: [:index, :new, :create]
    end
    get 'aichats/users/:id', to: 'aichats/users#show', as: 'chats_user'
    get 'aichats/users/:id/threads/:thread_id', to: 'aichats/users#thread', as: 'chats_user_thread'
    resources :vote_commons, only: [:create, :show]
    resources :test_announcement_mails, only: :create
    resources :chat_notifications, only: :index
    resources :notifications, only: :index
    resources :subscription_plans

    resources :chat_rooms, only: :show do
      resources :chats
    end

    resources :branches
    resources :sales_managements
    resources :vimeo_accounts

    resources :schools, concerns: :examable do
      get :search, on: :collection
      get :list_courses, on: :collection, to: "schools#list_courses"
      get :list_exams, on: :collection, to: "schools#list_exams"
      get :survey, on: :member
      resources :revisions
      resources :aichats do
        resources :users, except: [:index, :new, :create]
      end
      patch :aichats, controller: :aichats, action: :update
      resources :ai_model_apis, only: [:index, :create]
      resources :ai_tutor_agents, path: 'agents' do
        patch :set_default, on: :member
        resources :ai_tutor_prompts, path: 'prompts' do
          patch :update_content, on: :collection
          post :playground_chat, on: :collection
          get :debug_info, on: :collection
          delete :clear_debug, on: :collection
        end
        resources :ai_tutor_tools, path: 'tools' do
          patch :toggle_enabled, on: :member
          patch :update_when_to_use, on: :member
          get :rag_config, on: :collection
        end
        resources :ai_tutor_sources, path: 'sources' do
          post :bulk_destroy, on: :collection
          post :bulk_toggle_enabled, on: :collection
          post :train, on: :collection
          post :recrawl, on: :member
        end
        resource :ai_tutor_rag_config, path: 'rag_config', only: [:show, :edit, :update]
      end

      get 'aichats/users/:id', to: 'aichats/users#show', as: 'chats_user'
      get 'aichats/users/:id/threads/:thread_id', to: 'aichats/users#thread', as: 'chats_user_thread'
      resource :school_onboarding do
        get :basic, to: "school_onboarding#basic"
        patch :basic, to: "school_onboarding#patch_basic"
        get :survey_1, to: "school_onboarding#survey_1"
        patch :survey_1, to: "school_onboarding#patch_survey_1"
        get :survey_2, to: "school_onboarding#survey_2"
        patch :survey_2, to: "school_onboarding#patch_survey_2"
        get :progress, to: "school_onboarding#progress"
      end
      resources :test_announcement_mails, only: :create
      resources :pickup_resources do
        post :update_order, on: :collection
      end
      resources :banks, only: :index do
        resources :branches, only: :index
      end
      resources :catalogs do
        put :sort, on: :collection
        resources :course_catalogs do
          put :sort, on: :collection
        end
      end
      resources :school_plans, module: :schools do
        resources :school_plan_subscriptions do
          post :stop, on: :member
        end
      end

      resources :iams do
        get :get_resources, on: :collection
        post :create_iam, on: :collection
      end
      resources :plans
      resources :questions do
        get :search, on: :collection
      end
      resources :sales_managements, module: :schools do
        get :search, on: :collection
      end
      get :access_reports, to: "access_reports#show"

      resources :comments
      resource :billings
      post :restore, on: :member
      post "stripe_accounts/save_bank_account", "stripe_account#save_bank_account", on: :member
      resources :answers
      resource :stripe_accounts
      namespace :stripe_accounts do
        resources :payouts
      end
      resources :images do
        post :multi_destroy, on: :collection
      end
      resources :activities
      resources :premium_services do
        get :life_time, on: :member
        get :course_link, on: :member
        get :price_setting, on: :member
        put :sort, on: :member
        resources :subscriptions do
          post :complete_installment, on: :member
          post :stop, on: :member
          post :a_stop, on: :member
          post :a_resume, on: :member
        end
        resources :price_plans, only: [:create, :update, :destroy, :index]
        resources :premium_service_courses do
          put :sort, on: :member
        end
        resources :premium_service_goals do
          put :sort, on: :member
        end
        resources :premium_service_infos do
          put :sort, on: :member
        end
      end
      resources :announcement_mails
      resources :affiliates
      resources :managers
      resources :coupons
      resources :block_emails
      resources :block_email_users, only: [:index]
      resources :mail_sender_settings, except: :show
      resources :stripes, only: [:edit, :update]
      resources :exam_done_scores, only: [:update]
      resources :exam_average_scores, only: [:update]
      resources :school_users, only: [:update]
      resources :tutorials
      resources :partners, only: [:index, :create] do
        delete :remove, on: :collection
      end
      resources :banners
      resources :infors do
        get :search, on: :collection
      end
      resources :blogs do
        get :blog_access_reports, to: "blog_access_reports#show"
      end

      resources :answer_items, only: [] do
        resources :as, only: :index
      end

      resources :school_exam do
        resources :managers
      end
      resources :contact_forms do
        patch "/update_user_contacts/:user_contact_id", to: "contact_forms#update_user_contacts"
        resources :user_contacts, only: [:show, :destroy]
        resources :contact_form_mails
        get :form, on: :member
        post :save_form, on: :member, action: :save_form
        get :confirmation, on: :member
        post :save_confirmation, on: :member, action: :save_confirmation
        get :thank, on: :member
        post :save_thank, on: :member, action: :save_thank
        get :user_mail, on: :member
        post :save_user_mail, on: :member, action: :save_user_mail
        get :manager_mail, on: :member
        post :save_manager_mail, on: :member, action: :save_manager_mail
      end

      resources :user_exams, only: [:destroy]
      resources :user_goals do
        resources :user_skills, only: [:index]
        resources :personal_assignments, :user_exams, :activities
        resources :milestones do
          resources :personal_assignments, :personal_exams
          resources :skill_item_managers do
            resources :gradings
          end
          patch :complete
          post :complete_schedule, on: :collection
          member do
            put :course_sort
            put :exam_sort
          end
        end
        resources :user_skills, only: [:index]
        resources :activities, only: :index
        resources :user_exams, only: [:index, :show]
        resources :questions
      end

      resources :lessons, only: [] do
        resources :materials
      end

      resources :courses do
        member do
          post :import
          put :sort
          post :import_info
          get :course_lessons
        end
        get :all, on: :collection
        post :copy_from_other_school, on: :collection
        get :questions, on: :collection
        get :introduce, on: :member
        post :save_introduce, on: :member, action: :save_introduce
        get :page_design, on: :member
        post :save_page_design, on: :member, action: :save_page_design
        get :assumed_learner, on: :member
        post :save_assumed_learner, on: :member, action: :save_assumed_learner
        get :product_recommend, on: :member
        post :save_product_recommend, on: :member, action: :save_product_recommend
        get :pricing, on: :member
        post :save_pricing, on: :member, action: :save_pricing
        get :setting, on: :member
        post :save_setting, on: :member, action: :save_setting
        get :category, on: :member
        post :save_category, on: :member, action: :save_category
        get :publishing, on: :member
        post :save_publishing, on: :member, action: :save_publishing
        post :copy, on: :member
        member do
          put :course_sort
        end
        resources :chapters do
          put :sort, on: :member
        end
        resources :materials
        resources :reviews
        resources :announcement_mails
        resources :managers
        resources :questions
        resources :course_purchases
        resources :teacher_courses do
          put :sort, on: :member
        end
        get :purchases, on: :member
        get :course_access_reports, to: "course_access_reports#show"
        resources :lessons do
          put :sort, on: :member
          post :save_ai_chat_question, on: :member
          post :remove_ai_chat_question, on: :member
          get :show_modal_ai_chat_question, on: :member
          resources :lesson_exams, only: [:create, :new]
          resources :exams
          get :classroom_preview, on: :member
          post :copy, on: :member
          post :link_to_exam, on: :member
          post :update_published, on: :member
          get :check_vimeo, on: :collection
          post :multi_publish, on: :collection
          post :multi_unpublish, on: :collection
          post :multi_delete, on: :collection
          post :multi_chapter_edit, on: :collection
          post :generate_vector, on: :member
          post :generate_vector_rails, on: :member
          member do
            delete :delete_material
          end
        end
        resources :lessons, :skills, :enrollments do
          collection do
            post :import
            post :vimeo
          end
          put :update_chapter, on: :member
          resources :skill_items
        end
        resources :exams do
          resources :examinees
          resources :user_exams do
            resources :exam_marks
          end
          resources :qs do
            get :edit2, on: :collection
            post :import, on: :collection
            put :sort
            put :batch, on: :collection
            resources :as, :answer_items
          end
        end
      end

      resources :projects do
        resources :goals, :add_goals
        resources :managers
        resources :announcement_mails
        scope module: :p do
          resources :users, :goals do
            resources :user_skills, only: [:index]
            collection do
              post :import
            end
          end
        end
      end

      resources :users do
        post :impersonate, on: :member
        post :restore, on: :member
        resources :user_skills, only: [:index]
        resources :enrollments, only: [:index, :show]
        resources :user_goals, only: :index do
          resources :milestones, only: :index
        end
        resources :activities, only: :index
        resources :user_exams, only: [:index, :show]
        get :questions, to: "user_questions#index"
        # get :enrollments, to: "user_enrollments#index"
        get :iams, to: "user_iams#index"
        get :announcement_mails, to: "user_announcement_mails#index"
        get :onboarding, on: :member
        post :update_user_block, on: :member
        delete :delete_multiple, on: :collection
        resources :course_users, only: [:create, :destroy]
        resources :catalog_users, only: [:create, :destroy]
        resources :user_trackings, only: [:index, :show]
        resources :subscriptions, only: [:index, :show]
        resources :ai_chats, only: [:index, :show] do
          collection do
            post :update_daily_limit
          end
        end
        resources :medan_bonus_tickets, only: [:index, :create]
        get :deletion, on: :member
      end

      resources :goals, concerns: :examable do
        resources :add_users do
          collection do
            post "create_batch"
          end
        end
        resources :user_goals do
          resources :assignments, :assessments
        end
        resources :user_goals
        resources :materials

        resources :announcement_mails, :questions, :managers, :activities
        resources :skills do
          resources :skill_items
        end

        resources :master_milestones, concerns: :examable do
          resources :assignments, :assessments
          collection do
            delete "destroy_all"
            post :complete_schedule
          end
          member do
            put :course_sort
            put :exam_sort
          end
          resources :master_milestone_exams, only: [:new, :create]
        end

        resources :milestones, :newsfeeds, :chat_rooms
        resources :enrollments do
          collection do
            post :import
          end
        end
        post :import, on: :member
        resources :assignments
        resources :courses do
          member do
            post :import
          end
          resources :lessons, concerns: :examable do
            post :vimeo, on: :collection
            resources :skill_items
            put :sort, on: :member

            resources :lesson_exams, only: [:create, :new]
          end
          resources :managers
        end

        resources :add_users
        resources :master_milestones, :user_goals do
          resources :assignments, :assessments
        end
        resources :user_goals do
          resources :final_assessments do
            collection do
              get "edit_all"
              post "update_all"
              post "delete_all"
            end
          end
        end
        resources :user_assignments do
          resources :assessments do
            collection do
              get "edit_assignment"
              post "update_assignment"
              post "delete_assignment"
            end
          end
        end
        resources :milestones, :master_milestones do
          resources :skill_item_managers do
            resources :gradings
          end
        end
        resources :skill_item_managers do
          resources :gradings
        end
      end

      resources :notifications
      resources :inquiries
      resources :page_customs
      resources :school_design_uis do
        post :new_editable_group, on: :collection
        delete :remove_editable_group, on: :collection
        post :new_editable_section, on: :collection
        delete :remove_editable_section, on: :collection
        post :check_page_custom, on: :collection
        patch :check_page_custom, on: :collection
      end
      resources :classroom_designs do
        get :menu, on: :collection
        post :menu, on: :collection, action: :save_menu
      end
      resources :school_designs do
        get :design_images, on: :collection
        get :design_images, on: :member
        get :menu, on: :collection
        get :search, on: :collection
        post :menu, on: :collection, action: :save_menu
        get :header, on: :collection
        post :header, on: :collection, action: :save_header
        get :general, on: :collection
        post :general, on: :collection, action: :save_general
        post :active, on: :member
        post :copy, on: :member
        get :page_main, on: :member
        post :page_main, on: :member, action: :save_page_main
        get :page_exam_list, on: :member
        post :page_exam_list, on: :member, action: :save_page_exam_list
        get :page_exam_show, on: :member
        post :page_exam_show, on: :member, action: :save_page_exam_show
        get :page_course_list, on: :member
        post :page_course_list, on: :member, action: :save_page_course_list
        get :page_teacher_list, on: :member
        post :page_teacher_list, on: :member, action: :save_page_teacher_list
        get :page_course_show, on: :member
        post :page_course_show, on: :member, action: :save_page_course_show
        get :page_lesson_show, on: :member
        post :page_lesson_show, on: :member, action: :save_page_lesson_show
        get :page_blog_list, on: :member
        post :page_blog_list, on: :member, action: :save_page_blog_list
        get :page_blog_show, on: :member
        post :page_blog_show, on: :member, action: :save_page_blog_show
        get :page_premium_list, on: :member
        post :page_premium_list, on: :member, action: :save_page_premium_list
        get :page_premium_show, on: :member
        post :page_premium_show, on: :member, action: :save_page_premium_show
        get :page_inquiry, on: :member
        post :page_inquiry, on: :member, action: :save_page_inquiry
        get :page_inquiry_thank, on: :member
        post :page_inquiry_thank, on: :member, action: :save_page_inquiry_thank
        get :page_teacher_show, on: :member
        post :page_teacher_show, on: :member, action: :save_page_teacher_show
        get :page_review_show, on: :member
        post :page_review_show, on: :member, action: :save_page_review_show
        get :page_default_page_template, on: :member
        post :page_default_page_template, on: :member, action: :save_page_default_page_template
        get :page_default_css, on: :member
        post :page_default_css, on: :member, action: :save_page_default_css
        get :page_default_footer, on: :member
        post :page_default_footer, on: :member, action: :save_page_default_footer
        get :page_default_header, on: :member
        post :page_default_header, on: :member, action: :save_page_default_header
        get :page_question_list, on: :member
        post :page_question_list, on: :member, action: :save_page_question_list
        get :page_custom_page_list, on: :member
        post :page_custom_page_list, on: :member, action: :save_page_custom_page_list
        get :page_question_show, on: :member
        post :page_question_show, on: :member, action: :save_page_question_show
        get :page_school_plan_list, on: :member
        post :page_school_plan_list, on: :member, action: :save_page_school_plan_list
        get :page_school_plan_show, on: :member
        post :page_school_plan_show, on: :member, action: :save_page_school_plan_show
        get :page_custom_page_sign_up, on: :member
        post :page_custom_page_sign_up, on: :member, action: :save_page_custom_page_sign_up
        get :page_custom_page_sign_in, on: :member
        post :page_custom_page_sign_in, on: :member, action: :save_page_custom_page_sign_in
        get :page_custom_page_course_purchases_new, on: :member
        post :page_custom_page_course_purchases_new, on: :member, action: :save_page_custom_page_course_purchases_new
        get :page_custom_page_subscriptions_new, on: :member
        post :page_custom_page_subscriptions_new, on: :member, action: :save_page_custom_page_subscriptions_new
        patch :update_basic, on: :member, action: :update_basic
        # Purchase
        get :page_purchase_cart, on: :member
        post :page_purchase_cart, on: :member, action: :save_page_purchase_cart
        get :page_purchase_checkout, on: :member
        post :page_purchase_checkout, on: :member, action: :save_page_purchase_checkout
        get :page_purchase_thank, on: :member
        post :page_purchase_thank, on: :member, action: :save_page_purchase_thank
        resources :page_templates
      end
      resources :user_blocks, concerns: :examable do
        resources :user_block_users do
          post :create_multiple, on: :collection
        end
      end
      resources :skills do
        resources :skill_items
      end
      resources :lists do
        collection do
          post :reorder
        end
      end
      resources :signup_email_templates
      resources :user_groups do
        get :copy, on: :member
        get :cron_time, on: :member
        post :update_status, on: :member
        post :send_mail, on: :member
      end

      resources :step_emails do
        get :items, on: :member

        resources :step_email_items, only: :show
      end

      resources :terms
      resources :teachers do
        draw :meeting_admin
      end
      resources :user_onboards do
        resources :user_onboard_courses do
          put :sort, on: :member
        end
        post :active, on: :member
        get :preview, on: :member
        resources :contact_forms
      end

      resources :prompts do
        post :copy, on: :member
      end

      resources :ai_chat_questions do
        put :sort, on: :member
      end

      resources :impersonate_histories, only: :index
      resources :custom_texts
      resources :meeting_tickets, only: [:index, :create, :update, :destroy]
      resources :meeting_coupons, only: [:index, :create, :update, :destroy]
      resource :meeting_base_minutes, only: [:update]
    end

    resources :activities
  end

  #################################
  ######## classroom #############
  get "/classroom/", to: "classroom#index"
  namespace :classroom do
    resources :exams, only: [:show]

    draw :meeting_user
  end

  get '/classroom/*other', to: "classroom#index"

  # Embed
  get '/embed/', to: "embed#index"
  get '/embed/*embed_params', to: "embed#index"

  # for no longin users
  resources :courses, only: [:index, :show] do
    resources :enrollments, only: [:new, :create] do
      get :create_get, on: :collection
    end

    resources :reviews, only: :index
  end

  resources :schools, only: [:show] do
    resources :courses, only: [:index, :show] do
      resources :enrollments, only: [:new, :create] do
        get :create_get, on: :collection
      end
    end
  end
  post "/submit_contact_form/:contact_form_id", to: "submit_contact_form#create"
  resources :submit_contact_form do
    get :confirm, on: :collection
  end
  resources :pictures_simplemde
  resources :pictures
  resources :tracking_mails, only: :show
  resource :guests, only: [:create, :index, :show]
  get "block_email", to: "block_emails#create"

  devise_for :users, :controllers => {
    sessions: :sessions,
    registrations: :registrations,
    confirmations: :confirmations,
    omniauth_callbacks: "omniauth_callbacks"
  }
  devise_scope :user do
    get "/users/sign_out" => "devise/sessions#destroy"
  end

  post "stripe/webhook", to: "stripe#webhook"

  if Rails.env.development?
    mount LetterOpenerWeb::Engine, at: "/letter_opener"
  end

  get "home/index"
  root to: "home#index"
  mount ActionCable.server => '/cable'
  # For details on the DSL available within this file, see http://guides.rubyonrails.org/routing.html
end
