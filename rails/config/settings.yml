<% if ENV["RAILS_ENV"] == "development" %>
  <% ENV["AWS_ACCESS_KEY_ID"] = ENV["DEV_AWS_ACCESS_KEY_ID"] || ENV["AWS_ACCESS_KEY_ID"] %>
  <% ENV["AWS_SECRET_ACCESS_KEY"] = ENV["DEV_AWS_SECRET_ACCESS_KEY"] || ENV["AWS_SECRET_ACCESS_KEY"] %>
<% end %>
aws:
  access_key_id: <%= ENV["AWS_ACCESS_KEY_ID"] %>
  secret_access_key: <%= ENV["AWS_SECRET_ACCESS_KEY"] %>

vimeo:
  token: acbe0ef5ad3d1ab190828471bb29e629
  version:
    Accept: "application/vnd.vimeo.*+json;version=3.2"

paginations:
  default_perpage: 30
  evaluation_sheets: 10
  evaluations: 100
  materials: 10
  announcement_mails: 20
  exam: 50
  default_perpage_new: 50

datetime:
  date: "%Y/%m/%d"
  datetime: "%Y/%m/%d %H:%M:%S"
  month_date_jp: "%m月%d日"
  date_jp: "%Y年%m月%d日"

max_length:
  name: 100
  description: 1000
  body: 50000

upload_type:
  image: "image/*"
  excel: ".csv, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, .ods, .xls, .xlsx"

waf:
  secret_key: <%= ENV["AWS_WAF_SECRET_KEY"] %>
  cost: 3
  salt_format: "%y%m%d"

exam:
  min_value:
    pass_score: 0
    difficult_rate: 0
  max_value:
    pass_score: 100
    difficult_rate: 5
  public_types:
    - public
    - school
    - limit

goal:
  max_value:
    level: 5
    passing_score: 100
  min_value:
    level: 1
    passing_score: 1

default_domain: <%= ENV["DEFAULT_DOMAIN"] || "skillhub.jp" %>
global_resources:
  - notifications

mailer:
  rate: 150
  max_bulk: 50
  config_set: <%= ENV["SNS_CONFIG_SET"] %>
  sqs: <%= ENV["SQS_SNS_NOTIFICATION_QUEUE"] %>

api:
  domain: <%= ENV["API_DOMAIN"] %>
  subdomain: <%= ENV["API_SUBDOMAIN"] %>
# 本社、KK、山ラボのIP：*************","**************","**************
# framgiaIP："************","*************","**************"
whitelisted_ips: ["*************","**************","**************","************","*************","**************"]

stripe_fee: 3.6
tax: 0.1 # 10%
