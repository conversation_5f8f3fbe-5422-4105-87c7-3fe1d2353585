ruby "~> 3.1.2"

source "https://rubygems.org"

git_source(:github) do |repo_name|
  repo_name = "#{repo_name}/#{repo_name}" unless repo_name.include?("/")
  "https://github.com/#{repo_name}.git"
end

# Bundle edge Rails instead: gem "rails", github: "rails/rails"
gem "rails", "~> *******"
# Use mysql as the database for Active Record
gem 'mysql2', '~> 0.5'
# Use the Puma web server [https://github.com/puma/puma]
gem "puma", "~> 5.0"
# Use SCSS for stylesheets
# Use JavaScript with ESM import maps [https://github.com/rails/importmap-rails]
gem "importmap-rails"
gem "i18n", "1.8.5"
# Hotwire's modest JavaScript framework [https://stimulus.hotwired.dev]
gem "stimulus-rails"
gem "select2-rails"
# Hotwire's Sp-like page accelerator [https://turbo.hotwired.dev]
gem "turbo-rails"

# Use Uglifier as compressor for JavaScript assets
gem "uglifier", ">= 1.3.0"
# Use CoffeeScript for .coffee assets and views
gem "coffee-rails", "~> 5.0.0"
# See https://github.com/rails/execjs#readme for more supported runtimes
# gem "therubyracer", platforms: :ruby
# gem "mini_racer", platforms: :ruby
# gem "libv8", "7.3.492.27.1"
# gem "mini_racer","0.2.9", platforms: :ruby

# Build JSON APIs with ease. Read more: https://github.com/rails/jbuilder
gem "jbuilder"

# Use Redis adapter to run Action Cable in production
gem "redis", "~> 4.0"

gem "redis-rails", "~> 5"
gem "browser"
gem "acts-as-taggable-on"
gem "validates_timeliness", "~> 4.0"

### New gem settings ###
gem "devise"
gem "carrierwave"
gem "mini_magick"
gem "fog-aws"
gem "cancancan"
gem "RedCloth"
gem "ranked-model"
gem "sidekiq", "~> 5.2.9"
gem "roo"
gem "roo-xls"
gem "rails_autolink"
gem "kaminari"
gem 'ckeditor', '~> 5.1'
gem "omniauth"
gem "omniauth-facebook"
gem "omniauth-twitter"
gem "dotenv-rails"
gem "font-awesome-sass"
gem "truncate_html"
gem "actionpack-page_caching"
gem "draper"
gem "draper-cancancan"
gem "httparty"
gem "vimeo_me2", :git => "https://github.com/bo-oz/vimeo_me2.git"
gem "config"
gem "rubyzip", ">= 1.2.1"
gem "axlsx", git: "https://github.com/randym/axlsx.git", ref: "776037c0fc799bb09da8c9ea47980bd3bf296874"
gem "caxlsx_rails"
gem "aws-sdk-s3", require: false
gem "aws-sdk", "~> 3"
gem "aws-sdk-core"
gem "aws-ses", require: "aws/ses"
gem "exception_notification", git: "https://github.com/smartinez87/exception_notification.git"
gem "slack-notifier"
gem "whenever", require: false
gem "validates_overlap"
gem "aasm"
gem "momentjs-rails"
gem "bootstrap-daterangepicker-rails"
gem "bootsnap", require: false
gem "mechanize" # Web crawling
gem "rack-rewrite"
gem "highcharts-rails"
gem "ransack"
gem "rack-cors", require: "rack/cors"
gem "nokogiri", ">= 1.8.1"
gem "jwt"
gem "anycable-rails"
gem "sidekiq-status"
gem "simple_form"
gem "doorkeeper"
gem "rails-file-icons"
gem "lograge"
gem "enum_help"
gem "omniauth-stripe-connect"
gem "gon"
gem "stripe"
gem "natto"
gem "zengin_code", require: false
gem "mojinizer"
gem "swagger-docs"
# grapeは使用しないが、中にswagger-uiのassets一式が入っているので、viewに利用
gem "grape-swagger-rails"
gem "simplemde-rails"
gem "redcarpet"
gem "rouge-rails"
gem "remotipart", "~> 1.2"
gem "active_model_serializers", "~> 0.10.0"
gem "ruby-openai", "7.1"
gem "tiktoken_ruby"
gem "pdf-reader"
gem "docx"

gem "groupdate" # optional
gem 'apexcharts', '~> 0.2.0'
gem "koala"
gem "ancestry"
gem "recaptcha"
gem 'fugit'
gem 'liquid'
gem 'liquid-c'
gem "paranoia", "~> 2.2"
gem 'ahoy_matey'
gem 'jquery-datetimepicker-rails'
gem 'gemoji'
gem "sprockets-rails"
gem "slim-rails"
gem 'impressionist'
gem 'unicorn-worker-killer'
gem 'systemu', '~> 2.6', '>= 2.6.5'
gem 'nkf'
# gem 'datadog', require: 'datadog/auto_instrument'
gem 'puma_worker_killer'
gem 'pretender'

group :development, :test do
  # Call "byebug" anywhere in the code to stop execution and get a debugger console
  gem "byebug", platform: :mri
  gem "rspec-rails"
  gem "pry-rails"
  gem "pry-doc"
  gem "pry-stack_explorer"
  gem 'letter_opener_web'
  gem "nyan-cat-formatter"
end

group :test do
  gem "rspec_junit_formatter"
  gem "ffaker"
  gem "database_cleaner"
  gem "launchy"
  gem "selenium-webdriver"
  gem "rails-controller-testing"
  gem "rspec-retry"
  gem "fuubar"
  gem "simplecov", require: false
  # gem "shoulda-matchers", "~> 3.1"
  # gem "poltergeist", github: "teampoltergeist/poltergeist"
  # gem "phantomjs", require: "phantomjs/poltergeist"
end

group :development do
  # Access an IRB console on exception pages or by using <%= console %> anywhere in the code.
  gem "web-console", ">= 3.3.0"
  # Spring speeds up development by keeping your application running in the background. Read more: https://github.com/rails/spring
  gem "spring"
  gem "rails-footnotes", git: "https://github.com/BuiDuySon/rails-footnotes.git"
  gem "better_errors"
  gem "binding_of_caller"

  gem "capistrano", "~> 3.5.0"
  gem "capistrano-rails"
  gem "capistrano-rbenv-install"
  gem "capistrano-safe-deploy-to", "~> 1.1.1"
  gem "capistrano-rbenv"
  gem "capistrano-bundler"
  gem "capistrano-sidekiq"
  gem "capistrano3-unicorn"
  gem "capistrano-database-yml", "~> 1.0.0"
  gem "capistrano-env-config"
  gem "pry-byebug"
  # gem "ruby-debug-ide"
  # gem "debase", "0.2.2"
  gem "htmlbeautifier"
  gem 'debase-ruby_core_source'
  gem 'debase', '0.2.5.beta2'
end

group :production do
  gem "unicorn"
  gem "unicorn-rails"
end

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem "tzinfo-data", platforms: [:mingw, :mswin, :x64_mingw, :jruby]
#gem 'memory_profiler', require: true, github: 'SamSaffron/memory_profiler'

# gem "ruby_llm", path: "./../../ruby_llm"
gem "ruby_llm", github: "dat-n/ruby_llm", ref: "046ecf6c02f5a6eb321dc3ae8945eadd395eeb36"
gem "pinecone", "1.2.1"
gem "dry-schema", "~> 1.13.0"
