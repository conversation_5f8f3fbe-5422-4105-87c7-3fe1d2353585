require 'test_helper'

class FileExtractionWorkerJapaneseTest < ActiveSupport::TestCase
  def setup
    @worker = FileExtractionWorker.new
  end

  test "fix_japanese_encoding handles various Japanese text" do
    # Test with mixed Japanese and English
    mixed_text = "こんにちは Hello 世界 World"
    result = @worker.send(:fix_japanese_encoding, mixed_text)
    assert_equal mixed_text, result
    assert_equal Encoding::UTF_8, result.encoding
  end

  test "fix_japanese_encoding handles invalid characters" do
    # Test with null bytes and invalid characters
    invalid_text = "こんにちは\x00世界"
    result = @worker.send(:fix_japanese_encoding, invalid_text)
    assert_equal "こんにちは世界", result
    refute_includes result, "\x00"
  end

  test "fix_japanese_encoding handles different encodings" do
    # Test with Shift_JIS encoded text
    japanese_text = "こんにちは世界"
    shift_jis_text = japanese_text.encode('Shift_JIS')
    
    result = @worker.send(:fix_japanese_encoding, shift_jis_text)
    assert_equal Encoding::UTF_8, result.encoding
    assert_includes result, "こんにちは"
  end

  test "extract_text_file handles Japanese encodings" do
    # Create a temporary file with Japanese content in different encodings
    japanese_content = "こんにちは\n世界\nテスト"
    
    # Test UTF-8
    Tempfile.create(['test_utf8', '.txt']) do |file|
      file.write(japanese_content.encode('UTF-8'))
      file.close
      
      result = @worker.send(:extract_text_file, file.path)
      assert_includes result, "こんにちは"
      assert_includes result, "世界"
      assert_includes result, "テスト"
    end
    
    # Test Shift_JIS
    Tempfile.create(['test_sjis', '.txt']) do |file|
      file.write(japanese_content.encode('Shift_JIS'))
      file.close
      
      result = @worker.send(:extract_text_file, file.path)
      assert_includes result, "こんにちは"
      assert_includes result, "世界"
      assert_includes result, "テスト"
    end
  end

  test "extract_text_file handles empty files" do
    Tempfile.create(['empty', '.txt']) do |file|
      file.close
      
      result = @worker.send(:extract_text_file, file.path)
      assert_equal "", result
    end
  end

  test "extract_text_file handles binary files gracefully" do
    # Create a file with binary content
    Tempfile.create(['binary', '.txt']) do |file|
      file.write([0xFF, 0xFE, 0x00, 0x01].pack('C*'))
      file.close
      
      result = @worker.send(:extract_text_file, file.path)
      # Should not raise an error and return some content (even if garbled)
      assert_kind_of String, result
    end
  end

  # Mock test for PDF extraction (since we don't have actual PDF files)
  test "extract_pdf_content handles Japanese text" do
    # This would require actual PDF files with Japanese content
    # For now, we'll test the encoding fix method
    japanese_pdf_text = "PDFから抽出された日本語テキスト\n複数行のテスト"
    
    result = @worker.send(:fix_japanese_encoding, japanese_pdf_text)
    assert_includes result, "PDFから抽出"
    assert_includes result, "日本語テキスト"
    assert_includes result, "複数行のテスト"
  end

  test "extract_docx_as_zip handles Japanese XML content" do
    # Mock XML content that might come from a DOCX file
    xml_content = <<~XML
      <?xml version="1.0" encoding="UTF-8"?>
      <document>
        <w:t>こんにちは</w:t>
        <w:t>世界</w:t>
        <w:t>テスト文書</w:t>
      </document>
    XML
    
    # Test the regex extraction
    text_nodes = xml_content.scan(/<w:t[^>]*>([^<]*)<\/w:t>/).flatten
    content = text_nodes.join(' ')
    
    assert_includes content, "こんにちは"
    assert_includes content, "世界"
    assert_includes content, "テスト文書"
  end

  private

  def create_test_file_with_content(content, encoding = 'UTF-8')
    Tempfile.create(['test', '.txt']) do |file|
      file.write(content.encode(encoding))
      file.close
      yield file.path
    end
  end
end
