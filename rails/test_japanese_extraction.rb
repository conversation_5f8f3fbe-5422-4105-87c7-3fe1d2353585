#!/usr/bin/env ruby
# Simple test script for Japanese text extraction

require_relative 'app/workers/file_extraction_worker'
require 'tempfile'

class TestJapaneseExtraction
  def initialize
    @worker = FileExtractionWorker.new
  end

  def test_fix_japanese_encoding
    puts "Testing fix_japanese_encoding method..."
    
    # Test with mixed Japanese and English
    mixed_text = "こんにちは Hello 世界 World"
    result = @worker.send(:fix_japanese_encoding, mixed_text)
    puts "Mixed text: #{result}"
    puts "Encoding: #{result.encoding}"
    
    # Test with null bytes
    invalid_text = "こんにちは\x00世界"
    result = @worker.send(:fix_japanese_encoding, invalid_text)
    puts "Cleaned text: #{result}"
    puts "Contains null byte: #{result.include?("\x00")}"
    
    puts "✅ fix_japanese_encoding tests passed\n"
  end

  def test_extract_text_file
    puts "Testing extract_text_file method..."
    
    japanese_content = "こんにちは\n世界\nテスト文書\n日本語のファイル"
    
    # Test UTF-8
    Tempfile.create(['test_utf8', '.txt']) do |file|
      file.write(japanese_content.encode('UTF-8'))
      file.close
      
      result = @worker.send(:extract_text_file, file.path)
      puts "UTF-8 extraction: #{result.strip}"
      puts "Contains Japanese: #{result.include?('こんにちは')}"
    end
    
    # Test Shift_JIS
    Tempfile.create(['test_sjis', '.txt']) do |file|
      file.write(japanese_content.encode('Shift_JIS'))
      file.close
      
      result = @worker.send(:extract_text_file, file.path)
      puts "Shift_JIS extraction: #{result.strip}"
      puts "Contains Japanese: #{result.include?('こんにちは')}"
    end
    
    puts "✅ extract_text_file tests passed\n"
  end

  def test_docx_xml_extraction
    puts "Testing DOCX XML extraction..."
    
    xml_content = <<~XML
      <?xml version="1.0" encoding="UTF-8"?>
      <document>
        <w:t>こんにちは</w:t>
        <w:t>世界</w:t>
        <w:t>テスト文書</w:t>
        <w:t>日本語のドキュメント</w:t>
      </document>
    XML
    
    # Test the regex extraction (simulating DOCX extraction)
    text_nodes = xml_content.scan(/<w:t[^>]*>([^<]*)<\/w:t>/).flatten
    content = text_nodes.join(' ')
    result = @worker.send(:fix_japanese_encoding, content)
    
    puts "Extracted content: #{result}"
    puts "Contains all Japanese text: #{result.include?('こんにちは') && result.include?('世界') && result.include?('テスト文書')}"
    
    puts "✅ DOCX XML extraction tests passed\n"
  end

  def test_encoding_detection
    puts "Testing encoding detection..."
    
    # Test different Japanese encodings
    japanese_text = "これは日本語のテストです。漢字、ひらがな、カタカナが含まれています。"
    
    encodings = ['UTF-8', 'Shift_JIS', 'EUC-JP']
    
    encodings.each do |encoding|
      begin
        encoded_text = japanese_text.encode(encoding)
        puts "#{encoding}: #{encoded_text.encoding} - #{encoded_text.bytesize} bytes"
        
        # Test conversion back to UTF-8
        utf8_text = encoded_text.encode('UTF-8')
        puts "  Converted to UTF-8: #{utf8_text == japanese_text}"
      rescue => e
        puts "  Error with #{encoding}: #{e.message}"
      end
    end
    
    puts "✅ Encoding detection tests passed\n"
  end

  def run_all_tests
    puts "🚀 Starting Japanese text extraction tests...\n"
    
    test_fix_japanese_encoding
    test_extract_text_file
    test_docx_xml_extraction
    test_encoding_detection
    
    puts "🎉 All tests completed successfully!"
  end
end

# Run the tests
if __FILE__ == $0
  tester = TestJapaneseExtraction.new
  tester.run_all_tests
end
