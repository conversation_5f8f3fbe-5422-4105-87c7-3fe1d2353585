namespace :meeting_notifications do
  desc "Send daily schedule notifications to teachers and students"
  task send_daily_schedule: :environment do
    today = Date.today

    puts "Starting daily meeting notifications for #{today}"

    # Find all approved events scheduled for today
    todays_events = Meeting::Event.approved
                                 .where("DATE(start_at) = ?", today)
                                 .includes(:user, :teacher => :user)

    puts "Found #{todays_events.count} approved meetings scheduled for today"

    # Group events by teacher
    events_by_teacher = todays_events.group_by(&:teacher)

    # Group events by student
    events_by_student = todays_events.group_by(&:user)

    # Send notifications to teachers
    events_by_teacher.each do |teacher, events|
      next unless teacher&.user.present?

      next if events.empty?

      begin
        MeetingMailer.daily_schedule_to_teacher(teacher, events).deliver_now

        puts "Sent daily schedule to teacher #{teacher.name} (#{events.count} meetings)"
      rescue => e
        puts "Failed to send notification to teacher #{teacher.name}: #{e.message}"
      end
    end

    # Send notifications to students
    events_by_student.each do |student, events|
      # Skip if student has no meetings today
      next if events.empty?

      begin
        MeetingMailer.daily_schedule_to_student(student, events).deliver_now

        puts "Sent daily schedule to student #{student.name} (#{events.count} meetings)"
      rescue => e
        puts "Failed to send notification to student #{student.name}: #{e.message}"
      end
    end

    puts "Completed daily meeting notifications for #{today}"
  end
end